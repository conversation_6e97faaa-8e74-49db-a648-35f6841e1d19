# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-07-10 16:15
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('integrations', '0005_auto_20190702_1023'),
    ]

    operations = [
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=3, null=True)),
                ('dimensions', django.contrib.postgres.fields.jsonb.J<PERSON><PERSON>ield(blank=True, null=True)),
                ('unit_dimensions', models.CharField(blank=True, choices=[('LBS', 'Pounds'), ('OZS', 'Ounces'), ('IN', 'Inches'), ('CM', ' Centimeters')], default='IN', max_length=4, null=True)),
                ('weight', models.IntegerField(blank=True, null=True)),
                ('unit_weight', models.CharField(blank=True, choices=[('LBS', 'Pounds'), ('OZS', 'Ounces'), ('IN', 'Inches'), ('CM', ' Centimeters')], default='LBS', max_length=4, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AlterField(
            model_name='integrationconfig',
            name='external_id',
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='integrationtype',
            name='external_id',
            field=models.CharField(blank=True, max_length=15, null=True),
        ),
        migrations.AlterField(
            model_name='packagetype',
            name='external_id',
            field=models.CharField(blank=True, max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='servicetype',
            name='external_id',
            field=models.CharField(blank=True, max_length=3, null=True),
        ),
        migrations.AddField(
            model_name='package',
            name='type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='integrations.PackageType'),
        ),
    ]

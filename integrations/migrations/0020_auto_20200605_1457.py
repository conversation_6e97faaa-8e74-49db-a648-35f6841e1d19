# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-06-05 19:57
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0007_auto_20190712_1606'),
        ('inventory', '0034_auto_20200504_1505'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core_config', '0007_auto_20200416_1604'),
        ('integrations', '0019_auto_20200402_1537'),
    ]

    operations = [
        migrations.CreateModel(
            name='ModuleIntegrationConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('active_integration', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('current_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
                ('integration_config', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='integrations.IntegrationConfig')),
                ('kong_module', models.ManyToManyField(blank=True, null=True, to='core_config.KongModule')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.RenameField(
            model_name='historicalintegrationparam',
            old_name='core_config',
            new_name='integration_config',
        ),
        migrations.RenameField(
            model_name='integrationparam',
            old_name='config',
            new_name='integration_config',
        ),
    ]

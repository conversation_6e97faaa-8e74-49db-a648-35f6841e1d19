# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-10-23 00:53
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core_config', '0008_auto_20201015_1317'),
        ('inventory', '0036_auto_20200630_1710'),
        ('integrations', '0020_auto_20200605_1457'),
    ]

    operations = [
        migrations.AddField(
            model_name='moduleintegrationconfig',
            name='related_locations',
            field=models.ManyToManyField(blank=True, null=True, related_name='related_locations', to='inventory.Location'),
        ),
        migrations.AlterField(
            model_name='moduleintegrationconfig',
            name='current_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='current_location', to='inventory.Location'),
        ),
        migrations.RemoveField(
            model_name='moduleintegrationconfig',
            name='kong_module',
        ),
        migrations.AddField(
            model_name='moduleintegrationconfig',
            name='kong_module',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core_config.KongModule'),
        ),
    ]

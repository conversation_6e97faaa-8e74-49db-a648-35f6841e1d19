# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-11-12 22:44
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0026_auto_20191028_1129'),
        ('integrations', '0016_auto_20190801_1149'),
    ]

    operations = [
        migrations.AddField(
            model_name='integrationconfig',
            name='default_sku_filter',
            field=models.IntegerField(choices=[(0, '0 - All others'), (1, '1 - Point of Sale (POS) Trade Item'), (2, '2 - Full Case for Transport'), (3, '3 - Reserved'), (4, '4 - Inner Pack Trade Item Grouping for Handling'), (5, '5 - Reserved'), (6, '6 - Unit Load'), (7, '7 - Unit inside Trade Item or component inside a product not intended for individual sale')], default=1),
        ),
        migrations.AddField(
            model_name='integrationconfig',
            name='default_sku_group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.SKUGroup'),
        ),
        migrations.AddField(
            model_name='integrationconfig',
            name='default_sku_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.SKUType'),
        ),
    ]

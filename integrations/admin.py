# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.contrib import admin
from django.apps import apps
from .models import *

# Register your models here.
app = apps.get_app_config('integrations')


class IntegrationTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'active')


class IntegrationGroupAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'active')


class IntegrationConfigAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'username', 'type')


class IntegrationParamAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'external_id', 'group', 'integration_config', 'active')


class ModuleIntegrationConfigAdmin(admin.ModelAdmin):
    list_display = ('id', 'customer', 'current_location', 'kong_module', 'active_integration')
    raw_id_fields = ('current_location',)


admin.site.register(IntegrationType, IntegrationTypeAdmin)
admin.site.register(IntegrationGroup, IntegrationGroupAdmin)
admin.site.register(IntegrationConfig, IntegrationConfigAdmin)
admin.site.register(IntegrationParam, IntegrationParamAdmin)
admin.site.register(ModuleIntegrationConfig, ModuleIntegrationConfigAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

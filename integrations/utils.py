# -*- coding: utf-8 -*-
from __future__ import unicode_literals
import json

import boto3
import logging
from integrations.models import ModuleIntegrationConfig
from main.conf import common as common_configs


def verified_integration_module(**kwargs):
    if kwargs.get("permission_name") and not kwargs.get("kong_module__name"):
        kwargs["kong_module__name"] = kwargs.pop("permission_name")
    object_module = ModuleIntegrationConfig.objects.filter(**kwargs).first()
    if object_module:
        return object_module
    else:
        return False


def aws_lambda_request(lambda_name: str, body: dict, async_request: bool):
    invocation_type = "RequestResponse"
    if async_request:
        invocation_type = "Event"
    session = boto3.Session(region_name=common_configs.AWS_REGION, aws_access_key_id=common_configs.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=common_configs.AWS_SECRET_ACCESS_KEY)
    client = session.client('lambda')

    client.invoke(
        FunctionName=lambda_name,
        InvocationType=invocation_type,
        Payload=json.dumps(body)
    )


def eventbridge_request(source: str, body: dict):
    detail_type = body["detail-type"]
    session = boto3.Session()
    client = session.client("events")
    detail = json.dumps(body)
    entries_body = [
        {
            'Source': source,
            'DetailType': detail_type,
            'Detail': detail
        },
    ]
    print("Entries:", entries_body)
    response = client.put_events(Entries=entries_body)

    if response["ResponseMetadata"]["HTTPStatusCode"] > 300:
        logging.error('failed put event.')
        logging.error('Details:')
        logging.error(json.dumps(body))

    return True



# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from core_config.serializers import KongModuleNestedSerializer
from .models import *
from common.serializers import BaseSerializer, UserNestedSerializer
from customers.serializers import CustomerNestedSerializer
from inventory.serializers import SKUNestedSerializer
from operations.serializers import *
from rest_framework import serializers
from logistics.models import *


class IntegrationTypeSerializer(BaseSerializer):
    class Meta:
        model = IntegrationType
        fields = '__all__'


class IntegrationTypeNestedSerializer(BaseSerializer):
    class Meta:
        model = IntegrationType
        fields = ('id', 'name', 'active')


class IntegrationGroupSerializer(BaseSerializer):
    class Meta:
        model = IntegrationGroup
        fields = '__all__'


class IntegrationGroupNestedSerializer(BaseSerializer):
    class Meta:
        model = IntegrationGroup
        fields = ('id', 'external_id', 'name', 'active')


class IntegrationConfigSerializer(BaseSerializer):
    username = serializers.Char<PERSON><PERSON>(write_only=True)
    password = serializers.Char<PERSON><PERSON>(write_only=True)
    access_key = serializers.Char<PERSON><PERSON>(write_only=True)
    domain_name = serializers.Char<PERSON><PERSON>(write_only=True)
    secret_webhooks = serializers.Char<PERSON>ield(write_only=True)

    class Meta:
        model = IntegrationConfig
        fields = '__all__'


class IntegrationConfigNestedSerializer(BaseSerializer):
    type = IntegrationTypeNestedSerializer(read_only=True)
    customer = CustomerNestedSerializer(read_only=True)

    class Meta:
        model = IntegrationConfig
        fields = ('id', 'description', 'type', 'customer')


class IntegrationParamSerializer(BaseSerializer):
    group_id = serializers.IntegerField(write_only=True)
    group = IntegrationGroupNestedSerializer(read_only=True)
    integration_config_id = serializers.IntegerField(write_only=True)
    integration_config = IntegrationConfigNestedSerializer(read_only=True)

    class Meta:
        model = IntegrationParam
        fields = '__all__'


class IntegrationParamNestedSerializer(BaseSerializer):
    group = IntegrationGroupNestedSerializer(read_only=True)
    config = IntegrationConfigNestedSerializer(read_only=True)

    class Meta:
        model = IntegrationParam
        fields = ('id', 'name', 'external_id', 'description', 'group', 'integration_config')


class ServiceTypeSerializer(BaseSerializer):
    class Meta:
        model = ServiceType
        fields = '__all__'


class ServiceTypeNestedSerializer(BaseSerializer):
    class Meta:
        model = ServiceType
        fields = ('id', 'external_id', 'name')


class PackageTypeSerializer(BaseSerializer):
    service = ServiceTypeNestedSerializer(read_only=True, many=True)

    class Meta:
        model = PackageType
        fields = '__all__'


class PackageTypeNestedSerializer(BaseSerializer):
    service = ServiceTypeSerializer(read_only=True, many=True)

    class Meta:
        model = PackageType
        fields = ('id', 'external_id', 'name', 'service')


class PackageOrderSerializer(BaseSerializer):
    service = ServiceTypeNestedSerializer(read_only=True)
    service_id = serializers.IntegerField(write_only=True)
    type = PackageTypeNestedSerializer(read_only=True)
    type_id = serializers.IntegerField(write_only=True)
    customer_id = serializers.IntegerField(write_only=True)
    customer = CustomerNestedSerializer(read_only=True)

    class Meta:
        model = PackageOrder
        fields = '__all__'


class PackageOrderNestedSerializer(BaseSerializer):
    service = ServiceTypeNestedSerializer(read_only=True)
    type = PackageTypeNestedSerializer(read_only=True)

    class Meta:
        model = PackageOrder
        fields = ('id', 'external_id', 'service', 'type', 'dimensions', 'weight', 'properties')


class InventoryAmountSerializer(serializers.Serializer):
    sku_id = serializers.PrimaryKeyRelatedField(queryset=SKU.objects.all().values_list('id', flat=True))
    amount = serializers.IntegerField()
    location_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all().values_list('id', flat=True))
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all().values_list('id', flat=True),
                                                     required=False)


class SyncConfigsSerializer(serializers.Serializer):
    integration_group_id = serializers.PrimaryKeyRelatedField(
        queryset=IntegrationGroup.objects.all().values_list('id', flat=True), required=False)
    customer_id = serializers.PrimaryKeyRelatedField(
        queryset=Customer.objects.all().values_list('id', flat=True), required=False)


class InvoiceSerializer(serializers.Serializer):
    invoice_param_id = serializers.PrimaryKeyRelatedField(
        queryset=IntegrationParam.objects.filter(group__name="INVOICE").values_list('id', flat=True))
    payment_param_id = serializers.PrimaryKeyRelatedField(
        queryset=IntegrationParam.objects.filter(group__name="PAYMENT MEAN").values_list('id', flat=True))
    cost_center_param_id = serializers.PrimaryKeyRelatedField(
        queryset=IntegrationParam.objects.filter(group__name="COST CENTER").values_list('id', flat=True))
    customer_id = serializers.PrimaryKeyRelatedField(
        queryset=Customer.objects.all().values_list('id', flat=True), required=False)
    shipping_order_id = serializers.PrimaryKeyRelatedField(
        queryset=ShippingOrder.objects.all().values_list('id', flat=True), required=False)


class ModuleIntegrationConfigSerializer(serializers.ModelSerializer):
    customer = CustomerNestedSerializer(read_only=True)
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(), source='customer',
                                                     write_only=True)
    current_location = LocationNestedSerializer(read_only=True)
    current_location_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='current_location',
                                                             write_only=True)
    kong_module_id = serializers.IntegerField(write_only=True)
    kong_module = KongModuleNestedSerializer(read_only=True, many=True)

    type_integration_id = serializers.IntegerField(write_only=True)
    type_integration = IntegrationTypeNestedSerializer(read_only=True)

    class Meta:
        model = ModuleIntegrationConfig
        fields = '__all__'

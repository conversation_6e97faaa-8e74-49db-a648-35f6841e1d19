import coreapi
import coreschema
from rest_framework import schemas


class PackageOrderViewSchema(schemas.AutoSchema):

    def get_manual_fields(self, path, method):
        extra_fields = []
        if method == 'POST':
            extra_fields = [
                coreapi.Field(
                    name="shipping_id",
                    description="Shipping Order Id.",
                    location="query",
                    schema=coreschema.String(),
                    required=True
                ),
                coreapi.Field(
                    name="created",
                    description="Define select related costs (true) or only consult (false) of carriers ship.",
                    location="query",
                    schema=coreschema.String(),
                    required=False
                )
            ]

        manual_fields = super().get_manual_fields(path, method)
        return manual_fields + extra_fields

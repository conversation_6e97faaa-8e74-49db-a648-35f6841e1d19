# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from rest_framework.routers import Default<PERSON><PERSON><PERSON>

from .api_views import *
from django.urls import path
#from django.conf.urls import url

router = DefaultRouter()
router.register(r'integration-types', IntegrationTypeViewSet, 'integration-type')
router.register(r'integration-groups', IntegrationGroupViewSet, 'integration-group')
router.register(r'integration-configs', IntegrationConfigViewSet, 'integration-core_config')
router.register(r'integration-params', IntegrationParamViewSet, 'integration-param')
router.register(r'package-service-types', PackageServiceTypeViewSet, 'package-service-type')
router.register(r'package-types', PackageTypeViewSet, 'package-type')
router.register(r'package-orders', PackageOrderViewSet, 'package-order')
router.register(r'module-config', ModuleIntegrationConfigViewSet, 'module-config')
urlpatterns = router.urls


post_viewset = ShippingOrderView.as_view({
    'post': 'create_shipping_order',
})

get_viewset = ShippingOrderView.as_view({
    'get': 'get_shipping_order',
})

SKU_post_viewset = SKUView.as_view({
    'post': 'create_SKU',
})

SKU_patch_viewset = SKUView.as_view({
    'post': 'update_SKU',
})
Arco_sku_viewset = ArcoIntegrationView.as_view({
    'post': 'sku_event',
})
Arco_purchase_order_viewset = ArcoIntegrationView.as_view({
    'post': 'event_purchase_order',
})

# TODO Migrate this custom integration to lambda function, integrations decoupled


urlpatterns += [
    path('shopify/shipping-order/create/', post_viewset, name='post'),
    path('shopify/shipping-order/get/', get_viewset, name='get'),
    path('shopify/SKU/create/', SKU_post_viewset, name='post'),
    path('shopify/SKU/update/', SKU_patch_viewset, name='post'),
    #path('shopify/inventory/adjust/', SKUAdjustInventy_post_viewset, name='post'),
    #path('siigo/configs/sync/', SyncConfigs_post_viewset, name='post'),
    #path('siigo/invoice/create/', Invoice_post_viewset, name='post'),
    path('arco/sku-event', Arco_sku_viewset, name='post'),
    path('arco/purchase-order-event', Arco_purchase_order_viewset, name='post'),

]

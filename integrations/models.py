# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from core_config.models import KongModule
from operations.models import *


# Create your models here.
class IntegrationType(BaseModel):
    TYPE = (
        ('CONVEYOR', 'Conveyor'),
        ('ECOMMERCE', 'Ecommerce')
    )
    external_id = models.CharField(max_length=15, null=True, blank=True)
    name = models.CharField(max_length=50)
    type = models.CharField(max_length=20, choices=TYPE, default="ECOMMERCE",
                            null=True, blank=True)
    active = models.BooleanField()

    class Meta:
        ordering = ['id']

    def __str__(self):
        return '{}-{}'.format(self.pk, self.name)


class IntegrationGroup(BaseModel):
    external_id = models.CharField(max_length=15, null=True, blank=True, unique=True)
    name = models.CharField(max_length=50)
    active = models.BooleanField(default=True)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.id, self.name)


class IntegrationConfig(BaseModel):
    external_id = models.CharField(max_length=15, null=True, blank=True)
    description = models.CharField(max_length=100, null=True, blank=True)
    username = models.CharField(max_length=60)
    password = models.CharField(max_length=150)
    access_key = models.CharField(max_length=50, null=True, blank=True)
    domain_name = models.CharField(max_length=100, null=True, blank=True)
    secret_webhooks = models.CharField(max_length=100, null=True, blank=True)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    integrator_user = models.ForeignKey(User, null=True, blank=True, on_delete=models.PROTECT)
    type = models.ForeignKey(IntegrationType, on_delete=models.PROTECT)
    default_sku_type = models.ForeignKey(SKUType, null=True, blank=True, on_delete=models.PROTECT)
    default_sku_group = models.ForeignKey(SKUGroup, null=True, blank=True, on_delete=models.PROTECT)
    default_sku_filter = models.IntegerField(choices=SGTIN_FILTER_VALUES, default=1)

    class Meta:
        ordering = ['id']

    def __str__(self):
        return '{}-{}'.format(self.pk, self.username)


class IntegrationParam(BaseModel):
    name = models.CharField(max_length=50)
    external_id = models.CharField(max_length=30, null=True, blank=True)
    description = models.CharField(max_length=100, null=True, blank=True)
    group = models.ForeignKey(IntegrationGroup, on_delete=models.PROTECT)
    integration_config = models.ForeignKey(IntegrationConfig, on_delete=models.PROTECT)
    properties = JSONField(null=True, blank=True, default=dict)
    active = models.BooleanField(default=True)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}-{}'.format(self.integration_config, self.name, self.external_id)


class ServiceType(BaseModel):
    external_id = models.CharField(max_length=3, null=True, blank=True)
    name = models.CharField(max_length=40)
    description = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        ordering = ['external_id']

    def __str__(self):
        return '{}-{}'.format(self.external_id, self.name)


class PackageType(BaseModel):
    external_id = models.CharField(max_length=3, null=True, blank=True)
    name = models.CharField(max_length=40)
    description = models.CharField(max_length=100, null=True, blank=True)
    service = models.ManyToManyField(ServiceType, blank=True, related_name='services')

    class Meta:
        ordering = ['external_id']

    def __str__(self):
        return '{}-{}'.format(self.external_id, self.name)


class PackageOrder(BaseModel):
    UNIT_OF_MEASUREMENT = (
        ('LBS', 'Pounds'),
        ('OZS', 'Ounces'),
        ('IN', 'Inches'),
        ('CM', ' Centimeters')
    )

    external_id = models.CharField(max_length=3, null=True, blank=True, unique=True)
    dimensions = JSONField(null=True, blank=True)
    type = models.ForeignKey(PackageType, on_delete=models.PROTECT)
    service = models.ForeignKey(ServiceType, on_delete=models.PROTECT)
    unit_dimensions = models.CharField(max_length=4, choices=UNIT_OF_MEASUREMENT, default="IN",
                                       null=True, blank=True)
    weight = models.CharField(max_length=4, null=True, blank=True)
    unit_weight = models.CharField(max_length=4, choices=UNIT_OF_MEASUREMENT, default="LBS",
                                   null=True, blank=True)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    properties = JSONField(null=True, blank=True, default=dict)  # {"Address":value}

    class Meta:
        ordering = ['external_id']

    def __str__(self):
        return '{}-{}'.format(self.external_id, self.dimensions)


class ModuleIntegrationConfig(BaseModel):
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    kong_module = models.ForeignKey(KongModule, null=True, blank=True, on_delete=models.PROTECT)
    current_location = models.ForeignKey(Location, related_name='current_location', null=True, blank=True, on_delete=models.PROTECT)
    related_locations = models.ManyToManyField(Location, related_name='related_locations', blank=True)
    integration_config = models.ForeignKey(IntegrationConfig, null=True, blank=True, on_delete=models.PROTECT)
    active_integration = models.BooleanField(default=False)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.customer, self.kong_module)

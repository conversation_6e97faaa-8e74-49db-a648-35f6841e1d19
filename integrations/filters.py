# -*- coding: utf-8 -*-

from __future__ import unicode_literals

from django_filters import rest_framework as filters
from common.filters import BaseFilter
from .models import *
from customers.models import Customer


class IntegrationTypeFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = IntegrationType
        fields = ['external_id', 'name', 'type']


class IntegrationGroupFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = IntegrationGroup
        fields = ['external_id', 'name', 'active']


class IntegrationConfigFilter(BaseFilter, filters.FilterSet):
    customer_id = filters.ModelMultipleChoiceFilter('customer', queryset=Customer.objects.all())

    class Meta:
        model = IntegrationConfig
        fields = ['external_id', 'type__id', 'type__name', 'type__type']


class IntegrationParamFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = IntegrationParam
        fields = ['external_id', 'name', 'group_id', 'group__name', 'integration_config_id',
                  'integration_config__customer_id', 'integration_config__type_id', 'active']


class ServiceTypeFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = ServiceType
        fields = ['external_id', 'name']


class PackageTypeFilter(BaseFilter, filters.FilterSet):
    service_id = filters.ModelMultipleChoiceFilter('service', queryset=ServiceType.objects.all())

    class Meta:
        model = PackageType
        fields = ['external_id', 'name']


class PackageOrderFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = PackageOrder
        fields = ['external_id', 'type_id', 'service_id', 'unit_dimensions', 'unit_weight', 'customer_id']


class ModuleIntegrationConfigFilter(BaseFilter, filters.FilterSet):
    kong_module_id = filters.ModelMultipleChoiceFilter('kong_module', queryset=KongModule.objects.all())

    class Meta:
        model = ModuleIntegrationConfig
        fields = ['customer_id', 'current_location_id', 'kong_module_id', 'active_integration']

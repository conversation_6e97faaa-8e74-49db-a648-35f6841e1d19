# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import transaction, IntegrityError
from django_filters.rest_framework import DjangoFilterBackend
from kombu.utils import json
from rest_framework import viewsets, status
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework.permissions import *
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet
from .filters import *
from .serializers import *
from . import webhooks
from .business_logic import shopify, siigo
from .schemas import PackageOrderViewSchema
from .tasks import package_costs, sync_siigo_configs
from logistics.business_logic import shipping_orders
import sys
import traceback
from django.core.mail import EmailMessage
from main.conf.common import config
from rest_framework.generics import CreateAPIView
from integrations.business_logic.shopify import refresh_inventory
from core_config.models import UserConfig
import logging
import integrations.utils as integration_utils
import integrations.tasks as integration_tasks


def send_error(request, data):
    exc_inf = sys.exc_info()
    email_message = EmailMessage(
        subject='Kong error',
        body=json.dumps(data, indent=4, sort_keys=True) + "\n\nUser = " +
             request.user.username + "\nEndpoint = " + request._request.META["PATH_INFO"] +
             "\n\n" + ("\n".join(traceback.format_exception(*exc_inf))),
        to=[config.get_config('EMAIL_HOST_USER')]
    )
    try:
        email_message.send()
    except Exception as error:
        logging.error(error)


class IntegrationTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
        retrieve:
        Return the given Integration Type.
        list:
        Return a list of all Integrations types.
    """
    queryset = IntegrationType.objects.all()
    serializer_class = IntegrationTypeSerializer
    filter_class = IntegrationTypeFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class IntegrationGroupViewSet(viewsets.ReadOnlyModelViewSet):
    """
        retrieve:
        Return the given Integration Group.
        list:
        Return a list of all Integration Groups.
    """
    queryset = IntegrationGroup.objects.all()
    serializer_class = IntegrationGroupSerializer
    filter_class = IntegrationGroupFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class IntegrationConfigViewSet(viewsets.ModelViewSet):
    """
       retrieve:
       Return the given Integration Config.
       list:
       Return a list of all the existing Integration Config.
       create:
       Create a new Integration Config instance.
       update:
       Update an existing Integration Config instance.
       partial_update:
       Update partial an existing Integration Config instance.
       delete:
       Delete an existing Integration Config instance.
    """
    queryset = IntegrationConfig.objects.all()
    serializer_class = IntegrationConfigSerializer
    filter_class = IntegrationConfigFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class IntegrationParamViewSet(viewsets.ModelViewSet):
    """
       retrieve:
       Return the given Integration Param.
       list:
       Return a list of all the existing Integration Params.
       create:
       Create a new Integration Param instance.
       update:
       Update an existing Integration Param instance.
       partial_update:
       Update partial an existing Integration Param instance.
       delete:
       Delete an existing Integration Param instance.
    """
    queryset = IntegrationParam.objects.all()
    serializer_class = IntegrationParamSerializer
    filter_class = IntegrationParamFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class PackageServiceTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
        retrieve:
        Return the given Package service type.
        list:
        Return a list of all Package service types.
    """
    queryset = ServiceType.objects.all()
    serializer_class = ServiceTypeSerializer
    filter_class = ServiceTypeFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class PackageTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
        retrieve:
        Return the given Package type.
        list:
        Return a list of all Package types.
    """
    queryset = PackageType.objects.all()
    serializer_class = PackageTypeSerializer
    filter_class = PackageTypeFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class PackageOrderViewSet(viewsets.ModelViewSet):
    """
        retrieve:
        Return the given Package order.

        list:
        Return a list of all Package Orders.

        create:
        Create a new Package Order instance.

        update:
        Update an existing Package Order instance.

        partial_update:
        Update partial an Package Order instance.

        delete:
        Delete an existing Package Order instance.
    """
    queryset = PackageOrder.objects.all()
    serializer_class = PackageOrderSerializer
    filter_class = PackageOrderFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'
    schema = PackageOrderViewSchema()

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        try:
            with transaction.atomic():
                # user_config = utils.get_user_config(request.user.pk)
                shipping_id = request.query_params.get('shipping_id')
                created = request.query_params.get('created')

                if created == 'true':
                    package_order = serializer.save()
                    shipping_order = shipping_orders.save_shipping_order(shipping_id, package_order)
                    result = package_costs(shipping_order)
                    if 'Fault' in result:
                        raise IntegrityError
                    else:
                        return Response(data=serializer.data, status=status.HTTP_200_OK)
                else:
                    package_order = serializer.save()
                    shipping_order = shipping_orders.save_shipping_order(shipping_id, package_order)
                    result = package_costs(shipping_order)
                    if 'Fault' in result:
                        raise IntegrityError
                    else:
                        raise IntegrityError

        except IntegrityError:
            transaction.rollback()
            if 'Fault' in result:
                content = {
                    'message': result['Fault']['detail']['Errors']['ErrorDetail']['PrimaryErrorCode']['Description']}
                return Response(data=content, status=status.HTTP_406_NOT_ACCEPTABLE)
            else:
                return Response(data=serializer.data, status=status.HTTP_200_OK)


class ShippingOrderView(ViewSet):
    permission_classes = (AllowAny,)

    def create_shipping_order(self, request, *args):
        domain_name = request.query_params.get('domain_name')
        data = request.stream.read()
        integration_config = IntegrationConfig.objects.get(domain_name=domain_name)
        verified = webhooks.verify_webhook(data, request.META.get('HTTP_X_SHOPIFY_HMAC_SHA256'),
                                           integration_config.secret_webhooks)

        # Create shipping orders since shopify
        decode_data = data.decode('utf-8')
        json_data = json.loads(decode_data)
        if not verified:
            send_error(request=request, data=json_data)
            return Response(status=status.HTTP_403_FORBIDDEN)
        with transaction.atomic():
            result = shopify.create_orders(order=data, integration_config=integration_config)
            if result:
                return Response(status=status.HTTP_201_CREATED)
            else:
                send_error(request=request, data=json_data)
                return Response(status=status.HTTP_202_ACCEPTED)

    def get_shipping_order(self, request, *args):
        domain_name = request.query_params.get('domain_name')
        integration_config = IntegrationConfig.objects.get(domain_name=domain_name)

        with transaction.atomic():
            shopify.get_orders(integration_config=integration_config)


class SKUView(ViewSet):
    permission_classes = (AllowAny,)

    def create_SKU(self, request, *args):
        data = request.stream.read()
        decode_data = data.decode('utf-8')
        json_data = json.loads(decode_data)
        try:
            domain_name = request.query_params.get('domain_name')
            integration_config = IntegrationConfig.objects.get(domain_name=domain_name)
            verified = webhooks.verify_webhook(data, request.META.get('HTTP_X_SHOPIFY_HMAC_SHA256'),
                                               integration_config.secret_webhooks)

            # Create shipping orders since shopify
            if not verified:
                send_error(request=request, data=json_data)
                return Response(status=status.HTTP_403_FORBIDDEN)
            with transaction.atomic():
                result = shopify.create_SKU(skus=json_data, integration_config=integration_config)
                if result:
                    return Response(status=status.HTTP_201_CREATED)
                else:
                    send_error(request=request, data=json_data)
                    return Response(status=status.HTTP_202_ACCEPTED)
        except:
            send_error(request=request, data=json_data)
            return Response(status=status.HTTP_202_ACCEPTED)

    def update_SKU(self, request, *args):
        data = request.stream.read()
        decode_data = data.decode('utf-8')
        json_data = json.loads(decode_data)
        try:
            domain_name = request.query_params.get('domain_name')
            integration_config = IntegrationConfig.objects.get(domain_name=domain_name)
            verified = webhooks.verify_webhook(data, request.META.get('HTTP_X_SHOPIFY_HMAC_SHA256'),
                                               integration_config.secret_webhooks)

            # Create shipping orders since shopify
            if not verified:
                send_error(request=request, data=json_data)
                return Response(status=status.HTTP_403_FORBIDDEN)
            with transaction.atomic():
                result = shopify.update_SKU(skus=json_data, integration_config=integration_config)
                if result:
                    return Response(status=status.HTTP_201_CREATED)
                else:
                    send_error(request=request, data=json_data)
                    return Response(status=status.HTTP_202_ACCEPTED)
        except:
            send_error(request=request, data=json_data)
            return Response(status=status.HTTP_202_ACCEPTED)


class SKUAdjustInventyView(CreateAPIView):
    """
    Sum amount value in shopify inventory
    """
    permission_classes = (IsAuthenticated,)
    serializer_class = InventoryAmountSerializer

    def post(self, request, *args, **kwargs):
        serializer = InventoryAmountSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        sku_id = serializer.validated_data['sku_id']
        amount = serializer.validated_data['amount']
        location_id = serializer.validated_data['location_id']
        user_id = request.user.pk
        if 'customer_id' in serializer.validated_data:
            customer_id = serializer.validated_data['customer_id']
        else:
            customer_id = UserConfig.objects.get(user_id=user_id).customer_id
        integration_config = IntegrationConfig.objects.get(type__name="SHOPIFY", customer_id=customer_id)
        location = Location.objects.get(id=location_id)
        sku = SKU.objects.get(id=sku_id)
        if sku.params and 'shopify_inventory_item_id' in sku.params:
            try:
                refresh_inventory(integration_config=integration_config,
                                  shopify_inventory_item_id=sku.params['shopify_inventory_item_id'],
                                  amount=amount, shopify_location_id=location.external_id)
                return Response(serializer.validated_data, status=status.HTTP_200_OK)
            except Exception:
                return Response({"result": "error response from shopify"}, status=status.HTTP_406_NOT_ACCEPTABLE)
        else:
            return Response({"result": "Sku object without params info"}, status=status.HTTP_400_BAD_REQUEST)


class SyncConfigsView(CreateAPIView):
    """
    Sync Siigo configuration
    """
    permission_classes = (IsAuthenticated,)
    serializer_class = SyncConfigsSerializer

    def post(self, request, *args, **kwargs):
        serializer = SyncConfigsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        integration_group_id = serializer.validated_data.get('integration_group_id', None)
        customer_id = serializer.validated_data.get('customer_id', None)
        user_id = request.user.pk
        if not customer_id:
            customer_id = UserConfig.objects.get(user_id=user_id).customer_id
        integration_config_id = IntegrationConfig.objects.get(type__name="SIIGO", customer_id=customer_id).id
        sync_siigo_configs(integration_config_id=integration_config_id, integration_group_id=integration_group_id,
                           user_id=user_id)
        return Response(serializer.validated_data, status=status.HTTP_200_OK)


class InvoiceView(CreateAPIView):
    """
    Create Siigo invoice
    """
    permission_classes = (IsAuthenticated,)
    serializer_class = InvoiceSerializer

    def post(self, request, *args, **kwargs):
        serializer = InvoiceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        customer_id = serializer.validated_data.setdefault('customer_id')
        invoice_param_id = serializer.validated_data['invoice_param_id']
        payment_param_id = serializer.validated_data['payment_param_id']
        cost_center_param_id = serializer.validated_data['cost_center_param_id']
        shipping_order_id = serializer.validated_data['shipping_order_id']

        user_id = request.user.pk
        if not customer_id:
            customer_id = UserConfig.objects.get(user_id=user_id).customer_id
        integration_config = IntegrationConfig.objects.get(type__name="SIIGO", customer_id=customer_id)

        invoice_param = IntegrationParam.objects.get(pk=invoice_param_id)
        if not invoice_param.config == integration_config:
            return Response({"result": "invoice param and customer mismatched"}, status=status.HTTP_400_BAD_REQUEST)

        payment_param = IntegrationParam.objects.get(pk=payment_param_id)
        if not payment_param.config == integration_config:
            return Response({"result": "payment param and customer mismatched"}, status=status.HTTP_400_BAD_REQUEST)

        cost_center_param = IntegrationParam.objects.get(pk=cost_center_param_id)
        if not cost_center_param.config == integration_config:
            return Response({"result": "cost center param and customer mismatched"},
                            status=status.HTTP_400_BAD_REQUEST)

        shipping_order = ShippingOrder.objects.get(pk=shipping_order_id)
        if not shipping_order.contact:
            return Response({"result": "there isnt contact in shipping order"},
                            status=status.HTTP_400_BAD_REQUEST)

        try:
            siigo.create_invoice_from_shipping(
                integration_config=integration_config, invoice_param=invoice_param, payment_param=payment_param,
                cost_center_param=cost_center_param, shipping_order=shipping_order)
            return Response(serializer.validated_data, status=status.HTTP_200_OK)
        except Exception as error:
            return Response({"result": error.args[0]}, status=status.HTTP_400_BAD_REQUEST)


class ModuleIntegrationConfigViewSet(viewsets.ModelViewSet):
    """
    retrieve:
        Return the given Module Integration Config.
    list:
        Return a list of all the existing Module Integration Config.
    create:
        Create a new Module Integration Config instance.
    update:
        Update an existing Module Integration Config instance.
    partial_update:
        Update partial an existing Module Integration Config instance.
    delete:
        Delete  an existing Module Integration Config instance.
    """
    queryset = ModuleIntegrationConfig.objects.all()
    serializer_class = ModuleIntegrationConfigSerializer
    filter_class = ModuleIntegrationConfigFilter


class ArcoIntegrationView(ViewSet):
    permission_classes = (AllowAny,)

    def sku_event(self, request, *args):
        print("Events Sku Arco")
        data = request.data
        create = "INS"
        update = "UPD"
        inactive = "DLT"
        event_type = ""

        if data["Method"] == "Producto_{}".format(create):
            print("Create Sku")
            event_type = "ARCO_SKU_CREATE"

        elif data["Method"] == "Producto_{}".format(update):
            print("Update Sku")
            event_type = "ARCO_SKU_UPDATE"

        elif data["Method"] == "Producto_{}".format(inactive):
            print("Inactive Sku")
            event_type = "ARCO_SKU_INACTIVE"

        module_config = integration_utils.verified_integration_module(customer_id=2, kong_module__name=event_type,
                                                                      active_integration=True)
        if module_config:
            request_body = {
                "detail-type": event_type,
                "product_id": data["Id"]
            }
            integration_tasks.integration_module_request(integration_module_id=module_config.id,
                                                         body=request_body)

            return Response(status=status.HTTP_201_CREATED)

        return Response(status=status.HTTP_400_BAD_REQUEST)

    def event_purchase_order(self, request, *args):
        print("Events Purchase Order Arco")
        data = request.data
        create = "INS"
        inactive = "UPD"
        event_type = ""

        if data["Method"] == "OrdenCompra_{}".format(create):
            print("Create Sku")
            event_type = "ARCO_PUO_CREATE"

        elif data["Method"] == "OrdenCompra_{}".format(inactive):
            print("Inactive Sku")
            event_type = "ARCO_PUO_INACTIVE"

        module_config = integration_utils.verified_integration_module(customer_id=2, kong_module__name=event_type,
                                                                      active_integration=True)
        if module_config:
            request_body = {
                "detail-type": event_type,
                "order_id": data["Payload"]["OrdenCompraId2"]
            }
            integration_tasks.integration_module_request(integration_module_id=module_config.id,
                                                         body=request_body)

            return Response(status=status.HTTP_201_CREATED)

        return Response(status=status.HTTP_400_BAD_REQUEST)


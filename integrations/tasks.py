# -*- coding: utf-8 -*-

from __future__ import unicode_literals

from celery import shared_task

from .business_logic import ups, shopify, siigo
from .models import *
import logging
import inventory.utils as inventory_utils
import integrations.utils as integration_utils


@shared_task(bind=True)
def package_costs(self, shipping):
    """
    Execute calculated of package costs.
    """
    integration_config = IntegrationConfig.objects.get(customer_id=shipping.customer.parent.id,
                                                       type__name='UPS')
    results = ups.get_calculated_value_package(integration_config=integration_config, shipping=shipping)
    return results


@shared_task(bind=True)
def adjustment_shopify_inventory(self, audit_id, action_missing, action_extra, reporter_id, integration_config_id):
    import inventory.tasks as inventory_tasks
    logging.info("Starting to process shopify adjustment inventory : {order}".format(order=audit_id))

    audit_object = Audit.objects.get(pk=audit_id)
    audit_object.status = 'PROCESSING'
    audit_object.save()
    integration_config = IntegrationConfig.objects.get(pk=integration_config_id)
    inventory_tasks.adjustment_audit(audit_id=audit_object.pk, action_extra=action_extra,
                                     action_missing=action_missing,
                                     reporter_id=reporter_id)
    sku_process_result = shopify.process_set_inventory(location=audit_object.location, customer=audit_object.customer,
                                                       integration_config=integration_config)
    audit_object = Audit.objects.get(pk=audit_id)
    shopify.generate_file(sku_process_result=sku_process_result, audit_object=audit_object)


@shared_task(bind=True)
def inventory_update_operation(self, item_list: list, customer_id: int, location_id: int, integration_config_id: int,
                               value: bool):
    """
        Update inventory shopify increase or remove units.
    """

    customer_object = Customer.objects.get(id=customer_id)
    location_object = Location.objects.get(id=location_id)
    integration_config_object = IntegrationConfig.objects.get(id=integration_config_id)
    shopify.process_inventory(items=item_list, value=value, customer=customer_object,
                              location=location_object, integration_config=integration_config_object,
                              set_process=False)
    return integration_config_object


@shared_task(bind=True)
def inventory_validate_position(self, destination_id: int, customer_id: int, items_list: list):
    """
        Update inventory shopify increase or remove units.
    """

    destination_object = Location.objects.get(id=destination_id)
    customer_object = Customer.objects.get(id=customer_id)

    verification_module = integration_utils.verified_integration_module(customer_id=customer_object.id,
                                                                        permission_name='INVENTORY_ITEM_POSITION')
    sorter_locations = inventory_utils.sort_items_by_current_location(items_ids=items_list,
                                                                      customer_id=customer_object.id)

    if verification_module and verification_module.active_integration:
        integration_config_object = verification_module.integration_config
        if destination_object != verification_module.current_location:
            for group in sorter_locations:
                if group['current_location'] == verification_module.current_location:
                    shopify.process_inventory(items=group['items'], value=False, customer=customer_object,
                                              location=verification_module.current_location,
                                              integration_config=integration_config_object,
                                              set_process=False)
                else:
                    continue
        else:
            for group in sorter_locations:
                if group['current_location'] != verification_module.current_location:
                    shopify.process_inventory(items=group['items'], value=True, customer=customer_object,
                                              location=verification_module.current_location,
                                              integration_config=integration_config_object,
                                              set_process=False)
                else:
                    continue
    return destination_object


@shared_task(bind=True)
def sync_siigo_configs(self, integration_group_id: int, integration_config_id: int, user_id: int):
    logging.info("Starting to process siigo sync")

    integration_config = IntegrationConfig.objects.get(pk=integration_config_id)
    if integration_group_id:
        integration_group = IntegrationGroup.objects.get(pk=integration_group_id)
        sync_all = False
    else:
        integration_group = None
        sync_all = True
    siigo.process_sync(integration_group=integration_group, integration_config=integration_config, user_id=user_id,
                       sync_all=sync_all)


@shared_task(bind=True)
def integration_module_request(self, integration_module_id: int, body: dict, async_request=True):
    """
    Allow AWS Lambda integration request, if necessary implement others
    """
    integration_module = ModuleIntegrationConfig.objects.filter(id=integration_module_id).first()
    if integration_module and integration_module.integration_config.type.name == "AWS_LAMBDA":
        integration_utils.aws_lambda_request(
            lambda_name=integration_module.integration_config.domain_name, body=body, async_request=async_request)

    elif integration_module and integration_module.integration_config.type.name == "EVENTBRIDGE":
        integration_utils.eventbridge_request(
            source=integration_module.integration_config.username, body=body)

    elif integration_module:
        logging.error("ModuleIntegrationConfig {} has type {}, this type is not allowed".format(
            integration_module_id, integration_module.integration_config.type.name))
    else:
        logging.error("ModuleIntegrationConfig {} not found".format(integration_module_id))

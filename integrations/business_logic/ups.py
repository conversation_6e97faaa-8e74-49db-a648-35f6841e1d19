# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import requests
from integrations.models import IntegrationConfig, ServiceType
from operations.models import Contact
from customers.models import Customer
from logistics.models import ShippingOrder
import json


def get_calculated_value_package(integration_config, shipping):
    shipper = Customer.objects.get(pk=shipping.customer.parent.id)

    data = ({"UPSSecurity": {
        "UsernameToken": {
            "Username": integration_config.username,
            "Password": integration_config.password
        },
        "ServiceAccessToken": {
            "AccessLicenseNumber": integration_config.access_key
        }
    },
        "RateRequest": {
            "Request": {
                "SubVersion": "1703",
                "RequestOption": "Rate",
                "TransactionReference": {
                    "CustomerContext": ""
                }
            },
            "Shipment": {
                "Shipper": {
                    "Name": shipper.display_name,
                    "ShipperNumber": shipper.identification,
                    "Address": {
                        "AddressLine": [
                            shipper.properties['Address']['AddressLine'][0]
                        ],
                        "City": shipper.properties['Address']['City'],
                        "StateProvinceCode": shipper.properties['Address']['StateProvinceCode'],
                        "PostalCode": shipper.properties['Address']['PostalCode'],
                        "CountryCode": shipper.properties['Address']['CountryCode']
                    }
                },
                "ShipTo": {
                    "Name": shipping.contact.name,
                    "Address": {
                        "AddressLine":
                            shipping.contact.properties['addresses'][0]['address1'] +
                            shipping.contact.properties['addresses'][0]['address2']
                        ,
                        "City": shipping.contact.properties['addresses'][0]['city'],
                        "StateProvinceCode": shipping.contact.properties['addresses'][0]['province_code'],
                        "PostalCode": shipping.contact.properties['addresses'][0]['zip'],
                        "CountryCode": shipping.contact.properties['addresses'][0]['country_code']
                    }
                },
                "ShipFrom": {
                    "Name": shipping.customer.name,
                    "Address": {
                        "AddressLine": [
                            shipping.customer.properties['Address']['AddressLine'][0]
                        ],
                        "City": shipping.customer.properties['Address']['City'],
                        "StateProvinceCode": shipping.customer.properties['Address']['StateProvinceCode'],
                        "PostalCode": shipping.customer.properties['Address']['PostalCode'],
                        "CountryCode": shipping.customer.properties['Address']['CountryCode']
                    }
                },
                "Service": {
                    "Code": shipping.package_order.service.external_id,
                    "Description": shipping.package_order.service.name
                },
                "ShipmentTotalWeight": {
                    "UnitOfMeasurement": {
                        "Code": shipping.package_order.unit_weight,
                        "Description": "Pounds"
                    },
                    "Weight": shipping.package_order.weight
                },
                "Package": {
                    "PackagingType": {
                        "Code": shipping.package_order.type.external_id,
                        "Description": shipping.package_order.type.external_id
                    },
                    "Dimensions": {
                        "UnitOfMeasurement": {
                            "Code": shipping.package_order.unit_dimensions
                        },
                        "Length": shipping.package_order.dimensions['length'],
                        "Width": shipping.package_order.dimensions['width'],
                        "Height": shipping.package_order.dimensions['height']
                    },
                    "PackageWeight": {
                        "UnitOfMeasurement": {
                            "Code": shipping.package_order.unit_weight
                        },
                        "Weight": shipping.package_order.weight
                    }
                }
            }
        }
    })

    json_data = json.dumps(data)
    url = 'https://wwwcie.ups.com/rest/Rate'
    request = requests.post(url=url, data=json_data)
    money = request.json()

    if 'Fault' in money:
        return money
    else:
        response = money['RateResponse']
        result = {
            'TransportationCharges': response['RatedShipment']['RatedPackage']['TransportationCharges'],
            'BaseServiceCharge': response['RatedShipment']['RatedPackage']['BaseServiceCharge'],
            'ServiceOptionsCharges': response['RatedShipment']['RatedPackage']['ServiceOptionsCharges'],
            'ItemizedCharges': response['RatedShipment']['RatedPackage']['ItemizedCharges'],
            'TotalCharges': response['RatedShipment']['RatedPackage']['TotalCharges'],
            'BillingWeight': response['RatedShipment']['RatedPackage']['BillingWeight']
        }
        shipping.package_order.properties = result
        shipping.package_order.save()
        return response


def send_ship(integration_config, shipping_order):
    data = ()

    json_data = json.dumps(data)
    url = 'https://wwwcie.ups.com/rest/Ship'
    request = requests.post(url=url, data=json_data)
    money = request.json()
    print(money)


def generate_tracking(shipping_order_id):
    shipping_order = ShippingOrder.objects.get(pk=shipping_order_id)

    data = {}

import requests
from integrations.models import *
import json
from logistics.models import *


def get_siigo_identification_id(identification_type: str):
    identification_map = {
        "CC": "13",
        "NIT": "31",
        "TI": "12",
        "PASSPORT": "41",
        "SHIPPER_NUMBER": "42"
    }
    return identification_map[identification_type]


def get_payment_means(headers: dict, number_page: int, payment_means: list):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/PaymentMeans/GetAll?numberPage={}&namespace=v1"\
        .format(number_page)
    response = requests.get(url=url, headers=headers)
    json_response = json.loads(response.text)
    if json_response:
        payment_means += json_response
        new_payment_means = get_payment_means(headers=headers, number_page=number_page + 1, payment_means=payment_means)
        return new_payment_means
    else:
        return payment_means


def update_or_create_payment_means(payment_means_dict: dict, integration_config: IntegrationConfig,
                                   integration_group: IntegrationGroup, user_id: int):
    payment_means = IntegrationParam.objects.filter(integration_config=integration_config, group=integration_group)
    for payment_mean in payment_means:
        if payment_mean.external_id in payment_means_dict:
            payment_mean_dict = payment_means_dict.pop(payment_mean.external_id)
            payment_mean.name = payment_mean_dict['Name']
            payment_mean.modified_by_id = user_id
            payment_mean.save()
    new_payment_means = []
    for external_id, values_dict in payment_means_dict.items():
        new_payment_means.append(
            IntegrationParam(
                name=values_dict['Name'],
                external_id=external_id,
                group=integration_group,
                integration_config=integration_config,
                created_by_id=user_id,
                modified_by_id=user_id
            )
        )
    IntegrationParam.objects.bulk_create(new_payment_means)


def get_taxs(headers: dict, number_page: int, taxs: list):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/Taxes/GetAll?numberPage={}&namespace=v1"\
        .format(number_page)
    response = requests.get(url=url, headers=headers)
    json_response = json.loads(response.text)
    if json_response:
        taxs += json_response
        new_taxs = get_taxs(headers=headers, number_page=number_page + 1, taxs=taxs)
        return new_taxs
    else:
        return taxs


def update_or_create_taxs(taxs_dict: dict, integration_config: IntegrationConfig,
                          integration_group: IntegrationGroup, user_id: int):
    taxs = IntegrationParam.objects.filter(integration_config=integration_config, group=integration_group)
    for tax in taxs:
        if tax.external_id in taxs_dict:
            tax_dict = taxs_dict.pop(tax.external_id)
            tax.name = tax_dict["Description"]
            tax.properties["Percentage"] = tax_dict["Percentage"]
            tax.properties["TaxType"] = tax_dict["TaxType"]
            tax.modified_by_id = user_id
            tax.save()
    new_taxs = []
    for external_id, values_dict in taxs_dict.items():
        new_taxs.append(
            IntegrationParam(
                name=values_dict['Description'],
                external_id=external_id,
                properties={
                    "Percentage": values_dict["Percentage"],
                    "TaxType": values_dict["TaxType"]
                },
                group=integration_group,
                integration_config=integration_config,
                created_by_id=user_id,
                modified_by_id=user_id
            )
        )
    IntegrationParam.objects.bulk_create(new_taxs)


def get_invoices(headers: dict):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/ERPDocumentTypes/GetAllByType/0?namespace=v1"
    response = requests.get(url=url, headers=headers)
    json_response = json.loads(response.text)
    return json_response


def update_or_create_invoices(invoices_dict: dict, integration_config: IntegrationConfig,
                              integration_group: IntegrationGroup, user_id: int):
    invoices = IntegrationParam.objects.filter(integration_config=integration_config, group=integration_group)
    for invoice in invoices:
        if invoice.external_id in invoices_dict:
            invoice_dict = invoices_dict.pop(invoice.external_id)
            invoice.name = invoice_dict["InternalDescription"]
            invoice.description = invoice_dict["Name"]
            invoice.properties["ERPDocClass"] = invoice_dict["ERPDocClass"]
            invoice.properties["ERPDocCode"] = invoice_dict["ERPDocCode"]
            invoice.properties["IsAutomaticEnum"] = invoice_dict["IsAutomaticEnum"]
            invoice.modified_by_id = user_id
            invoice.save()
    new_invoices = []
    for external_id, values_dict in invoices_dict.items():
        new_invoices.append(
            IntegrationParam(
                name=values_dict['InternalDescription'],
                external_id=external_id,
                description=values_dict['Name'],
                properties={
                    "ERPDocClass": values_dict["ERPDocClass"],
                    "ERPDocCode": values_dict["ERPDocCode"],
                    "IsAutomaticEnum": values_dict["IsAutomaticEnum"]
                },
                group=integration_group,
                integration_config=integration_config,
                created_by_id=user_id,
                modified_by_id=user_id
            )
        )
    IntegrationParam.objects.bulk_create(new_invoices)


def get_warehouses(headers: dict, number_page: int, warehouses: list):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/Warehouses/GetAll?numberPage={}&namespace=v1" \
        .format(number_page)
    response = requests.get(url=url, headers=headers)
    json_response = json.loads(response.text)
    if json_response:
        warehouses += json_response
        new_warehouses = get_warehouses(headers=headers, number_page=number_page + 1, warehouses=warehouses)
        return new_warehouses
    else:
        return warehouses


def update_or_create_warehouses(warehouses_dict: dict, integration_config: IntegrationConfig,
                                integration_group: IntegrationGroup, user_id: int):
    warehouses = IntegrationParam.objects.filter(integration_config=integration_config, group=integration_group)
    for warehouse in warehouses:
        if warehouse.external_id in warehouses_dict:
            warehouse_dict = warehouses_dict.pop(warehouse.external_id)
            warehouse.name = warehouse_dict["Description"]
            warehouse.modified_by_id = user_id
            warehouse.save()
    new_warehouses = []
    for external_id, values_dict in warehouses_dict.items():
        new_warehouses.append(
            IntegrationParam(
                name=values_dict['Description'],
                external_id=external_id,
                group=integration_group,
                integration_config=integration_config,
                created_by_id=user_id,
                modified_by_id=user_id
            )
        )
    IntegrationParam.objects.bulk_create(new_warehouses)


def process_sync(integration_group: IntegrationGroup, integration_config: IntegrationConfig, user_id: int,
                 sync_all: bool):
    token = auth_me(integration_config=integration_config)
    headers = {
        'Ocp-Apim-Subscription-Key': integration_config.access_key,
        'Authorization': token
    }
    if sync_all or integration_group.name == "PAYMENT MEAN":
        if sync_all:
            integration_group = IntegrationGroup.objects.get(name='PAYMENT MEAN')
        payment_means = get_payment_means(headers=headers, number_page=0, payment_means=[])
        payment_means_dict = {}
        for payment_mean in payment_means:
            if payment_mean['Type'] in [0, 2] and not payment_mean['IsSystem']:
                payment_means_dict[str(payment_mean['ACPaymentMeanID'])] = payment_mean
        update_or_create_payment_means(payment_means_dict=payment_means_dict, integration_config=integration_config,
                                       integration_group=integration_group, user_id=user_id)
    if sync_all or integration_group.name in ["IVA", "RETEFUENTE"]:
        taxs = get_taxs(headers=headers, number_page=0, taxs=[])
        iva_dict = {}
        retefuente_dict = {}
        for tax in taxs:
            if tax['TaxType'] == 0:
                iva_dict[str(tax['Id'])] = tax
            elif tax['TaxType'] == 1:
                retefuente_dict[str(tax['Id'])] = tax
        if sync_all:
            integration_group = IntegrationGroup.objects.get(name='IVA')
            update_or_create_taxs(taxs_dict=iva_dict, integration_config=integration_config,
                                  integration_group=integration_group, user_id=user_id)
            integration_group = IntegrationGroup.objects.get(name='RETEFUENTE')
            update_or_create_taxs(taxs_dict=retefuente_dict, integration_config=integration_config,
                                  integration_group=integration_group, user_id=user_id)
        elif integration_group.name == "IVA":
            update_or_create_taxs(taxs_dict=iva_dict, integration_config=integration_config,
                                  integration_group=integration_group, user_id=user_id)
        else:
            update_or_create_taxs(taxs_dict=retefuente_dict, integration_config=integration_config,
                                  integration_group=integration_group, user_id=user_id)
    if sync_all or integration_group.name == "INVOICE":
        if sync_all:
            integration_group = IntegrationGroup.objects.get(name='INVOICE')
        invoices = get_invoices(headers=headers)
        invoices_dict = {}
        for invoice in invoices:
            if invoice['ERPDocClass'] == 'FV' and invoice['IsAutomaticEnum'] and invoice['IsActive']:
                invoices_dict[str(invoice['Id'])] = invoice
        update_or_create_invoices(invoices_dict=invoices_dict, integration_config=integration_config,
                                  integration_group=integration_group, user_id=user_id)
    if sync_all or integration_group.name == "WAREHOUSE":
        if sync_all:
            integration_group = IntegrationGroup.objects.get(name='WAREHOUSE')
        warehouses = get_warehouses(headers=headers, number_page=0, warehouses=[])
        warehouses_dict = {}
        for warehouse in warehouses:
            if warehouse['InUse']:
                warehouses_dict[str(warehouse['Code'])] = warehouse
        update_or_create_warehouses(warehouses_dict=warehouses_dict, integration_config=integration_config,
                                    integration_group=integration_group, user_id=user_id)


def auth_me(integration_config: IntegrationConfig):
    url = "https://siigonube.siigo.com:50050/connect/token"
    data = {
        'grant_type': 'password',
        'username': integration_config.username,
        'password': integration_config.password,
        'scope': 'WebApi offline_access'
    }
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': 'Basic U2lpZ29XZWI6QUJBMDhCNkEtQjU2Qy00MEE1LTkwQ0YtN0MxRTU0ODkxQjYx',
        'Accept': 'application/json'
    }
    response = requests.post(url=url, headers=headers, data=data)
    json_response = json.loads(response.text)
    token = json_response['access_token']
    return token


def get_account(identification: str, headers: dict):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/Accounts/GetByCode?identification={}&branchOffice=0" \
          "&namespace=v1".format(identification)
    response = requests.get(url=url, headers=headers)
    json_response = json.loads(response.text)
    return json_response


def create_account(contact: Contact, shipping_order: ShippingOrder, headers: dict):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/Accounts/Create?namespace=v1"
    identification_id = get_siigo_identification_id(identification_type=contact.type_identification)
    last_name = '...'
    if contact.last_name:
        last_name = contact.last_name
    data = {
        "IsLeaflet": False,
        "IsCustomer": True,
        "IsSupplier": False,
        "IsDealer": False,
        "IsBank": False,
        "IsSocialReason": False,
        "FirstName": contact.name,
        "LastName": last_name,
        "IdTypeCode": identification_id,
        "Identification": contact.identification,
        "BranchOffice": 0,
        "IsVATCompanyType": False,
        "Address": shipping_order.shipping_address.get("address1", None),
        "Phone": {
            "Number": None
        },
        "EMail": contact.email,
        "IsActive": True
    }
    response = requests.post(url=url, data=json.dumps(data), headers=headers)
    if response.status_code >= 400:
        raise Exception(response.text)
    json_response = json.loads(response.text)
    return json_response


def get_contact(contact_siigo_id: str, headers: dict):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/Contacts/GetByID/{}?namespace=v1".format(contact_siigo_id)
    response = requests.get(url=url, headers=headers)
    json_response = json.loads(response.text)
    return json_response


def create_contact(contact: Contact, shipping_order: ShippingOrder, account: dict, headers: dict):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/Contacts/Create?namespace=v1"
    last_name = '...'
    if contact.last_name:
        last_name = contact.last_name
    data = {
        "AccountID": account["Id"],
        "Mobile": {
            "Number": None
        },
        "EMail": contact.email,
        "FirstName": contact.name,
        "LastName": last_name,
        "Gender": 2,
        "IsPrincipal": True
    }
    response = requests.post(url=url, data=json.dumps(data), headers=headers)
    if response.status_code >= 400:
        raise Exception(response.text)
    json_response = json.loads(response.text)
    return json_response


def create_invoice_from_shipping(integration_config: IntegrationConfig, invoice_param: IntegrationParam,
                                 payment_param: IntegrationParam, cost_center_param: IntegrationParam,
                                 shipping_order: ShippingOrder):
    url = "http://siigoapi.azure-api.net/siigo/api/v1/Invoice/Save?namespace=v1"
    token = auth_me(integration_config=integration_config)
    headers = {
        'Ocp-Apim-Subscription-Key': integration_config.access_key,
        'Authorization': token,
        'Content-Type': 'application/json'
    }
    contact = shipping_order.contact
    seller_identification = ""
    if shipping_order.seller:
        seller_identification = shipping_order.seller.identification
    account = get_account(identification=contact.identification, headers=headers)
    if not account['Id']:
        account = create_account(contact=contact, shipping_order=shipping_order, headers=headers)
    if account["PrincipalContactID"]:
        siigo_contact = get_contact(contact_siigo_id=account["PrincipalContactID"], headers=headers)
    else:
        siigo_contact = create_contact(contact=contact, account=account, shipping_order=shipping_order,
                                       headers=headers)
    str_date = shipping_order.created.strftime('%Y%m%d')
    items = []
    total_tax = 0
    total_value = 0
    total_discounts = 0
    shipping_order_lines = ShippingOrderLine.objects.filter(shipping_order=shipping_order)
    for line in shipping_order_lines:
        amount = line.amount
        price = float(line.costs.get('price', 0)) * amount
        discount = float(line.costs.get('discount', 0))
        iva_param_code = line.costs.get('iva_param_code', -1)
        tax = 0
        if not iva_param_code == -1:
            tax = float(line.costs.get('tax', {}).get('price', 0))
        warehouse_code = None
        if line.location_param:
            warehouse_code = line.location_param.external_id
        item = {
            "ProductCode": line.sku.external_id,
            "GrossValue": price - tax - discount,
            "BaseValue": price - tax,
            "Quantity": amount,
            "UnitValue": (price - tax)/amount,
            "TaxAddId": line.costs.get('iva_param_code', -1),
            "TaxAddValue": tax,
            "TaxDiscountId": -1,
            "TotalValue": price,
            "TaxAdd2Id": -1,
            "WareHouseCode": warehouse_code,
            "SalesmanIdentification": seller_identification
        }
        total_tax += tax
        total_value += price
        total_discounts += discount
        items.append(item)
    cost_center_code = cost_center_param.external_id.split('-')
    cost_center_code.extend(["-1"])
    data = {
        "Header": {
            "DocCode": invoice_param.external_id,
            "DocDate": str_date,
            "DiscountValue": total_discounts,
            "VATTotalValue": "{0:.2f}".format(total_tax),
            "TotalValue": total_value,
            "TotalBase": total_value - total_tax,
            "RetICATotalID": -1,
            "RetICATotaPercentage": -1,
            "RetVATTotalID": -1,
            "RetVATTotalPercentage": -1,
            "SalesmanIdentification": seller_identification,
            "Observations": "",
            "Account": account,
            "Contact": siigo_contact,
            "CostCenterCode": cost_center_code[0],
            "SubCostCenterCode": cost_center_code[1]
        },
        "Items": items,
        "Payments": [
            {
                "PaymentMeansCode": payment_param.external_id,
                "Value": total_value,
                "DueDate": str_date,
                "DueQuote": 1
            }
        ]
    }
    response = requests.post(url=url, data=json.dumps(data), headers=headers)
    if response.status_code >= 400:
        raise Exception(response.text)
    json_response = json.loads(response.text)
    if not shipping_order.billing_address:
        shipping_order.billing_address = {}
    shipping_order.billing_address["siigo_invoice_id"] = json_response["Header"]["Id"]
    shipping_order.save()

# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import time

import requests
import json
import logging
import os
import csv
from django.core.files.base import File

from operations.models import Contact
from logistics.business_logic import shipping_orders
from logistics.models import ShippingOrder, ShippingOrderLine
from inventory.models import SKU, Location, Item
from inventory.business_logic import audits
import inventory.utils as inventory_utils
from customers.models import Customer
from integrations.models import IntegrationConfig


def get_values_order(order):
    lines = []
    costs = {}
    external_id = order.get('id', None)
    created = order.get('created_at', None)
    updated = order.get('updated_at', None)
    costs["payment_method"] = order.get('gateway', None)
    costs["total_price"] = order.get('total_price', None)
    costs["total_items_price"] = order.get('total_line_items_price', None)
    costs["total_discounts"] = order.get('total_discounts', None)
    costs["shipping_price"] = order.get('total_shipping_price_set', {}).get('shop_money', {}) \
        .get('amount', None)

    for line in order.get('line_items', None):
        line_costs = {}
        line_sku = line['sku']
        line_amount = line['quantity']
        line_costs["tax"] = line.get('tax_lines', [{}])[0]
        line_costs["price"] = line.get('price', None)
        line_costs["discount"] = line.get('total_discount', None)
        line = {'sku_id': line_sku, 'amount': line_amount, "costs": line_costs}
        lines.append(line)

    billing_address = order.get('billing_address', None)
    shipping_address = order.get('shipping_address', None)
    contact = order.get('customer', None)

    data = {"external_id": external_id, "created": created, "updated": updated, "billing_address": billing_address,
            "shipping_address": shipping_address, "contact": contact, "lines": lines, "costs": costs}
    return data


def validate_sku_external_ids(lines):
    """
    creates production order lines from {sku,amount} array
    """
    skus_list = []

    for line in lines:
        skus_list.append(line['sku_id'])

    skus_ids = SKU.objects.filter(external_id__in=skus_list).values_list('id', flat=True)

    if not len(skus_list) == len(skus_ids):
        return False
    else:
        return True


def transform_lines(lines):
    """
    creates production order lines from {sku,amount} array
    """
    skus_list = []
    new_lines = []

    for line in lines:
        skus_list.append(line['sku_id'])

    skus_ids = SKU.objects.filter(external_id__in=skus_list).values('id', 'external_id')

    for ids in skus_ids:
        sku_external_id = ids.get('external_id')
        sku_id = ids.get('id')
        dict_lines = {}
        for line in lines:
            if sku_external_id == line['sku_id']:
                dict_lines['sku_id'] = sku_id
                dict_lines['amount'] = line['amount']
                dict_lines['costs'] = line['costs']
                new_lines.append(dict_lines)
                break

    return new_lines


def create_orders(order, integration_config):
    # get values of response on connection.
    str = order.decode('utf-8')
    order = json.loads(str)
    if order.get('financial_status') == 'paid':
        # validate values contact.
        data = get_values_order(order)
        valid_skus = validate_sku_external_ids(lines=data['lines'])
        if valid_skus:
            contact_object = Contact.objects.filter(external_id=data['contact']['id'], type_contact='CLIENT')
            if contact_object:
                contact_object = contact_object.first()
            else:
                url = 'https://{}@{}/admin/api/2019-04/customers/{}.json'.format(integration_config.password,
                                                                                 integration_config.username,
                                                                                 data['contact']['id'])
                request = requests.get(url)
                response = request.json()
                if "customer" in response:
                    contact_object = shipping_orders.create_contact(data=response["customer"],
                                                                    integration_config=integration_config)

            # create shipping order and lines.
            shipping_object = ShippingOrder.objects.filter(external_id=data['external_id'])
            if shipping_object:
                shipping_object = shipping_object.first()
            else:
                shipping_object = shipping_orders.create_shipping_order(external_id=data['external_id'],
                                                                        created=data['created'],
                                                                        updated=data['updated'],
                                                                        billing_address=data['billing_address'],
                                                                        shipping_address=data['shipping_address'],
                                                                        costs=data['costs'],
                                                                        contact=contact_object,
                                                                        customer=integration_config.customer,
                                                                        user=integration_config.integrator_user)

            shipping_lines = ShippingOrderLine.objects.filter(shipping_order_id=shipping_object.pk)
            if shipping_lines:
                shipping_lines.delete()
            lines = transform_lines(lines=data['lines'])
            shipping_orders.create_lines_with_costs(order=shipping_object, lines=lines)
            return shipping_object
        else:
            return False
    else:
        return False


def get_orders(integration_config):
    url = 'https://{}@{}/admin/api/2019-04/orders.json'.format(integration_config.password,
                                                               integration_config.username)
    request = requests.get(url)
    orders = request.json()
    order_objects = []

    if "orders" in orders:
        data = orders["orders"]
        for order in data:
            if order['financial_status'] == 'paid':
                orders = create_orders(order=order, integration_config=integration_config)
                order_objects.append(orders)
                return order_objects


def create_SKU(skus, integration_config):
    properties = {}
    params = {}
    for sku_data in skus["variants"]:
        if sku_data["sku"]:
            variant_str = ''
            for variants in skus["options"]:
                variant_name = variants["name"]
                variant_value = sku_data["option{}".format(variants["position"])]
                variant_str = "{} {} {}".format(variant_str, variant_name, variant_value)
                properties[variant_name] = variant_value
            properties["price"] = str(sku_data["price"])
            properties["weight"] = str(sku_data["weight"])
            properties["weight_unit"] = sku_data["weight_unit"]
            properties["product_type"] = skus["product_type"]
            properties["tags"] = skus["tags"]
            params["shopify_id"] = str(sku_data["product_id"])
            params["shopify_variant_id"] = str(sku_data["id"])
            params["shopify_inventory_item_id"] = str(sku_data["inventory_item_id"])
            params["shopify_inventory_quantity"] = sku_data["inventory_quantity"]

            name = "{} {}".format(skus["title"], variant_str)
            sku = SKU.objects.create(
                created_by=integration_config.integrator_user,
                modified_by=integration_config.integrator_user,
                type_id=integration_config.default_sku_type_id,
                group_id=integration_config.default_sku_group_id,
                customer_id=integration_config.customer_id,
                external_id=sku_data["sku"],
                name=name,
                display_name=name,
                properties=properties,
                params=params,
                filter=integration_config.default_sku_filter
            )
            if sku.reference == '000None':
                sku.reference = ""
            sku.save()
        else:
            logging.error("sku variant does not have sku code")
    return True


def update_SKU(skus, integration_config):
    for sku_data in skus["variants"]:
        if sku_data["sku"]:
            sku = SKU.objects.filter(external_id=sku_data["sku"]).first()
            if sku:
                properties = sku.properties
                params = sku.params
                if not params:
                    params = {}
                if not properties:
                    properties = {}
                params["shopify_id"] = str(sku_data["product_id"])
                params["shopify_variant_id"] = str(sku_data["id"])
                params["shopify_inventory_item_id"] = str(sku_data["inventory_item_id"])
                params["shopify_inventory_quantity"] = sku_data["inventory_quantity"]
                properties["price"] = str(sku_data["price"])
                properties["weight"] = str(sku_data["weight"])
                properties["weight_unit"] = sku_data["weight_unit"]
                properties["product_type"] = skus["product_type"]
                properties["tags"] = skus["tags"]
                sku.params = params
                sku.properties = properties
                sku.modified_by = integration_config.integrator_user
                sku.save()

            else:
                new_sku = skus
                new_sku["variants"] = [sku_data]
                create_SKU(skus=new_sku, integration_config=integration_config)
    return True


def refresh_inventory(integration_config, shopify_inventory_item_id, amount, shopify_location_id):
    json_data = {
        "location_id": shopify_location_id,
        "inventory_item_id": int(shopify_inventory_item_id),
        "available_adjustment": amount
    }
    url = 'https://{}@{}/admin/api/2019-04/inventory_levels/adjust.json'.format(integration_config.password,
                                                                                integration_config.username)
    request = requests.post(url, data=json_data)
    if request.status_code >= 400:
        raise
    shopify_inventory_level = request.json()
    return shopify_inventory_level


def set_inventory(integration_config, shopify_inventory_item_id, amount, shopify_location_id):
    json_data = {
        "location_id": shopify_location_id,
        "inventory_item_id": int(shopify_inventory_item_id),
        "available": amount
    }
    url = 'https://{}@{}/admin/api/2019-04/inventory_levels/set.json'.format(integration_config.password,
                                                                             integration_config.username)
    request = requests.post(url, data=json_data)
    if request.status_code >= 400:
        raise
    shopify_inventory_level = request.json()
    return shopify_inventory_level


def search_shopify_id_by_sku(integration_config, sku_external_id):
    query = "{productVariants(query:\"sku:" + sku_external_id + "\" first:5){edges{node{product{id}}}}}"
    data = {
        "query": query
    }
    headers = {
        'Content-Type': 'application/json'
    }
    url = 'https://{}@{}/admin/api/2019-10/graphql.json'.format(integration_config.password,
                                                                integration_config.username)
    request = requests.post(url, data=json.dumps(data), headers=headers)
    shopify_product = request.json()['data']['productVariants']['edges'][0]['node']['product']['id']
    shopify_product_id = shopify_product[(str.find(shopify_product, 'Product/') + 8):]
    return shopify_product_id


def get_shopify_product(integration_config, shopify_product_id):
    url = 'https://{}@{}/admin/api/2019-04/products/{}.json'.format(integration_config.password,
                                                                    integration_config.username, shopify_product_id)
    request = requests.get(url)
    if request.status_code >= 400:
        raise
    shopify_product = request.json()
    return shopify_product


def process_inventory(items: list, value: bool, customer: Customer, location: Location,
                      integration_config: IntegrationConfig, set_process=False):
    result = inventory_utils.sort_items_by_sku(items, customer.pk)
    sku_list = [x['sku'].id for x in result]

    sku_params = SKU.objects.filter(id__in=sku_list).values('id', 'external_id', 'params')
    sku_process_result = []

    changed = False
    for sku in sku_params:
        if not (sku['params'] and 'shopify_inventory_item_id' in sku['params']):
            try:
                shopify_product_id = search_shopify_id_by_sku(integration_config=integration_config,
                                                              sku_external_id=sku['external_id'])
                shopify_product = get_shopify_product(integration_config=integration_config,
                                                      shopify_product_id=shopify_product_id)
                update_SKU(integration_config=integration_config, skus=shopify_product['product'])
                changed = True
            except Exception as error:
                logging.error(error.args)
    if changed:
        sku_params = SKU.objects.filter(id__in=sku_list).values('id', 'external_id', 'params')

    for sku in sku_params:
        if sku['params'] and 'shopify_inventory_item_id' in sku['params']:
            try:
                amount = [len(element['items']) for element in result if element['sku'].id == sku['id']]
                if value:
                    amount = +(amount[0])
                else:
                    amount = -(amount[0])
                if set_process:
                    set_inventory(integration_config=integration_config,
                                  shopify_inventory_item_id=sku['params']['shopify_inventory_item_id'],
                                  amount=amount, shopify_location_id=location.external_id)
                else:
                    refresh_inventory(integration_config=integration_config,
                                      shopify_inventory_item_id=sku['params']['shopify_inventory_item_id'],
                                      amount=amount, shopify_location_id=location.external_id)
                sku_process_result.append({"sku_id": str(sku['id']), "sku_external_id": sku['external_id'],
                                           "amount": amount, "result": "successful fit"})
            except:
                sku_process_result.append({"sku_id": str(sku['id']), "sku_external_id": sku['external_id'],
                                           "amount": 0, "result": "error response from shopify"})
        else:
            sku_process_result.append({"sku_id": str(sku['id']), "sku_external_id": sku['external_id'],
                                       "amount": 0, "result": "error updating sku params info"})
    return sku_process_result


def process_inventory_audits(items: list, value: bool, customer: Customer, location: Location,
                             integration_config: IntegrationConfig, set_process=False, empty=False):
    result: dict

    if empty:
        result = inventory_utils.sort_items_by_sku_empty(items, customer.pk)
    else:
        result = inventory_utils.sort_items_by_sku(items, customer.pk)
    sku_list = [x['sku'].id for x in result]

    sku_params = SKU.objects.filter(id__in=sku_list).values('id', 'external_id', 'params')
    sku_process_result = []

    changed = False
    for sku in sku_params:
        if not (sku['params'] and 'shopify_inventory_item_id' in sku['params']):
            try:
                shopify_product_id = search_shopify_id_by_sku(integration_config=integration_config,
                                                              sku_external_id=sku['external_id'])
                shopify_product = get_shopify_product(integration_config=integration_config,
                                                      shopify_product_id=shopify_product_id)
                update_SKU(integration_config=integration_config, skus=shopify_product['product'])
                changed = True
            except Exception as error:
                logging.error(error.args)
                logging.error("{}".format(sku['external_id']))
    if changed:
        sku_params = SKU.objects.filter(id__in=sku_list).values('id', 'external_id', 'params')

    for sku in sku_params:
        if sku['params'] and 'shopify_inventory_item_id' in sku['params']:
            try:
                amount = [len(element['items']) for element in result if element['sku'].id == sku['id']]
                if value:
                    if amount[0] == 0:
                        amount = amount[0]
                    else:
                        amount = +(amount[0])
                else:
                    if amount[0] == 0:
                        amount = amount[0]
                    else:
                        amount = -(amount[0])
                if set_process:
                    set_inventory(integration_config=integration_config,
                                  shopify_inventory_item_id=sku['params']['shopify_inventory_item_id'],
                                  amount=amount, shopify_location_id=location.external_id)
                    time.sleep(1)
                else:
                    refresh_inventory(integration_config=integration_config,
                                      shopify_inventory_item_id=sku['params']['shopify_inventory_item_id'],
                                      amount=amount, shopify_location_id=location.external_id)
                    time.sleep(1)
                sku_process_result.append({"sku_id": str(sku['id']), "sku_external_id": sku['external_id'],
                                           "amount": amount, "result": "successful fit"})
            except:
                sku_process_result.append({"sku_id": str(sku['id']), "sku_external_id": sku['external_id'],
                                           "amount": 0, "result": "error response from shopify"})
        else:
            sku_process_result.append({"sku_id": str(sku['id']), "sku_external_id": sku['external_id'],
                                       "amount": 0, "result": "error updating sku params info"})
    return sku_process_result


def process_audit(audit_object, action_missing, action_extra, integration_config):
    audit_results = audits.AuditResults(audit_object)
    audit_results.calculate_values()
    missing_items = audit_results.missing_items
    extra_items = audit_results.extra_items
    sku_process_result = []

    if action_missing == 'DOWNLOAD' and action_extra == 'LOAD':
        sku_process_result = process_inventory(items=extra_items, value=True, customer=audit_object.customer,
                                               location=audit_object.location, integration_config=integration_config)
        sku_process_result += process_inventory(items=missing_items, value=False, customer=audit_object.customer,
                                                location=audit_object.location, integration_config=integration_config)
    elif action_missing == 'DOWNLOAD' and action_extra == 'IGNORE':
        sku_process_result = process_inventory(items=missing_items, value=False, customer=audit_object.customer,
                                               location=audit_object.location, integration_config=integration_config)
    elif action_missing == 'IGNORE' and action_extra == 'LOAD':
        sku_process_result = process_inventory(items=extra_items, value=True, customer=audit_object.customer,
                                               location=audit_object.location, integration_config=integration_config)
    return sku_process_result


def process_set_inventory(location, customer, integration_config):
    location_ids = inventory_utils.get_location_tree(location_id=location.id)
    list_to_zero = Item.objects.filter(current_location_id__in=location_ids, customer=customer).values_list('id',
                                                                                                            flat=True)

    sku_process_zero = process_inventory_audits(items=list_to_zero, value=True, customer=customer, location=location,
                                                integration_config=integration_config, set_process=True, empty=True)

    list_to_present = Item.objects.filter(current_location_id__in=location_ids, state='PRESENT',
                                          customer=customer).values_list('id', flat=True)

    sku_process_result = process_inventory_audits(items=list_to_present, value=True, customer=customer,
                                                  location=location,
                                                  integration_config=integration_config, set_process=True, empty=False)
    return sku_process_result


def generate_file(sku_process_result, audit_object):
    abs_path = os.path.abspath(os.path.dirname(__file__))
    file_name = "audit_{}_refresh_skus_file.csv".format(audit_object.pk)
    path = os.path.join(abs_path, "../../inventory/tmp/" + file_name)
    audit_items_file_tmp = open(path, 'w')
    writer = csv.writer(audit_items_file_tmp)

    for result in sku_process_result:
        logging.info("Lines of action missing for generate file of: sku:{}, sku_external_id:{}".format(
            result['sku_id'], result['sku_external_id']))
        writer.writerow([
            result['sku_id'],
            result['sku_external_id'],
            result['amount'],
            result['result'],
        ])

    audit_items_file_tmp.close()
    summary_file = open(path, "rb")
    file_name = "audit_{}_adjust_items_file.csv".format(audit_object.pk)
    audit_object.refresh_inventory_file.save(file_name, File(summary_file))
    os.remove(path)

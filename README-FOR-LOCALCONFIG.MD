# 🛠️ Configuración de entorno local

## Requisitos previos

- Python  3.10
- Git (haber clonado el repositorio)
- WSL2 (solo si usas Windows)

---

## 1. Crear entorno virtual de Python

Asegúrate de tener Python 3.10 instalado.

```bash
# Crear el entorno virtual
python -m venv venv

# Activar el entorno virtual
source venv/bin/activate

# Instalar dependencias del proyecto
pip install -r requirements.txt
```
<hr>

## 2. Instalar Docker y Docker Compose:

### Instalar dependencias necesarias
```bash
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common
```
### Agregar la clave GPG oficial de Docker
```bash
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker.gpg
```
### Agregar el repositorio de Docker
```bash
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | \
sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
```
### Instalar Docker Engine
```bash
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io
```
<hr>

## 3. Configurar el docker y la base de datos:
   - Ir a la carpeta "docker_postgres_local" para continuar con la configuración del contenedor de la base de datos(README.md).
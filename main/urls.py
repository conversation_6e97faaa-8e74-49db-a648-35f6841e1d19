"""stock_management URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/1.8/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  url(r'^$', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  url(r'^$', Home.as_view(), name='home')
Including another URLconf
    1. Add an import:  from blog import urls as blog_urls
    2. Add a URL to urlpatterns:  url(r'^blog/', include(blog_urls))
"""
from drf_yasg import openapi


from django.conf import settings
#from django.conf.urls import include
from django.urls import include, re_path, path
from django.contrib import admin
#from rest_framework_swagger.views import get_swagger_view
from drf_yasg.views import get_schema_view
from rest_framework import permissions
from common.views import server_check_view, worker_check_view

urlpatterns = [
    path('admin/', admin.site.urls),
    path('auth/', include('djoser.urls.base')),
    path('auth/', include('djoser.urls.authtoken')),
    path('admin/dynamic_raw_id/', include('dynamic_raw_id.urls')),
    path('server/check/', server_check_view),
    path('worker/check/', worker_check_view),
]

# Load Kong enabled modules dynamic
for kong_module in settings.APP_MODULES:
    # TODO: Agregar LOGS
    module = __import__(kong_module, globals(), locals(), ['urls'], 0)
    urls = module.urls
    urlpatterns.append(path('{0}/'.format(kong_module), include(urls.router.urls)))

# Swagger URLs
schema_view = get_schema_view(
   openapi.Info(
      title="Kong Core API",
      default_version='v2',
      description="Test description",
      terms_of_service="https://www.google.com/policies/terms/",
      contact=openapi.Contact(email="<EMAIL>"),
      license=openapi.License(name="BSD License"),
   ),
   public=True,
   permission_classes=(permissions.AllowAny,),
)
urlpatterns.extend([
    path('api/docs/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('api/docs/redoc', schema_view.with_ui('redoc', cache_timeout=0), name='schema-swagger-ui'),
])

# DRF URLs
urlpatterns.extend([
    path('api-auth/', include('rest_framework.urls', namespace='rest_framework'))
])


# DEBUG URLS
if settings.DEBUG:
    from django.conf.urls.static import static
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)


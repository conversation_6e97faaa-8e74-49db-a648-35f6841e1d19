from __future__ import absolute_import, unicode_literals
import os
from celery import Celery, signals

# os.environ.setdefault("DJANGO_SETTINGS_MODULE", "main.conf.local")

app = Celery('main')
app.config_from_object('django.conf:settings', namespace='CELERY')
app.autodiscover_tasks()


# Workaround to  make  sentry  work with  Celery
# Taken from: https://github.com/getsentry/raven-python/issues/922
@signals.setup_logging.connect
def setup_logging(**kwargs):
    """Setup logging."""
    pass


@app.task(bind=True)
def debug_task(self):
    print('Request: {0!r}'.format(self.request))

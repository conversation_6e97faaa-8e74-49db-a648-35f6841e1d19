
from .common import *

TESTING = sys.argv[1:2] == ['test']

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True
DATABASE_HOST = config.get_config('DB_HOST')
DATABSE_PORT = config.get_config('DB_PORT')
DATABASE_NAME = config.get_config('DB_NAME')
DATABASE_USER = config.get_config('DB_USER')
DATABASE_PASSWORD = config.get_config('DB_PASSWORD')

if TESTING:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': 'test_kong_core',
            'USER': 'kong_dev',
            'PASSWORD': 'kong_dev$',
            'HOST': 'localhost',
            'PORT': '5432',
            'TEST': {
                'NAME': 'test_kong_core',
            }

        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': DATABASE_NAME,
            'USER': DATABASE_USER,
            'PASSWORD':DATABASE_PASSWORD,
            'HOST': DATABASE_HOST,
            'PORT': DATABSE_PORT,
        }
    }


# SWAGGER settings.
SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'basic': {
            'type': 'basic'
        }
    },
    'USE_SESSION_AUTH': True
}

# Logging Config
LOGGING['handlers'] = {
    'console': {
        'level': 'DEBUG',
        'class': 'logging.StreamHandler',
        'formatter': 'console'
    },
}

# DRF Auth config

LOGIN_URL = 'rest_framework:login'
LOGOUT_URL = 'rest_framework:logout'

# Static files (CSS, JavaScript, Images)
STATIC_ROOT = os.path.join(BASE_DIR, "static")
STATIC_URL = '/static/'

# Media files and uploads

MEDIA_ROOT = os.path.join(BASE_DIR, "media")
MEDIA_URL = '/media/'

# AWS STORAGE CONFIG
AWS_STORAGE_BUCKET_NAME = config.get_config('AWS_STORAGE_BUCKET_NAME', None)
AWS_STATIC_LOCATION = 'static'
AWS_PUBLIC_MEDIA_LOCATION = 'media/public'
AWS_PRIVATE_MEDIA_LOCATION = 'media/private'


if AWS_STORAGE_BUCKET_NAME:
    AWS_QUERYSTRING_AUTH = False
    AWS_S3_CUSTOM_DOMAIN = '%s.s3.amazonaws.com' % AWS_STORAGE_BUCKET_NAME
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',
    }
    STATIC_URL = 'https://%s/%s/' % (AWS_S3_CUSTOM_DOMAIN, AWS_STATIC_LOCATION)

    DEFAULT_FILE_STORAGE = 'common.storage_backends.PublicMediaStorage'
    PRIVATE_FILE_STORAGE = 'common.storage_backends.PrivateMediaStorage'
    STATICFILES_STORAGE = 'common.storage_backends.StaticStorage'

if 'test' in sys.argv:
    CELERY_TASK_ALWAYS_EAGER = True
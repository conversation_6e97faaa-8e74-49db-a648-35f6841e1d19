import json
import os

from django.core.exceptions import ImproperlyConfigured

with open(os.environ.get('KONG_SETTINGS_MODULE')) as f:
    configs = json.loads(f.read())


def get_config(setting, default=None, configs=configs):
    try:
        val = configs[setting]
        if val == 'True':
            val = True
        elif val == 'False':
            val = False
        elif val == 'None':
            val = None
        return val
    except KeyError:
        if default is not None:
            return default
        error_msg = "ImproperlyConfigured: Set {0} environment variable".format(setting)
        raise ImproperlyConfigured(error_msg)

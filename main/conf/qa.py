from .common import *

DEBUG = True

#Database Config
DATABASE_HOST = config.get_config('DB_HOST')
DATABSE_PORT = config.get_config('DB_PORT')
DATABASE_NAME = config.get_config('DB_NAME')
DATABASE_USER = config.get_config('DB_USER')
DATABASE_PASSWORD = config.get_config('DB_PASSWORD')

DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': DATABASE_NAME,
            'USER': DATABASE_USER,
            'PASSWORD':DATABASE_PASSWORD,
            'HOST': DATABASE_HOST,
            'PORT': DATABSE_PORT,
        }
    }

# SWAGGER settings.
SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'basic': {
            'type': 'basic'
        }
    },
    'USE_SESSION_AUTH': True
}

# Logging Config
LOGGING['handlers'] = {
    'console': {
        'level': 'INFO',
        'class': 'logging.StreamHandler',
        'formatter': 'console'
    },
}


# DRF Auth config
LOGIN_URL = 'rest_framework:login'
LOGOUT_URL = 'rest_framework:logout'

# SSL HTTPS Config
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_SSL_REDIRECT = False
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True


# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Media files and uploads
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, "media")


# AWS STORAGE CONFIG
AWS_STORAGE_BUCKET_NAME = config.get_config('AWS_STORAGE_BUCKET_NAME', None)
AWS_STATIC_LOCATION = 'static'
AWS_PUBLIC_MEDIA_LOCATION = 'media/public'
AWS_PRIVATE_MEDIA_LOCATION = 'media/private'


if AWS_STORAGE_BUCKET_NAME:
    AWS_QUERYSTRING_AUTH = False
    AWS_S3_CUSTOM_DOMAIN = '%s.s3.amazonaws.com' % AWS_STORAGE_BUCKET_NAME
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',
    }
    STATIC_URL = 'https://%s/%s/' % (AWS_S3_CUSTOM_DOMAIN, AWS_STATIC_LOCATION)

    DEFAULT_FILE_STORAGE = 'common.storage_backends.PublicMediaStorage'
    PRIVATE_FILE_STORAGE = 'common.storage_backends.PrivateMediaStorage'
    STATICFILES_STORAGE = 'common.storage_backends.StaticStorage'



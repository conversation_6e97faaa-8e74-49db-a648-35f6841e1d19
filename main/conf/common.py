"""
Django settings for stock_management project.
Generated by 'django-admin startproject' using Django 1.8.7.
For more information on this file, see
https://docs.djangoproject.com/en/1.8/topics/settings/
For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.8/ref/settings/
"""

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
import os
import sys
import urllib.parse
from django.utils.translation import gettext_lazy as _
from kombu import Exchange, Queue
from kombu.utils.url import safequote

import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

from main.conf import base as config
broker_connection_retry_on_startup = True
TESTING = sys.argv[1:2] == ['test']
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.8/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'jux%gmrpb*3*3b+x@y_z7v!&_$wi1p%cn+o7mkhg6x*&akim0^'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = (
    'bulk_admin',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.postgres',
    'dynamic_raw_id',
    'corsheaders',
    'rest_framework',
    'rest_framework.authtoken',
    'djoser',
    'storages',
    'drf_yasg'
)

# Load Kong Enabled modules.
ALL_MODULES = ('inventory', 'operations', 'rfid', 'core_config', 'logistics', 'customers', 'integrations',
               'rfid_config', 'stock')
APP_MODULES = config.get_config('APP_MODULES', default=[])

if APP_MODULES == 'ALL':
    APP_MODULES = ALL_MODULES

# Add kong modules to installed apps
INSTALLED_APPS += tuple(APP_MODULES)

MIDDLEWARE = (
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    #'django.contrib.auth.middleware.SessionAuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.middleware.security.SecurityMiddleware',
)

ROOT_URLCONF = 'main.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# Password validation
# https://docs.djangoproject.com/en/1.10/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

#WSGI_APPLICATION = 'wsgi.application'

# Internationalization
# https://docs.djangoproject.com/en/1.8/topics/i18n/

#LANGUAGE_CODE = config.get_config('APP_LANGUAGE_CODE')
LANGUAGES = [
    ('en', _('English')),
    ('es', _('Spanish')),
]
LANGUAGE_CODE = 'es'

TIME_ZONE = config.get_config('APP_TIME_ZONE')

USE_I18N = True

USE_L10N = False

USE_TZ = True

# Customer Setting
NAMESPACE = config.get_config('APP_NAMESPACE')

# AWS configuration
AWS_ACCESS_KEY_ID = config.get_config('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = config.get_config('AWS_SECRET_ACCESS_KEY')
AWS_REGION = config.get_config('AWS_REGION')
AWS_SQS_URL = config.get_config('AWS_SQS_URL')
AWS_LAMBDA_NAME = config.get_config('AWS_LAMBDA_NAME', None)

#AWS_SQS_INTEGRATION_URL = config.get_config('AWS_SQS_INTEGRATION_URL', None)
# Audits paths configuration
AUDIT_PATH = '{}/stock-management/audits/audits/{}/'

# Rest Framework Settings
REST_FRAMEWORK = {
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'DEFAULT_PAGINATION_CLASS': 'common.pagination.StandardResultsSetPagination',
    'PAGE_SIZE': 50,
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication'
    ),
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
    ),
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',

    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.DjangoModelPermissions',
    ),
    'DATETIME_FORMAT': '%d-%m-%Y %H:%M:%S',
    'DATETIME_INPUT_FORMATS': ['%d-%m-%Y %H:%M:%S', 'iso-8601'],
    'DATE_FORMAT': '%d-%m-%Y',
    'DATE_INPUT_FORMATS': ['%d-%m-%Y', 'iso-8601'],
    'TEST_REQUEST_DEFAULT_FORMAT': 'json',
    'EXCEPTION_HANDLER': 'rest_framework.views.exception_handler'
}
CORS_ORIGIN_ALLOW_ALL = True

# Logging Config
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,

    'formatters': {
        'console': {
            'format': '[%(asctime)s][%(levelname)s] %(name)s '
                      '%(filename)s:%(funcName)s:%(lineno)d | %(message)s',
            'datefmt': '%d-%m-%Y %H:%M:%S',
        },
    },

    'loggers': {
        'django': {
            'level': 'DEBUG',
            'handlers': ['console'],
            'propagate': False,
        },
        '': {
            'level': 'DEBUG',
            'handlers': ['console'],
            'propagate': False,
        },
        'celery': {
            'level': 'WARNING',
            'handlers': ['console'],
            'propagate': False,
        },
        'core.handlers': {
            'level': 'DEBUG',
            'handlers': ['console']
        }
    }
}

# Logs monitoring config
sentry_dsn = config.get_config("SENTRY_DSN", "")
if sentry_dsn:
    sentry_sdk.init(
        dsn=sentry_dsn,
        integrations=[DjangoIntegration()],

        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        traces_sample_rate=1.0,

        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True
    )

# Rabbit
# BROKER_URL_STRING = "amqp://{user}:{password}@{hostname}/{vhost}"
# RABBIT_HOST = config.get_config('RABBIT_HOST')
# RABBIT_PORT = config.get_config('RABBIT_PORT', default=5126)

# RABBIT_HOSTNAME = "{}:{}".format(RABBIT_HOST, RABBIT_PORT)

# RABBIT_DEFAULT_USER = config.get_config('RABBIT_DEFAULT_USER')
# RABBIT_DEFAULT_PASS = config.get_config('RABBIT_DEFAULT_PASS')
# RABBIT_ENV_VHOST = config.get_config('RABBIT_ENV_VHOST')


# Celery Config
#from kombu import Connection

CELERY_BROKER_URL = "sqs://{aws_access_key}:{aws_secret_key}@".format(
    aws_access_key=safequote(AWS_ACCESS_KEY_ID),
    aws_secret_key=safequote(AWS_SECRET_ACCESS_KEY),
)
#CELERY_BROKER_URL = "sqs://"
#conn = Connection(CELERY_BROKER_URL)

CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ENABLE_UTC = False
CELERY_TIMEZONE = TIME_ZONE
CELERYD_HIJACK_ROOT_LOGGER = False

CELERY_BROKER_HEARTBEAT = 30


# Extra Config
import boto3
session = boto3.Session(
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    region_name="us-east-1"
)
CELERY_BROKER_TRANSPORT_OPTIONS = {
    'region': 'us-east-1',
    "visibility_timeout": 3 * 60 * 60,
    'predefined_queues': {
        'celery': {
            'url': AWS_SQS_URL,
            'access_key_id': AWS_ACCESS_KEY_ID,
            'secret_access_key': AWS_SECRET_ACCESS_KEY,
        }
    }
}
sqs_client = session.client("sqs", region_name="us-east-1")
CELERY_TASK_ALWAYS_EAGER = config.get_config('APP_EAGER_TASKS', default=False)

# This field defines if use keys privates auto generated or provided by customer.
USE_CUSTOMER_IDS = config.get_config('APP_USE_CUSTOMER_IDS', default=False)

# Remove company prefix by app.
CUSTOMER_COMPANY_PREFIX = config.get_config('CUSTOMER_COMPANY_PREFIX', default=None)

# Company prefix by customer in app.
METADATA_COMPANY_PREFIX = config.get_config('APP_COMPANY_PREFIX_METADATA', default=False)

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

SESSION_COOKIE_AGE = 60 * 60 * 8  # 8 horas

# Renovar tiempo de sesión con cada request
SESSION_SAVE_EVERY_REQUEST = True

# Asegura que la sesión no se borre al cerrar el navegador
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# -*- coding: utf-8 -*-
from django_filters import rest_framework as filters

from common.filters import BaseFilter
from .models import *


class WarehouseFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Warehouse
        fields = ['external_id']


class OlpnFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Olpn
        fields = ['shipment_id', 'external_id']


class ShipmentFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Shipment
        fields = ['external_id', 'source_id', 'destination_id', 'customer_id']


class ShipmentLineFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = ShipmentLine
        fields = ['shipment_id', 'ean']


class PackageFilter(BaseFilter, filters.FilterSet):
    epc = filters.CharFilter(field_name='properties__epc', lookup_expr='exact')
    epcs = filters.BaseInFilter(field_name='properties__epc')

    class Meta:
        model = Package
        fields = ['external_id', 'shipment_id', 'status', 'type', 'location_id',
                  'customer_id', 'epc']


class PackageLineFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = PackageLine
        fields = ['package_id', 'ean', 'status']


class PrintOrderStockLabelFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = PrintOrderStockLabel
        fields = ['external_id', 'status', 'template_label_id', 'location_id', 'printed_by_id', 'batch_number',
                  'batch_type', 'customer_id']


class PrintOrderLineStockLabelFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = PrintOrderLineStockLabel
        fields = ['print_order_label_id', 'package_id']

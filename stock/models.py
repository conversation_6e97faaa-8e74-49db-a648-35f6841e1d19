# -*- coding: utf-8 -*-
from __future__ import unicode_literals
import functools
from django.db.models import Sum

from common.models import *
from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>ield
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from simple_history.models import HistoricalRecords
from inventory.models import Item, Location, SKU, Customer
from logistics.models import PrintTemplateLabel
from rfid.exceptions import StatusUnavailableForPrint
from django.core.validators import MinValueValidator
from operations.models import Contact


class Warehouse(BaseModel):
    name = models.CharField(max_length=50)
    display_name = models.CharField(max_length=100)
    external_id = models.CharField(max_length=15, null=True, blank=True, unique=True)
    description = models.CharField(max_length=100, null=True, blank=True)
    address = models.CharField(max_length=100, null=True, blank=True)
    latitude = models.DecimalField(max_digits=8, decimal_places=4, null=True, blank=True)
    longitude = models.DecimalField(max_digits=8, decimal_places=4, null=True, blank=True)

    def __str__(self):
        return self.display_name

    class Meta:
        ordering = ['-id']


class Shipment(BaseModel):
    STATUS = (
        ('CREATED', 'Created'),
        ("INCOMPLETE", "Incomplete"),
        ("COMPLETE", "Complete"),
        ("ISSUED", "Issued"),
        ('VERIFIED', 'Verified'),
        ("CLOSED", "Closed"),
        ("DISPATCH", "Dispatch"),
        ("CANCELED", "Canceled")
    )
    external_id = models.CharField(max_length=50, null=True, blank=True, unique=True)
    order_id = models.CharField(max_length=50, null=True, blank=True)
    wave = models.CharField(max_length=50, null=True, blank=True)
    status = models.CharField(max_length=15, choices=STATUS, default='CREATED')
    expected_arrival_date = models.DateField(null=True)
    arrival_date = models.DateField(null=True)
    consolidation_gate_number = models.CharField(max_length=50, blank=True, null=True)
    source = models.ForeignKey(Location, null=True, blank=True, related_name='outgoing_shipments', on_delete=models.PROTECT)
    destination = models.ForeignKey(Location, null=True, blank=True, related_name='incoming_shipments', on_delete=models.PROTECT)
    total_expected_items = models.IntegerField(null=True, blank=True)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    client = models.ForeignKey(Contact, null=True, blank=True, on_delete=models.PROTECT)
    properties = HStoreField(null=True, blank=True, default=dict)  # bill_name, bill_address, bill_city
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return "{}-{}".format(self.pk, self.external_id)


class ShipmentLine(BaseModel):
    shipment = models.ForeignKey(Shipment, related_name='lines', on_delete=models.CASCADE)
    ean = models.CharField(max_length=40)
    amount = models.IntegerField()
    amount_verified = models.IntegerField(null=True, blank=True, default=0)
    properties = HStoreField(null=True, blank=True)
    sku_properties = HStoreField(null=True, blank=True, default=dict)
    status = models.CharField(max_length=15, choices=Shipment.STATUS, default='CREATED')
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return "{}-{}".format(self.shipment_id, self.ean)


class Olpn(BaseModel):
    external_id = models.CharField(max_length=50, null=True, blank=True, unique=True)
    shipment = models.ForeignKey(Shipment, related_name='olpns', null=True, default=True, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    @property
    def item_list(self):
        items_id_list = Item.objects.filter(olpn=self).exclude(state='INACTIVE').values_list('id', flat=True)
        return list(items_id_list)

    def __str__(self):
        return "{}-{}".format(self.pk, self.external_id)


class Package(BaseModel):
    TYPE = (
        ('CONTAINER', 'Container'),
    )
    STATUS = (
        ('CREATED', 'Created'),
        ("INCOMPLETE", "Incomplete"),
        ("COMPLETE", "Complete"),
        ("PROCESSING", "Processing"),
        ('VERIFIED', 'Verified'),
        ('DISPATCHED', 'Dispatched'),
        ("CLOSED", "Closed"),
        ("CANCELED", "Canceled"),
        ("DELETED", "Deleted")
    )
    external_id = models.CharField(max_length=40, null=True, blank=True, unique=True)
    shipment = models.ForeignKey(Shipment, blank=True, null=True, on_delete=models.PROTECT)
    type = models.CharField(max_length=15, choices=TYPE, default='CONTAINER')
    status = models.CharField(max_length=15, choices=STATUS, default='CREATED')
    location = models.ForeignKey(Location, null=True, blank=True, on_delete=models.PROTECT)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    properties = HStoreField(null=True, blank=True, default=dict)
    params = JSONField(null=True, blank=True, default=dict)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return "{}-{}".format(self.id, self.type)

    @property
    def items_list(self):
        lines = PackageLine.objects.filter(package=self)
        items = [line.items_list for line in lines]
        try:  # unify all items lists in only one
            return functools.reduce(lambda current_list, next_list: current_list + next_list, items)
        except TypeError:
            return []

    @property
    def total_amount(self):
        sum_calculation = PackageLine.objects.filter(package=self).aggregate(total_amount=Sum('amount'))
        return sum_calculation.get('total_amount') or 0


class PackageLine(BaseModel):
    package = models.ForeignKey(Package, related_name='lines', on_delete=models.CASCADE)
    ean = models.CharField(max_length=40)
    amount = models.IntegerField()
    amount_verified = models.IntegerField(null=True, blank=True, default=0)
    status = models.CharField(max_length=15, choices=Package.STATUS, default='CREATED')
    items = JSONField(null=True, blank=True, default=dict([("items", [])]))  # {"items":["456786", "56723"]}
    properties = HStoreField(null=True, blank=True, default=dict)
    sku_properties = HStoreField(null=True, blank=True, default=dict)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return "{}-{}-{}".format(self.id, self.ean, self.amount)

    @property
    def items_list(self):
        if self.items:
            return self.items["items"]
        else:
            return []


class PrintOrderStockLabel(BaseModel):
    PRINT_STATUS = (
        ("ISSUED", "Issued"),
        ("IN_PROCESS", "In Process"),
        ("PRINTED", "Printed"),
        ("ABORTED", "Aborted")
    )
    BATCH_TYPE_LABEL = (
        ("SHIPMENT", "Shipment"),
        ("PACKAGE", "Package")
    )
    external_id = models.CharField(max_length=30, null=True, blank=True, unique=True)
    status = models.CharField(max_length=20, choices=PRINT_STATUS, default="ISSUED")
    prefix = models.CharField(max_length=3, default='POL')
    template_label = models.ForeignKey(PrintTemplateLabel, null=True, blank=True, on_delete=models.PROTECT)
    location = models.ForeignKey(Location, on_delete=models.PROTECT)
    batch_number = models.CharField(max_length=100, null=True, blank=True)
    batch_type = models.CharField(max_length=20, choices=BATCH_TYPE_LABEL, default="SHIPMENT")
    total_label_used = models.IntegerField(default=0, null=True, blank=True)
    printed_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.PROTECT)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.prefix, self.pk)

    def init_print(self, user):
        self.printed_by = user
        self.status = "IN_PROCESS"
        self.save()

    def finish_print(self, total_label_used):
        if self.status == "IN_PROCESS":
            self.status = "PRINTED"
            self.total_label_used = total_label_used
            self.save()
        else:
            raise StatusUnavailableForPrint

    def aborted_print(self):
        self.status = "ABORTED"
        self.save()


class PrintOrderLineStockLabel(BaseModel):
    print_order_label = models.ForeignKey(PrintOrderStockLabel, on_delete=models.PROTECT)
    shipment = models.ForeignKey(Shipment, null=True, blank=True, on_delete=models.PROTECT)
    package = models.ForeignKey(Package, null=True, blank=True, on_delete=models.PROTECT)
    initial_serial = models.BigIntegerField(null=True, blank=True, default=-1)
    amount = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.pk, self.print_order_label)

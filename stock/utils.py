# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from collections import defaultdict

from .models import Item

SKU_CRITERIA = "sku"
STATE_CRITERIA = "state"


def sort_items_by_sku(items_ids):
    item_objects = Item.objects.filter(pk__in=items_ids).exclude(state='INACTIVE')
    skus_dict = defaultdict(list)
    for item in item_objects:
        skus_dict[item.sku].append(item.pk)

    result = [{'sku': sku, 'items': items} for sku, items in skus_dict.items()]
    return result


def sort_items_by_state(items_ids):
    item_objects = Item.objects.filter(pk__in=items_ids).exclude(state='INACTIVE')
    states_dict = defaultdict(list)
    for item in item_objects:
        states_dict[item.state].append(item.pk)
    result = [{'state': sku, 'items': items} for sku, items in states_dict.items()]
    return result


def sort_items(criteria, items_ids):
    results = {}
    sorted_items = {}
    if criteria == STATE_CRITERIA:
        sorted_items = sort_items_by_state(items_ids=items_ids)
    elif criteria == SKU_CRITERIA:
        sorted_items = sort_items_by_sku(items_ids=items_ids)

    results['criteria'] = criteria
    results['results'] = sorted_items
    return results

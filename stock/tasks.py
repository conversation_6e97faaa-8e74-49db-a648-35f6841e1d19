# -*- coding: utf-8 -*-

from __future__ import unicode_literals

import logging

from celery import shared_task
from .models import Shipment
from .business_logic import shipment as shipment_logic
from .business_logic import olpn as olpn_logic

from .integrations import sqs


@shared_task(bind=True)
def process_transfer_order_message(self, message):
    """
    Executes transfer order message
    """
    logging.info("Processing message:%s" % message)
    shipment = shipment_logic.insert_shipment_from_message(message)
    sqs.notify_shipment(shipment)


@shared_task(bind=True)
def process_remove_packing_shipment(self, shipment_id, packings):
    """
    Executes remove packing of shipment
    """
    logging.warning("Processing shipment:%s" % shipment_id)
    shipment_object = Shipment.objects.get(external_id=shipment_id)
    olpn_logic.remove_shipment_packing(shipment_object=shipment_object, packings=packings)
    sqs.notify_shipment(shipment_object)
    return True


@shared_task(bind=True)
def process_add_olpn_shipment(self, shipment_id, packings):
    """
    Executes remove olpn of shipment
    """
    logging.warning("Processing shipment to update:%s" % shipment_id)
    shipment_object = Shipment.objects.get(external_id=shipment_id)

    process = olpn_logic.add_shipment_packing(shipment_object=shipment_object, packings=packings)
    sqs.notify_shipment(shipment_object)
    return process

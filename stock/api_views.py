# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import transaction
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets, status, schemas, mixins
from rest_framework.decorators import action
from rest_framework.filters import Ordering<PERSON><PERSON><PERSON>, SearchFilter
from rest_framework.response import Response
from rest_framework.permissions import *
from rest_framework.views import APIView

from common.schemas import DefaultViewSchema
from common.serializers import EmptySerializer
from common.pagination import StandardResultsSetPagination
from .tasks import *
from .schemas import CheckShipmentViewSchema, CheckPackageViewSchema, PackageViewSchema
from .serializers import *
from .filters import *
from core_config.utils import get_user_config
import integrations.tasks as integration_tasks
import integrations.utils as integration_utils
from stock.business_logic import package, shipment, print_order_stock_labels


class WarehouseViewSet(viewsets.ModelViewSet):
    """
    retrieve:
    Return the given warehouse.

    list:
    Return a list of all the existing warehouses.

    create:
    Create a new warehouse instance.
    
    update:
    Update an existing warehouse instance.
    
    partial_update:
    Update partial an existing warehouse instance.
    
    delete:
    Delete  an existing warehouse instance.
    """
    queryset = Warehouse.objects.all()
    serializer_class = WarehouseSerializer
    filter_class = WarehouseFilter


class TransferOrderView(APIView):
    """
        Registers Transfer Order Message
    """

    permission_classes = (IsAuthenticated,)
    schema = schemas.AutoSchema()

    def get_serializer(self):
        return TransferOrderSerializer()

    def post(self, request):
        serializer = TransferOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        action = serializer.data['action']
        shipment_id = serializer.data['shipment_id']
        packings = serializer.data['packings']

        if action == 'LOAD':
            process_transfer_order_message(request.data)
            result = process_add_olpn_shipment(shipment_id, packings)
            serializer = StringListSerializer({"results": result})
            return Response(serializer.data)
        if action == 'UNLOAD':
            process_remove_packing_shipment(shipment_id, packings)
        return Response(serializer.data, status=status.HTTP_202_ACCEPTED)


class ShipmentViewSet(viewsets.ModelViewSet):
    """
    retrieve:
    Return the given shipment instance.
    list:
    Return a list of all the existing shipment.
    create:
    Create a new shipment instance.
    update:
    Update an existing shipment instance.
    partial_update:
    Update partial an existing shipment instance.
    delete:
    Delete  an existing shipment instance.
    """
    queryset = Shipment.objects.all()
    serializer_class = ShipmentSerializer
    pagination_class = StandardResultsSetPagination
    filter_class = ShipmentFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines', None)
        user_config = get_user_config(request.user.pk)
        if not serializer.validated_data.get('source_id'):
            serializer.validated_data['source_id'] = user_config.default_location_id
        if 'customer_id' in serializer.validated_data:
            shipment_object = serializer.save()
        else:
            customer_id = user_config.customer_id
            shipment_object = serializer.save(customer_id=customer_id)
        if lines:
            shipment.create_shipment_lines(shipment=shipment_object, lines=lines)
        shipment.update_total_expected_items(shipment_id=shipment_object.pk,
                                             reporter_id=request.user.pk)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(methods=['post'], detail=True, serializer_class=EmptySerializer, schema=CheckShipmentViewSchema())
    def check_shipment(self, request, pk=None):
        shipment_object = Shipment.objects.get(pk=pk)
        serializer = EmptySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user

        dispatch = request.query_params.get('dispatch')

        if dispatch == 'true':
            shipment.dispatch_shipment(shipment=shipment_object, user=user)
        else:
            shipment.check_shipment(shipment=shipment_object, user=user)

        logging.warning('Check Package: {}, Status: {}'.format(shipment_object.pk, shipment_object.status))
        return Response(status=status.HTTP_202_ACCEPTED)

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists Move History
        """
        shipment = Shipment.objects.get(pk=pk)
        query = shipment.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = ShipmentNestedSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ShipmentNestedSerializer(query, many=True)
        return Response(serializer.data)

    @action(methods=['get'], detail=True)
    def lines(self, request, pk=None):
        query = ShipmentLine.objects.filter(shipment_id=pk)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = ShipmentLineNestedSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ShipmentLineNestedSerializer(query, many=True)
        return Response(serializer.data)


class ShipmentLineViewSet(viewsets.ModelViewSet):
    """
    retrieve:
    Return the given shipment line instance.
    list:
    Return a list of all the existing shipment lines.
    create:
    Create a new shipment line instance.
    update:
    Update an existing shipment line instance.
    partial_update:
    Update partial an existing shipment line instance.
    delete:
    Delete  an existing shipment line instance.
    """
    queryset = ShipmentLine.objects.all()
    serializer_class = ShipmentLineSerializer
    filter_class = ShipmentLineFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        shipment_line = serializer.save()
        shipment.update_total_expected_items(shipment_id=shipment_line.shipment_id,
                                             reporter_id=request.user.pk)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        shipment_line = serializer.save()
        shipment.update_total_expected_items(shipment_id=shipment_line.shipment_id,
                                             reporter_id=request.user.pk)
        return Response(serializer.data)


class OlpnViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Olpn.objects.all()
    serializer_class = OlpnHistorySerializer
    filter_class = OlpnFilter

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists Olpn History
        """
        olpn_object = self.get_object()
        olpns = Olpn.objects.get(pk=olpn_object.pk)
        query = olpns.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = OlpnHistorySerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = OlpnHistorySerializer(query, many=True)
        return Response(serializer.data)


class PackageViewSet(viewsets.ModelViewSet):
    """
       retrieve:
       Return the given package.
       list:
       Return a list of all the existing packages.
       create:
       Create a new package instance.
       update:
       Update an existing package instance.
       partial_update:
       Update partial an existing package instance.
       delete:
       Delete  an existing package instance.
    """
    __basic_fields = ('external_id', 'id')
    queryset = Package.objects.all()
    serializer_class = PackageSerializer
    filter_class = PackageFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'
    search_fields = __basic_fields
    schema = PackageViewSchema()

    def get_queryset(self):
        if self.action != 'create':
            self.queryset = self.queryset.exclude(status="DELETED")
        return super(PackageViewSet, self).get_queryset()

    def list(self, request, *args, **kwargs):
        if "customer_id" in request.GET:
            customer_id = request.GET["customer_id"]
        else:
            user_config = get_user_config(request.user.pk)
            customer_id = user_config.customer_id
        module_config = integration_utils.verified_integration_module(
            customer_id=customer_id, kong_module__name="STOCK_PACKAGE_LIST",
            active_integration=True)
        if module_config:
            package_ids = list(self.filter_queryset(self.get_queryset()).values_list("id", flat=True))
            request_body = {
                "detail-type": "STOCK_PACKAGE_LIST",
                "package_ids": package_ids,
                "reporter_username": request.user.username
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_config.id, body=request_body, async_request=False)
        return super(PackageViewSet, self).list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines', None)

        # Validation for items
        with transaction.atomic():
            external_id = serializer.validated_data.get('external_id')
            epc = serializer.validated_data.get('properties', {}).get('epc')
            force_epcs = serializer.validated_data.pop('force_epcs', ())
            packages = self.queryset.filter(external_id=external_id)
            force_destroy = False
            return_epc = False
            return_extra_epcs = []
            if 'customer_id' in serializer.validated_data:
                customer_id = serializer.validated_data['customer_id']
            else:
                user_config = get_user_config(request.user.pk)
                customer_id = user_config.customer_id
            if packages or force_epcs:
                for package_object in packages:
                    force_destroy = True
                    if package_object.properties and epc == package_object.properties.get('epc'):
                        return_epc = True
                    elif package_object.properties and package_object.properties.get('epc')\
                            and package_object.status in ("DISPATCHED", "VERIFIED"):
                        return_extra_epcs.append(package_object.properties['epc'])
                package.destroy_packages(packages=packages, reporter=request.user, customer_id=customer_id,
                                         force_destroy=force_destroy, force_epcs=force_epcs)
            package_object = serializer.save(customer_id=customer_id)
            if lines:
                package.create_lines_from_array(package=package_object, lines=lines)
                logging.debug("Packing following lines in pack {}".format(lines, package_object.pk))
            package.process_epc_association(package_object=package_object, reporter=request.user)

        module_config = integration_utils.verified_integration_module(
            customer_id=package_object.customer_id, kong_module__name="STOCK_PACKAGE_CREATE",
            active_integration=True)
        if module_config:
            request_body = {
                "detail-type": "STOCK_PACKAGE_CREATE",
                "package_id": package_object.id,
                "return_epc": return_epc,
                "return_extra_epcs": return_extra_epcs
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_config.id, body=request_body)

        headers = self.get_success_headers(serializer.data)
        serializer = self.get_serializer(package_object)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(methods=['post'], detail=True, serializer_class=EmptySerializer, schema=CheckPackageViewSchema())
    def check_package(self, request, pk=None):
        package_object = self.get_object()
        serializer = EmptySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = request.user

        dispatch = request.query_params.get('dispatch')

        if dispatch == 'true':
            package.dispatch_package(package=package_object, user=user)
        else:
            package.check_package(package=package_object, user=user)

        logging.warning('Check Package: {}, Status: {}'.format(package_object.pk, package_object.status))
        return Response(status=status.HTTP_202_ACCEPTED)

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists package History
        """
        packages = Package.objects.get(pk=pk)
        query = packages.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = PackageNestedSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PackageNestedSerializer(query, many=True)
        return Response(serializer.data)

    @action(methods=['get'], detail=True, url_name='items')
    def items(self, request, pk=None):
        """
        Lists Package Items
        """
        package_object = self.get_object()
        serializer = StringListSerializer({"results": package_object.items_list})
        return Response(serializer.data)

    @action(methods=['get'], detail=True)
    def lines(self, request, pk=None):
        """
            Lists package Lines
        """
        query = PackageLine.objects.filter(package_id=pk)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = PackageLineSerializer(
                page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PackageLineSerializer(
            query, many=True)
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        type_return_id = request.query_params.get('type_return_id')
        instance = self.get_object()
        with transaction.atomic():
            package.destroy_packages(packages=[instance], reporter=request.user, type_return_id=type_return_id)
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(methods=['delete'], detail=False, serializer_class=PackageNestedSerializer)
    def delete(self, request):
        """
            Change packages status to DELETED from filter results, limited to pagination results
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        force_epcs = [request.query_params['epc']] if request.query_params.get('epc') else []
        force_epcs.extend(request.query_params.get('epcs', "").split())
        response_data = serializer.data.copy()
        package.destroy_packages(packages=page, reporter=request.user, force_epcs=force_epcs)
        return self.get_paginated_response(response_data)


class PackageLineViewSet(viewsets.ModelViewSet):
    """
       retrieve:
       Return the given package line.
       list:
       Return a list of all the existing package lines.
       create:
       Create a new package line instance.
       update:
       Update an existing package line instance.
       partial_update:
       Update partial an existing package line instance.
       delete:
       Delete  an existing package line instance.
    """
    queryset = PackageLine.objects.all()
    serializer_class = PackageLineSerializer
    filter_class = PackageLineFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists PackageLine History
        """
        package_lines = PackageLine.objects.get(pk=pk)
        query = package_lines.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = PackageLineSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PackageLineSerializer(query, many=True)
        return Response(serializer.data)


class PrintOrderStockLabelViewSet(viewsets.ModelViewSet):
    """
        retrieve:
        Return the given print order label.
        list:
        Return a list of all the existing print orders labels.
        create:
        Create a new print order label instance.

        update:
        Update an existing print order label instance.

        partial_update:
        Update partial an existing print order label instance.

        delete:
        Delete  an existing print order label instance.
    """
    queryset = PrintOrderStockLabel.objects.all()
    serializer_class = PrintOrderStockLabelSerializer
    filter_class = PrintOrderStockLabelFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines')

        with transaction.atomic():
            print_order_stock_label = serializer.save()
            print_order_stock_labels.create_label_lines_from_array(order=print_order_stock_label, lines=lines)
        headers = self.get_success_headers(serializer.data)
        serializer = PrintOrderStockLabelSerializer(print_order_stock_label)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(methods=['get'], detail=True)
    def label_lines(self, request, pk=None):
        """
            Lists print order LABEL Lines
        """
        query = PrintOrderLineStockLabel.objects.filter(
            print_order_label_id=pk)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = PrintOrderLineStockLabelSerializer(
                page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PrintOrderLineStockLabelSerializer(query, many=True)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=EmptySerializer,
            url_path='actions/init_print', url_name='init-print')
    def init_print(self, request, pk=None):
        print_order_stock_label = self.get_object()
        serializer = EmptySerializer()
        print_order_stock_label.init_print(request.user)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=PrintOrderStockLabelTotalSerializer,
            url_path='actions/finish_print', url_name='finish-print')
    def finish_print(self, request, pk=None):
        print_order_stock_label = self.get_object()
        serializer = PrintOrderStockLabelTotalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        total_label_used = serializer.validated_data['total_label_used']
        print_order_stock_label.finish_print(total_label_used)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=EmptySerializer,
            url_path='actions/aborted_print', url_name='aborted-print')
    def finish_aborted(self, request, pk=None):
        print_order_stock_label = self.get_object()
        print_order_stock_label.aborted_print()
        return Response(status=status.HTTP_202_ACCEPTED)


class PrintOrderLineStockLabelViewSet(viewsets.ReadOnlyModelViewSet):
    """
        retrieve:
        Return the given print order label.
        list:
        Return a list of all the existing print orders labels.
    """
    queryset = PrintOrderLineStockLabel.objects.all()
    serializer_class = PrintOrderLineStockLabelSerializer
    filter_class = PrintOrderLineStockLabelFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class StockLabelShipmentPackagesViewSet(mixins.CreateModelMixin, viewsets.GenericViewSet):
    """
    create:
    Create a new print stock label instance for each shipment with packages as lines.
    """
    serializer_class = StockLabelShipmentSerializer
    permission_classes = (IsAuthenticated,)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        shipment_ids = serializer.validated_data['shipment_ids']
        location_id = serializer.validated_data['location_id']
        template_label_id = serializer.validated_data.get('template_label_id')
        customer_id = serializer.validated_data.get('customer_id')
        if not customer_id:
            user_config = get_user_config(request.user.pk)
            customer_id = user_config.customer_id
        with transaction.atomic():
            print_orders = print_order_stock_labels.create_from_shipment_packages(
                shipment_ids=shipment_ids, location_id=location_id, template_label_id=template_label_id,
                customer_id=customer_id)
        page = self.paginate_queryset(print_orders)
        serializer = PrintOrderStockLabelSerializer(page, many=True)
        paginator = self.get_paginated_response(serializer.data)
        return Response(paginator.data, status=status.HTTP_201_CREATED)

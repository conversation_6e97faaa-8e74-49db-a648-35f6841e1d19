import coreapi
import coreschema
from rest_framework import schemas

from common.schemas import DefaultViewSchema


class CheckShipmentViewSchema(schemas.AutoSchema):

    def get_manual_fields(self, path, method):
        extra_fields = [
            coreapi.Field(
                name="dispatch",
                description="Define shipment dispatched (true) or verified (false).",
                location="query",
                schema=coreschema.String(),
                required=False
            )
        ]

        manual_fields = super().get_manual_fields(path, method)
        return manual_fields + extra_fields


class CheckPackageViewSchema(schemas.AutoSchema):

    def get_manual_fields(self, path, method):
        extra_fields = [
            coreapi.Field(
                name="dispatch",
                description="Define package dispatched (true) or verified (false).",
                location="query",
                schema=coreschema.String(),
                required=False
            )
        ]

        manual_fields = super().get_manual_fields(path, method)
        return manual_fields + extra_fields


class PackageViewSchema(DefaultViewSchema):
    def get_manual_fields(self, path, method):
        extra_fields = []
        if self.view.action == 'destroy':
            extra_fields = [
                coreapi.Field(
                    name="type_return_id",
                    description="Type return from items inside package",
                    location="query",
                    schema=coreschema.String(),
                    required=False,
                    example='1',
                    type='string'
                )
            ]
        manual_fields = super().get_manual_fields(path, method)
        return manual_fields + extra_fields

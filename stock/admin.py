# -*- coding: utf-8 -*-

from django.contrib import admin
from django.apps import apps
from .models import *
from simple_history.admin import SimpleHistoryAdmin
from operations.models import Trace

# auto-register all models
app = apps.get_app_config('stock')


class WarehouseAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'display_name', 'address')


class ItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'rfid_label', 'olpn', 'state', 'verified')


class TraceAdmin(admin.ModelAdmin):
    list_display = ('id', 'item', 'location', 'reporter', 'reporter_source', 'action', 'modified')


class OlpnAdmin(admin.ModelAdmin):
    list_display = ("id", "external_id", "shipment")
    raw_id_fields = ('shipment',)


class ShipmentLineInline(admin.TabularInline):
    fields = ('ean', 'amount', 'amount_verified')
    model = ShipmentLine
    extra = 0
    show_change_link = True


class ShipmentAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'consolidation_gate_number', 'source', 'destination',
                    'total_expected_items', 'client', 'customer')
    raw_id_fields = ('source', 'destination', 'client')
    inlines = [
        ShipmentLineInline
    ]
    readonly_fields = ('created', 'modified', 'created_by', 'modified_by')


class ShipmentLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'shipment', 'ean', 'amount', 'amount_verified')
    raw_id_fields = ('shipment',)


class PackageLineInline(admin.TabularInline):
    fields = ('ean', 'amount', 'amount_verified', 'status', 'items')
    model = PackageLine
    extra = 0
    show_change_link = True


class PackageAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'shipment', 'type', 'status',
                    'location', 'customer')
    raw_id_fields = ('shipment', 'location')
    inlines = [
        PackageLineInline
    ]
    readonly_fields = ('created', 'modified', 'created_by', 'modified_by')


class PackageLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'package', 'ean', 'amount', 'amount_verified', 'status')
    raw_id_fields = ('package',)


class PrintOrderLineInLine(admin.TabularInline):
    fields = ('shipment', 'package', 'initial_serial', 'amount')
    model = PrintOrderLineStockLabel
    extra = 0
    show_change_link = True
    raw_id_fields = ('shipment', 'package')


class PrintOrderLabelAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'status', 'location', 'template_label', 'total_label_used',
                    'printed_by', 'batch_type', 'batch_number')
    raw_id_fields = ('location',)
    inlines = [
        PrintOrderLineInLine
    ]
    readonly_fields = ('created', 'modified', 'prefix')
    fieldsets = (
        ('Main Details', {
            'fields': (('created', 'modified'),
                       ('prefix', 'external_id', 'location', 'template_label'),
                       ('batch_number', 'batch_type'),
                       ('printed_by',),
                       ('total_label_used',)
                       )
        }),
        ('Print Order Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class PrintOrderLineLabelAdmin(admin.ModelAdmin):
    list_display = ('id', 'print_order_label', 'shipment', 'package', 'amount')
    raw_id_fields = ('print_order_label', 'shipment', 'package')


admin.site.register(Warehouse, WarehouseAdmin)
admin.site.register(Olpn, OlpnAdmin)
admin.site.register(Shipment, ShipmentAdmin)
admin.site.register(ShipmentLine, ShipmentLineAdmin)
admin.site.register(Package, PackageAdmin)
admin.site.register(PackageLine, PackageLineAdmin)
admin.site.register(PrintOrderStockLabel, PrintOrderLabelAdmin)
admin.site.register(PrintOrderLineStockLabel, PrintOrderLineLabelAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

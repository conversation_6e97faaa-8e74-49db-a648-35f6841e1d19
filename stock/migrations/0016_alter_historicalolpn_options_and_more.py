# Generated by Django 4.2.11 on 2025-04-20 01:09

from django.conf import settings
import django.contrib.postgres.fields.hstore
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("inventory", "0102_delete_inventorysalesreport_and_more"),
        ("logistics", "0039_alter_historicalshippingorder_options_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("operations", "0059_alter_historicalcontact_options_and_more"),
        ("customers", "0012_alter_historicalcustomer_options_and_more"),
        ("stock", "0015_auto_20211220_1425"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicalolpn",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical olpn",
                "verbose_name_plural": "historical olpns",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalpackage",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical package",
                "verbose_name_plural": "historical packages",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalpackageline",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical package line",
                "verbose_name_plural": "historical package lines",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalprintorderlinestocklabel",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical print order line stock label",
                "verbose_name_plural": "historical print order line stock labels",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalprintorderstocklabel",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical print order stock label",
                "verbose_name_plural": "historical print order stock labels",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalshipment",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical shipment",
                "verbose_name_plural": "historical shipments",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalshipmentline",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical shipment line",
                "verbose_name_plural": "historical shipment lines",
            },
        ),
        migrations.AlterField(
            model_name="historicalolpn",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalolpn",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalpackage",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalpackage",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalpackage",
            name="params",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="historicalpackage",
            name="properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="historicalpackageline",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalpackageline",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalpackageline",
            name="items",
            field=models.JSONField(blank=True, default={"items": []}, null=True),
        ),
        migrations.AlterField(
            model_name="historicalpackageline",
            name="properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="historicalpackageline",
            name="sku_properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="historicalprintorderlinestocklabel",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalprintorderlinestocklabel",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalprintorderstocklabel",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalprintorderstocklabel",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalshipment",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalshipment",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalshipment",
            name="properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="historicalshipmentline",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalshipmentline",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalshipmentline",
            name="sku_properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="olpn",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="olpn",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="olpn",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="olpn",
            name="shipment",
            field=models.ForeignKey(
                default=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="olpns",
                to="stock.shipment",
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="customers.customer",
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="location",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="inventory.location",
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="params",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="package",
            name="properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="package",
            name="shipment",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="stock.shipment",
            ),
        ),
        migrations.AlterField(
            model_name="packageline",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="packageline",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="packageline",
            name="items",
            field=models.JSONField(blank=True, default={"items": []}, null=True),
        ),
        migrations.AlterField(
            model_name="packageline",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="packageline",
            name="properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="packageline",
            name="sku_properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="printorderlinestocklabel",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printorderlinestocklabel",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="printorderlinestocklabel",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printorderlinestocklabel",
            name="package",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="stock.package",
            ),
        ),
        migrations.AlterField(
            model_name="printorderlinestocklabel",
            name="print_order_label",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="stock.printorderstocklabel",
            ),
        ),
        migrations.AlterField(
            model_name="printorderlinestocklabel",
            name="shipment",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="stock.shipment",
            ),
        ),
        migrations.AlterField(
            model_name="printorderstocklabel",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printorderstocklabel",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="customers.customer",
            ),
        ),
        migrations.AlterField(
            model_name="printorderstocklabel",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="printorderstocklabel",
            name="location",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="inventory.location"
            ),
        ),
        migrations.AlterField(
            model_name="printorderstocklabel",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printorderstocklabel",
            name="printed_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printorderstocklabel",
            name="template_label",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="logistics.printtemplatelabel",
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="client",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="operations.contact",
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="customers.customer",
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="destination",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="incoming_shipments",
                to="inventory.location",
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="shipment",
            name="source",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="outgoing_shipments",
                to="inventory.location",
            ),
        ),
        migrations.AlterField(
            model_name="shipmentline",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shipmentline",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="shipmentline",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shipmentline",
            name="sku_properties",
            field=django.contrib.postgres.fields.hstore.HStoreField(
                blank=True, default=dict, null=True
            ),
        ),
        migrations.AlterField(
            model_name="warehouse",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="warehouse",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="warehouse",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]

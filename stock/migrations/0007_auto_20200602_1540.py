# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-06-02 20:40
from __future__ import unicode_literals

import django.contrib.postgres.fields.hstore
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0006_auto_20200526_1027'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalpackageline',
            name='sku_properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalshipmentline',
            name='sku_properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='packageline',
            name='sku_properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='shipmentline',
            name='sku_properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpackageline',
            name='sku',
            field=models.CharField(max_length=40),
        ),
        migrations.AlterField(
            model_name='historicalshipmentline',
            name='sku',
            field=models.CharField(max_length=40),
        ),
        migrations.AlterField(
            model_name='packageline',
            name='sku',
            field=models.CharField(max_length=40),
        ),
        migrations.AlterField(
            model_name='shipmentline',
            name='sku',
            field=models.CharField(max_length=40),
        ),
    ]

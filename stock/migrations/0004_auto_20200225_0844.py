# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-02-25 13:44
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.hstore
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customers', '0003_historicalcustomer'),
        ('stock', '0003_auto_20190820_1044'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalPackage',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, max_length=40, null=True)),
                ('type', models.CharField(choices=[('CONTAINER', 'Container')], default='CONTAINER', max_length=15)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETE', 'Complete'), ('PROCESSING', 'Processing'), ('VERIFIED', 'Verified'), ('CLOSED', 'Closed'), ('CANCELED', 'Canceled')], default='CREATED', max_length=15)),
                ('properties', django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical package',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalPackageLine',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('amount', models.IntegerField()),
                ('amount_verified', models.IntegerField(blank=True, default=0, null=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETE', 'Complete'), ('PROCESSING', 'Processing'), ('VERIFIED', 'Verified'), ('CLOSED', 'Closed'), ('CANCELED', 'Canceled')], default='CREATED', max_length=15)),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical package line',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalShipment',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('order_id', models.CharField(blank=True, max_length=50, null=True)),
                ('wave', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETE', 'Complete'), ('ISSUED', 'Issued'), ('VERIFIED', 'Verified'), ('CLOSED', 'Closed'), ('DISPATCH', 'Dispatch'), ('CANCELED', 'Canceled')], default='CREATED', max_length=15)),
                ('expected_arrival_date', models.DateField(null=True)),
                ('arrival_date', models.DateField(null=True)),
                ('consolidation_gate_number', models.CharField(blank=True, max_length=50, null=True)),
                ('total_expected_items', models.IntegerField(blank=True, null=True)),
                ('properties', django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer')),
                ('destination', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('source', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
            ],
            options={
                'verbose_name': 'historical shipment',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalShipmentLine',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('amount', models.IntegerField()),
                ('amount_verified', models.IntegerField(blank=True, default=0, null=True)),
                ('properties', django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical shipment line',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=40, null=True)),
                ('type', models.CharField(choices=[('CONTAINER', 'Container')], default='CONTAINER', max_length=15)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETE', 'Complete'), ('PROCESSING', 'Processing'), ('VERIFIED', 'Verified'), ('CLOSED', 'Closed'), ('CANCELED', 'Canceled')], default='CREATED', max_length=15)),
                ('properties', django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='PackageLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField()),
                ('amount_verified', models.IntegerField(blank=True, default=0, null=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETE', 'Complete'), ('PROCESSING', 'Processing'), ('VERIFIED', 'Verified'), ('CLOSED', 'Closed'), ('CANCELED', 'Canceled')], default='CREATED', max_length=15)),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='stock.Package')),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='ShipmentLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField()),
                ('amount_verified', models.IntegerField(blank=True, default=0, null=True)),
                ('properties', django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.AddField(
            model_name='shipment',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='shipment',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETE', 'Complete'), ('ISSUED', 'Issued'), ('VERIFIED', 'Verified'), ('CLOSED', 'Closed'), ('DISPATCH', 'Dispatch'), ('CANCELED', 'Canceled')], default='CREATED', max_length=15),
        ),
        migrations.AddField(
            model_name='shipmentline',
            name='shipment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='stock.Shipment'),
        ),
        migrations.AddField(
            model_name='shipmentline',
            name='sku',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU'),
        ),
        migrations.AddField(
            model_name='package',
            name='shipment',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='stock.Shipment'),
        ),
        migrations.AddField(
            model_name='historicalshipmentline',
            name='shipment',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='stock.Shipment'),
        ),
        migrations.AddField(
            model_name='historicalshipmentline',
            name='sku',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKU'),
        ),
        migrations.AddField(
            model_name='historicalpackageline',
            name='package',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='stock.Package'),
        ),
        migrations.AddField(
            model_name='historicalpackageline',
            name='sku',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKU'),
        ),
        migrations.AddField(
            model_name='historicalpackage',
            name='shipment',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='stock.Shipment'),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2021-09-30 20:51
from __future__ import unicode_literals

import django.contrib.postgres.fields.hstore
import django.contrib.postgres.fields.jsonb
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('stock', '0013_auto_20210615_1941'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalpackage',
            name='params',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='package',
            name='params',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpackage',
            name='properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name='package',
            name='properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(blank=True, default={}, null=True),
        ),
    ]

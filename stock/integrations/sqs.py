# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import logging

import boto3
from django.conf import settings
from rest_framework.renderers import J<PERSON><PERSON>enderer
from ..serializers import ShipmentSerializer


def _get_sqs_session():
    session = boto3.Session(
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_REGION
    )
    sqs = session.client('sqs')
    return sqs


def _send_message(message_attrs={}, message_body={}):
    sqs = _get_sqs_session()
    queue_url = settings.AWS_SQS_URL
    # Send message to SQS queue

    response = sqs.send_message(
        QueueUrl=queue_url,
        DelaySeconds=10,
        MessageAttributes=message_attrs,
        MessageBody=message_body
    )
    return response


def notify_shipment(shipment):
    message_data = ShipmentSerializer(shipment)
    json = JSONRenderer().render(message_data.data)
    _send_message(message_body=json.decode("utf-8"))
    logging.info("Send of shipment from message:%s" % message_data)
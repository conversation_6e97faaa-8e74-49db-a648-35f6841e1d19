# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json

from stock import utils
from .models import *
from rest_framework import serializers
from common.serializers import BaseSerializer, UserNestedSerializer, StringListSerializer
from django.http import JsonResponse
from operations.models import Item, Trace
from inventory.serializers import SKUAmountSerializer, LocationNestedSerializer, SKUNestedSerializer
from inventory.models import Location
from common.models import User
from customers.serializers import CustomerNestedSerializer
from rest_framework.validators import UniqueTogetherValidator
from logistics.serializers import PrintTemplateLabelSerializer
from operations.serializers import ContactNestedSerializer


class WarehouseSerializer(BaseSerializer):
    class Meta:
        model = Warehouse
        fields = '__all__'


class WarehouseNestedSerializer(BaseSerializer):
    class Meta:
        model = Warehouse
        fields = ('external_id', 'display_name', 'id')


# Traces Serializer

class TraceSerializer(BaseSerializer):
    location = LocationNestedSerializer(read_only=True)
    location_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='location',
                                                     write_only=True)
    reporter = UserNestedSerializer(read_only=True)
    reporter_id = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), source='reporter',
                                                     write_only=True)

    class Meta:
        model = Trace
        fields = '__all__'

# Transfer Order Serializer

class TransferPackingsSerializer(serializers.Serializer):
    external_id = serializers.CharField(max_length=150)
    lines = SKUAmountSerializer(many=True, write_only=True, required=False)


class TransferOrderSerializer(serializers.Serializer):
    ACTIONS = [
        ('LOAD', 'Load'),
        ('UNLOAD', 'Unload')
    ]
    source = LocationNestedSerializer(read_only=True)
    source_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='source',
                                                   write_only=True)

    destination = LocationNestedSerializer(read_only=True)
    destination_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='destination',
                                                        write_only=True)
    wave = serializers.CharField(max_length=50, required=True)
    order_id = serializers.CharField(max_length=50, required=True)
    packings = TransferPackingsSerializer(many=True)
    properties = serializers.DictField(required=False)
    expected_arrival_date = serializers.DateField()
    shipment_id = serializers.CharField(max_length=150)
    shipment_amount = serializers.IntegerField(default=0)
    action = serializers.ChoiceField(choices=ACTIONS)
    consolidation_gate_id = serializers.CharField(max_length=50)


class ShipmentLineDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShipmentLine
        fields = ('ean', 'amount', 'properties', 'sku_properties')


# Shipment Serializer
class ShipmentSerializer(BaseSerializer):
    source = LocationNestedSerializer(read_only=True)
    source_id = serializers.IntegerField(write_only=True, required=False)
    destination = LocationNestedSerializer(read_only=True)
    destination_id = serializers.IntegerField(write_only=True, required=False)
    client = ContactNestedSerializer(read_only=True)
    client_id = serializers.IntegerField(write_only=True, required=False)
    lines = ShipmentLineDetailSerializer(many=True, write_only=True, required=False)

    class Meta:
        model = Shipment
        fields = '__all__'


class ShipmentNestedSerializer(BaseSerializer):
    source = LocationNestedSerializer(read_only=True)
    destination = LocationNestedSerializer(read_only=True)
    customer = CustomerNestedSerializer(read_only=True)
    client = ContactNestedSerializer(read_only=True)

    class Meta:
        model = Shipment
        fields = ('id', 'source', 'destination', 'external_id', 'order_id', 'wave',
                  'expected_arrival_date', 'arrival_date', 'total_expected_items',
                  'customer', 'client', 'properties')


# Shipment Line Serializer
class ShipmentLineSerializer(BaseSerializer):
    shipment = ShipmentNestedSerializer(read_only=True)
    shipment_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = ShipmentLine
        fields = '__all__'


class ShipmentLineNestedSerializer(BaseSerializer):
    shipment = ShipmentNestedSerializer(read_only=True)

    class Meta:
        model = ShipmentLine
        fields = ('shipment', 'ean', 'amount', 'status', 'amount_verified', 'properties', 'sku_properties')


# Olpns serializers

class OlpnSerializer(BaseSerializer):
    items = serializers.SerializerMethodField()

    class Meta:
        model = Olpn
        fields = '__all__'

    def get_items(self, obj):
        items = obj.item_list
        return items


class OlpnHistorySerializer(BaseSerializer):
    class Meta:
        model = Olpn
        fields = '__all__'


class OlpnNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = Olpn
        fields = ('external_id', 'shipment',)


# Item serializers


class ItemNestedSerializer(BaseSerializer):
    id = serializers.CharField(max_length=150)
    olpn = OlpnNestedSerializer(read_only=True)

    class Meta:
        model = Item
        fields = ('id', 'olpn')


# Package Serializer
class PackageLineDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = PackageLine
        fields = ('ean', 'amount', 'properties', 'sku_properties')


class PackageSerializer(BaseSerializer):
    shipment = ShipmentNestedSerializer(read_only=True)
    shipment_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    location = LocationNestedSerializer(read_only=True)
    location_id = serializers.IntegerField(write_only=True, required=False)
    customer = CustomerNestedSerializer(read_only=True)
    customer_id = serializers.IntegerField(write_only=True, required=False)
    lines = PackageLineDetailSerializer(many=True, write_only=True, required=False)
    external_id = serializers.CharField(allow_null=True, allow_blank=True)
    total_amount = serializers.IntegerField(read_only=True)

    class Meta:
        model = Package
        fields = '__all__'

    def validate(self, data):
        epc = data.get('properties', {}).get('epc')
        item_object = None
        if epc:
            item_object = Item.objects.filter(id=epc).first()
        if epc and not item_object:
            raise serializers.ValidationError(
                {"epc": ["epc with value '{}' dont exist.".format(epc)]})
        if epc and item_object.state not in ["PRESENT", "PACKED"]:
            data['force_epcs'] = (epc,)
        return data


class PackageNestedSerializer(BaseSerializer):
    shipment = ShipmentNestedSerializer(read_only=True)
    location = LocationNestedSerializer(read_only=True)

    class Meta:
        model = Package
        fields = ('id', 'external_id', 'status', 'type', 'shipment', 'location',
                  'properties', 'total_amount', 'params')


class PackageLineSerializer(BaseSerializer):
    package = PackageNestedSerializer(read_only=True)
    package_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = PackageLine
        fields = '__all__'


# Print Order Label Serializer
class PrintOrderLineStockLabelSerializer(BaseSerializer, serializers.ReadOnlyField):
    package = PackageNestedSerializer(read_only=True)

    class Meta:
        model = PrintOrderLineStockLabel
        fields = '__all__'


class PrintOrderLineStockLabelDetailSerializer(serializers.Serializer):
    shipment_id = serializers.IntegerField(write_only=True, required=False)
    package_id = serializers.IntegerField(write_only=True, required=False)
    amount = serializers.IntegerField(write_only=True)
    initial_serial = serializers.IntegerField(write_only=True, required=False)


class PrintOrderStockLabelSerializer(BaseSerializer, serializers.ModelSerializer):
    printed_by = UserNestedSerializer(read_only=True)

    template_label = PrintTemplateLabelSerializer(read_only=True)
    template_label_id = serializers.IntegerField(write_only=True, required=False)

    location = LocationNestedSerializer(read_only=True)
    location_id = serializers.IntegerField(write_only=True)

    created_by = UserNestedSerializer(read_only=True)
    modified_by = UserNestedSerializer(read_only=True)

    lines = PrintOrderLineStockLabelDetailSerializer(write_only=True, many=True, required=False)

    class Meta:
        model = PrintOrderStockLabel
        fields = '__all__'
        read_only_fields = ('batch_type', 'batch_number', 'status', 'prefix')


class PrintOrderStockLabelTotalSerializer(serializers.Serializer):
    total_label_used = serializers.IntegerField()


class StockLabelShipmentSerializer(serializers.Serializer):
    shipment_ids = serializers.ListField(child=serializers.IntegerField(), write_only=True)
    location_id = serializers.IntegerField(write_only=True)
    template_label_id = serializers.IntegerField(write_only=True, required=False)
    customer_id = serializers.IntegerField(write_only=True, required=False)

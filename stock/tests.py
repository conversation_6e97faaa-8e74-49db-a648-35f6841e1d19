"""
# -*- coding: utf-8 -*-
from __future__ import unicode_literals

# Create your tests here.
import tempfile
from mimify import File

from django.contrib.auth.models import Permission
from django.core.files.uploadedfile import SimpleUploadedFile
from django.http import request
from django.test import Client
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework.utils import json

from common.models import User
from stock.models import Warehouse, Location, SubLocation, SKUGroup, SKU, Item, Move, MoveLine, Audit, AuditLine


class InventoryMoveAPITestCase(APITestCase):
    def setUp(self):
        self.client = Client()
        self.normal_user = User.objects.create_user(
            username="monkey", password="monkey1234", email="<EMAIL>", is_staff=True
        )
        self.normal_user.save()

        self.super_user = User.objects.create_superuser(
            username="super_monkey", password="super_monkey1234", email="<EMAIL>"
        )
        self.super_user.save()
        self.warehouse_mex = Warehouse.objects.create(
            name="MAIN WAREHOUSE MEXICO",
            display_name="Main - Warehouse -MEXICO",
        )
        self.warehouse_mex.save()

        # create source location.
        self.source = Location.objects.create(
            id=1,
            name="MAIN WAREHOUSE",
            display_name="Main - Warehouse",
            type='SIMPLE',
            warehouse=self.warehouse_mex,
        )
        self.source.save()
        self.destination = Location.objects.create(
            id=2,
            name="MAIN STORE",
            display_name="Main - Store",
            type='SIMPLE'
        )
        self.destination.save()
        self.sub_location = SubLocation.objects.create(
            code='A1',
            location=self.source
        )
        self.sub_location.save()
        # create sku_group.

        self.sku_group = SKUGroup.objects.create(
            name='ZAPATOS',
        )
        self.sku_group.save()
        self.sku = SKU.objects.create(
            id=1,
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004068",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku.save()
        self.item = Item.objects.create(
            id="9202226913468bc",
            serial=1,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item.save()
        self.item_two = Item.objects.create(
            id="9202226913468bd",
            serial=2,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,

        )
        self.item_two.save()

        self.item_three = Item.objects.create(
            id="9202226913468be",
            serial=3,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item_three.save()

        self.move = Move.objects.create(
            external_id="AB4",
            source=self.source,
            destination=self.destination,
            type="",
            status=""
        )
        self.move_line = MoveLine.objects.create(
            move=self.move,
            sku=self.sku,
            amount=2,
            items={"items": [self.item.id, self.item_two.id]}
        )

        permission = Permission.objects.get(name='Can add move')
        u = User.objects.get(pk=self.normal_user.pk)
        u.user_permissions.add(permission)

    def test_POSTing_a_new_move_with_items(self):
        data = {"status": "PROCESSING", "items": [self.item.id, self.item_two.id, self.item_three.id],
                "destination_id": self.destination.id, "type": "SIMPLE_ENTRY", "source_id": self.source.id,
                "external_id": "ABC1"}
        url = reverse("move-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_new_move_with_lines(self):
        data = {"status": "UNASSIGNED", "lines": [{"sku_id": self.sku.id, "amount": 2}],
                "destination_id": self.destination.id, "type": "TRANSFER", "source_id": self.source.id,
                "external_id": "ABC2"}
        url = reverse("move-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_action_receive_items(self):
        data = {"item_ids": [self.item.id, self.item_two.id, self.item_three.id]}
        url = reverse("move-actions-receive-items", args=[self.move.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 202)

    def test_GETing_a_receive_items(self):
        url = reverse("move-received-items", args=[self.move.id])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_GETing_a_expected_items(self):
        url = reverse("move-expected-items", args=[self.move.id])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_GETing_a_lines(self):
        url = reverse("move-lines", args=[self.move.id])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_GETing_a_move_line_with_expected_items(self):
        url = reverse("move-line-expected_items", args=[self.move_line.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_GETing_a_move_line_with_received_items(self):
        url = reverse("move-line-expected_items", args=[self.move_line.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_position_items(self):
        data = {"items": [self.item.id, self.item_two.id], "destination_id": self.destination.id,
                "sub_location_id": self.sub_location.code}
        url = reverse("position-items")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 202)


class InventoryItemAPITestCase(APITestCase):
    def setUp(self):
        self.client = Client()
        self.normal_user = User.objects.create_user(
            username="monkey", password="monkey1234", email="<EMAIL>", is_staff=True
        )
        self.normal_user.save()

        self.super_user = User.objects.create_superuser(
            username="super_monkey", password="super_monkey1234", email="<EMAIL>"
        )
        self.super_user.save()
        self.warehouse_mex = Warehouse.objects.create(
            name="MAIN WAREHOUSE MEXICO",
            display_name="Main - Warehouse -MEXICO",
        )
        self.warehouse_mex.save()

        # create source location.
        self.source = Location.objects.create(
            id=1,
            name="MAIN WAREHOUSE",
            display_name="Main - Warehouse",
            type='SIMPLE',
            warehouse=self.warehouse_mex,
        )
        self.source.save()
        self.destination = Location.objects.create(
            id=2,
            name="MAIN STORE",
            display_name="Main - Store",
            type='SIMPLE'
        )
        self.destination.save()
        self.sub_location = SubLocation.objects.create(
            code='A1',
            location=self.source
        )
        self.sub_location.save()
        # create sku_group.

        self.sku_group = SKUGroup.objects.create(
            name='ZAPATOS',
        )
        self.sku_group.save()
        self.sku = SKU.objects.create(
            id=1,
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004068",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku.save()
        self.item = Item.objects.create(
            id="9202226913468bc",
            serial=1,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item.save()
        self.item_two = Item.objects.create(
            id="9202226913468bd",
            serial=2,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,

        )
        self.item_two.save()

        self.item_three = Item.objects.create(
            id="9202226913468be",
            serial=3,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item_three.save()
        permission = Permission.objects.get(name='Can change item')
        u = User.objects.get(pk=self.normal_user.pk)
        u.user_permissions.add(permission)

    def test_GETing_item_sorter_state(self):
        data = {"items": [self.item.id], "criteria": "state"}
        url = reverse("items-sorter", args=[])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, 201)

    def test_GETing_item_sorter_sku(self):
        data = {"items": [self.item.id], "criteria": "sku"}
        url = reverse("items-sorter", args=[])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url, data)
        self.assertEqual(response.status_code, 201)

    def test_GETing_item_activity(self):
        url = reverse("item-activity", args=[self.item.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class InventoryAuditAPITestCase(APITestCase):
    def setUp(self):
        self.client = Client()
        self.normal_user = User.objects.create_user(
            username="monkey", password="monkey1234", email="<EMAIL>", is_staff=True
        )
        self.normal_user.save()

        self.super_user = User.objects.create_superuser(
            username="super_monkey", password="super_monkey1234", email="<EMAIL>"
        )
        self.super_user.save()
        self.warehouse_mex = Warehouse.objects.create(
            name="MAIN WAREHOUSE MEXICO",
            display_name="Main - Warehouse -MEXICO",
        )
        self.warehouse_mex.save()

        # create source location.
        self.source = Location.objects.create(
            id=1,
            name="MAIN WAREHOUSE",
            display_name="Main - Warehouse",
            type='SIMPLE',
            warehouse=self.warehouse_mex,
        )
        self.source.save()
        self.destination = Location.objects.create(
            id=2,
            name="MAIN STORE",
            display_name="Main - Store",
            type='SIMPLE'
        )
        self.destination.save()
        self.sub_location = SubLocation.objects.create(
            code='A1',
            location=self.source
        )
        self.sub_location.save()
        # create sku_group.

        self.sku_group = SKUGroup.objects.create(
            name='ZAPATOS',
        )
        self.sku_group.save()
        self.sku = SKU.objects.create(
            id=1,
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004068",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku.save()
        self.item = Item.objects.create(
            id="9202226913468bc",
            serial=1,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item.save()
        self.item_two = Item.objects.create(
            id="9202226913468bd",
            serial=2,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,

        )
        self.item_two.save()

        self.item_three = Item.objects.create(
            id="9202226913468be",
            serial=3,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item_three.save()

        self.audit = Audit.objects.create(
            external_id="ABC23",
            location=self.source,
            sub_location=self.sub_location,
            id=2
        )
        self.audit.save()

        self.audit_line = AuditLine.objects.create(
            audit=self.audit,
            sku=self.sku
        )

        permission = Permission.objects.get(name='Can add audit')
        permission_change = Permission.objects.get(name='Can change audit')

        u = User.objects.get(pk=self.normal_user.pk)
        u.user_permissions.add(permission)
        u.user_permissions.add(permission_change)

    def test_POSTing_a_new_audit(self):
        data = {"external_id": "ABC1", "location_id": self.source.id, "id": 1,
                "sub_location_id": self.sub_location.code}
        url = reverse("audit-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_file_audit(self):
        temp = tempfile.TemporaryFile()
        temp.writelines('9202226913468be\n')
        temp.seek(0)

        data = {"audit_items_file": temp}
        url = reverse("audit-audit-file", args=[self.audit.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)


("class InventoryViewModelsAPITestCase(APITestCase):\n"
 "    def setUp(self):\n"
 "        self.client = Client()\n"
 "        self.normal_user = User.objects.create_user(\n"
 "            username=\"monkey\", password=\"monkey1234\", email=\"<EMAIL>\", is_staff=True\n"
 "        )\n"
 "        self.normal_user.save()\n"
 "\n"
 "        self.super_user = User.objects.create_superuser(\n"
 "            username=\"super_monkey\", password=\"super_monkey1234\", email=\"<EMAIL>\"\n"
 "        )\n"
 "        self.super_user.save()\n"
 "        self.warehouse_mex = Warehouse.objects.create(\n"
 "            name=\"MAIN WAREHOUSE MEXICO\",\n"
 "            display_name=\"Main - Warehouse -MEXICO\",\n"
 "        )\n"
 "        self.warehouse_mex.save()\n"
 "\n"
 "        # create source location.\n"
 "        self.source = Location.objects.create(\n"
 "            id=1,\n"
 "            name=\"MAIN WAREHOUSE\",\n"
 "            display_name=\"Main - Warehouse\",\n"
 "            type='SIMPLE',\n"
 "            warehouse=self.warehouse_mex,\n"
 "        )\n"
 "        self.source.save()\n"
 "        self.destination = Location.objects.create(\n"
 "            id=2,\n"
 "            name=\"MAIN STORE\",\n"
 "            display_name=\"Main - Store\",\n"
 "            type='SIMPLE'\n"
 "        )\n"
 "        self.destination.save()\n"
 "        self.sub_location = SubLocation.objects.create(\n"
 "            code='A1',\n"
 "            location=self.source\n"
 "        )\n"
 "        self.sub_location.save()\n"
 "        # create sku_group.\n"
 "\n"
 "        self.sku_group = SKUGroup.objects.create(\n"
 "            name='ZAPATOS',\n"
 "        )\n"
 "        self.sku_group.save()\n"
 "        self.sku = SKU.objects.create(\n"
 "            id=1,\n"
 "            name=\"OLD_PARR_NEW_IBC_12YR_12X1000ML_40%\",\n"
 "            display_name=\"OLD PARR NEW IBC 12YR 12X1000ML 40%\",\n"
 "            filter=1,\n"
 "            ean=\"5000281004068\",\n"
 "            reference=\"0107836\",\n"
 "            group=self.sku_group,\n"
 "            company_prefix=\"591001\",\n"
 "            gtin_code=\"05000281004068\",\n"
 "            gtin_data_structure=\"GTIN_13\",\n"
 "            properties={\n"
 "                \"descripcion\": \"OLD PARR NEW IBC 12YR 12X1000ML 40%\",\n"
 "                \"moneda\": \"$\",\n"
 "                \"precio\": \"41.00\",\n"
 "                \"codproveedor\": \"82451962\",\n"
 "                \"codigosku\": \"01-07836\"\n"
 "            },\n"
 "        )\n"
 "        self.sku.save()\n"
 "        self.item = Item.objects.create(\n"
 "            id=\"9202226913468bc\",\n"
 "            serial=1,\n"
 "            current_location=self.source,\n"
 "            state=\"PRESENT\",\n"
 "            batch_type=\"PRODUCTION_ORDER\",\n"
 "            batch_number=\"102030\",\n"
 "            properties=None,\n"
 "            locked=False,\n"
 "            quality_check=False,\n"
 "            completed_check=False,\n"
 "            sku=self.sku,\n"
 "            current_sub_location=self.sub_location,\n"
 "        )\n"
 "        self.item.save()\n"
 "        self.item_two = Item.objects.create(\n"
 "            id=\"9202226913468bd\",\n"
 "            serial=2,\n"
 "            current_location=self.source,\n"
 "            state=\"PRESENT\",\n"
 "            batch_type=\"PRODUCTION_ORDER\",\n"
 "            batch_number=\"102030\",\n"
 "            properties=None,\n"
 "            locked=False,\n"
 "            quality_check=False,\n"
 "            completed_check=False,\n"
 "            sku=self.sku,\n"
 "            current_sub_location=self.sub_location,\n"
 "\n"
 "        )\n"
 "        self.item_two.save()\n"
 "\n"
 "        self.item_three = Item.objects.create(\n"
 "            id=\"9202226913468be\",\n"
 "            serial=3,\n"
 "            current_location=self.source,\n"
 "            state=\"PRESENT\",\n"
 "            batch_type=\"PRODUCTION_ORDER\",\n"
 "            batch_number=\"102030\",\n"
 "            properties=None,\n"
 "            locked=False,\n"
 "            quality_check=False,\n"
 "            completed_check=False,\n"
 "            sku=self.sku,\n"
 "            current_sub_location=self.sub_location,\n"
 "        )\n"
 "        self.item_three.save()\n"
 "\n"
 "        self.move = Move.objects.create(\n"
 "            external_id=\"AB4\",\n"
 "            source=self.source,\n"
 "            destination=self.destination,\n"
 "            type=\"\",\n"
 "            status=\"\"\n"
 "        )\n"
 "        self.move_line = MoveLine.objects.create(\n"
 "            move=self.move,\n"
 "            sku=self.sku,\n"
 "            amount=2,\n"
 "            items={\"items\": [self.item.id, self.item_two.id]}\n"
 "        )\n"
 "\n"
 "        permission = Permission.objects.get(name='Can add move')\n"
 "        u = User.objects.get(pk=self.normal_user.pk)\n"
 "        u.user_permissions.add(permission)\n"
 "\n"
 "    def test_GETing_summary_location_type(self):\n"
 "        url = reverse(\"summary-location-type-list\")\n"
 "        login = self.client.login(username=\"monkey\", password=\"monkey1234\")\n"
 "        self.assertEqual(login, True)\n"
 "        response = self.client.get(url)\n"
 "        self.assertEqual(response.status_code, 200)\n"
 "\n"
 "    def test_GETing_summary_location(self):\n"
 "        url = reverse(\"summary-location-list\")\n"
 "        login = self.client.login(username=\"monkey\", password=\"monkey1234\")\n"
 "        self.assertEqual(login, True)\n"
 "        response = self.client.get(url)\n"
 "        self.assertEqual(response.status_code, 200)\n"
 "\n"
 "    def test_GETing_summary_sub_location(self):\n"
 "        url = reverse(\"summary-sub-location-list\")\n"
 "        login = self.client.login(username=\"monkey\", password=\"monkey1234\")\n"
 "        self.assertEqual(login, True)\n"
 "        response = self.client.get(url)\n"
 "        self.assertEqual(response.status_code, 200)\n"
 "\n"
 "    def test_GETing_total_items_location(self):\n"
 "        url = reverse(\"total-items-location-list\")\n"
 "        login = self.client.login(username=\"monkey\", password=\"monkey1234\")\n"
 "        self.assertEqual(login, True)\n"
 "        response = self.client.get(url)\n"
 "        self.assertEqual(response.status_code, 200)\n"
 "\n"
 "    def test_GETing_total_items_sub_location(self):\n"
 "        url = reverse(\"total-items-sub-location-list\")\n"
 "        login = self.client.login(username=\"monkey\", password=\"monkey1234\")\n"
 "        self.assertEqual(login, True)\n"
 "        response = self.client.get(url)\n"
 "        self.assertEqual(response.status_code, 200)\n"
 "\n"
 "    def test_GETing_total_items_warehouse(self):\n"
 "        url = reverse(\"total-items-warehouse-list\")\n"
 "        login = self.client.login(username=\"monkey\", password=\"monkey1234\")\n"
 "        self.assertEqual(login, True)\n"
 "        response = self.client.get(url)\n"
 "        self.assertEqual(response.status_code, 200) ")
 """
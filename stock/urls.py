# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.urls import path
from rest_framework.routers import DefaultRouter

from .api_views import *

router = DefaultRouter()
router.register(r'warehouses', WarehouseViewSet, 'warehouse')
router.register(r'shipments', ShipmentViewSet, 'shipment')
router.register(r'shipment-lines', ShipmentLineViewSet, 'shipment-line')
router.register(r'packages', PackageViewSet, 'package')
router.register(r'package-lines', PackageLineViewSet, 'package-line')
router.register(r'print-order-stock-labels', PrintOrderStockLabelViewSet, 'print-order-stock-label')
router.register(r'print-order-line-stock-labels', PrintOrderLineStockLabelViewSet, 'print-order-line-stock-label')
router.register(r'label-shipment-packages', StockLabelShipmentPackagesViewSet, 'label-shipment-package')

urlpatterns = router.urls

urlpatterns += [
    path('transfer-orders/', TransferOrderView.as_view(), name='transfer-order-create'),
]

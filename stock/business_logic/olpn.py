# -*- coding: utf-8 -*-

from __future__ import unicode_literals
import logging

from ..models import Shipment, Olpn, Item
from operations.models import Packing


def validation_packing(packing_list, shipment_object):
    packings_invalid = Packing.objects.filter(
        external_id__in=packing_list).exclude(shipment_id=shipment_object.pk).exclude(shipment_id=None)
    invalid = []

    for packing in packings_invalid:
        invalid.append(packing.external_id)

    valid = list(set(packing_list) - set(invalid))

    logging.info("Valid: {}, Invalid: {}".format(valid, invalid))
    result = {"Invalid": invalid, "Valid": valid}
    return result


def remove_shipment_packing(shipment_object, packings):
    packing_list = []

    for packing in packings:
        packing_list.append(packing['external_id'])

    logging.warning("The Olpns are: {}".format(packing_list))
    packing_objects = Packing.objects.filter(external_id__in=packing_list)

    for packing in packing_objects:
        packing.shipment_id = None
        packing.save()

    packings = Packing.objects.filter(shipment_id=shipment_object.id)
    logging.warning("Remove {} packings of shipment".format(len(packings)))
    shipment_object.total_expected_items = len(packings)
    shipment_object.save()

    logging.warning(" {} Packingds update to without id: {} shipment".format(packing_objects, shipment_object.pk))
    return packing_objects


def add_shipment_packing(shipment_object, packings):
    packing_list = []

    for packing in packings:
        packing_list.append(packing['external_id'])

    logging.warning("The Packings are: {}".format(packing_list))

    packings_summary = validation_packing(packing_list, shipment_object)
    packings_valid = packings_summary.get("Valid")
    packings_invalid = packings_summary.get("Invalid")

    if len(packings_valid):
        packing_objects = Packing.objects.filter(external_id__in=packing_list)

        for packing in packing_objects:
            packing.shipment_id = shipment_object.pk
            packing.save()

        packings = Packing.objects.filter(shipment_id=shipment_object.id)
        total_items = len(packings)
        shipment_object.total_expected_items += total_items
        shipment_object.save()
        logging.warning(" {} Packings update with shipment of id: {} ".format(packing_objects, shipment_object.pk))
        return packings_invalid
    else:
        return packings_invalid

from datetime import datetime
import logging

from stock.models import *
from inventory.business_logic import moves as moves_logic
from inventory.models import Trace, ReturnType
from common.models import User


def create_lines_from_array(package, lines):
    new_lines = []
    for line in lines:
        new_lines.append(
            PackageLine(
                package=package,
                ean=line['ean'],
                amount=line['amount'],
                items={"items": []},
                properties=line['properties'],
                sku_properties=line['sku_properties'],
                created_by=package.created_by,
                modified_by=package.modified_by
            )
        )
    PackageLine.objects.bulk_create(new_lines)
    return new_lines


def destroy_packages_lines(packages: [Package], reporter: User, force_epcs=(), type_return: ReturnType = None):
    lines = PackageLine.objects.select_related("package").filter(package__in=packages)
    customer_items = {}  # {--customer_id--, [--items--]}
    items_list = []
    traces = []

    for package in packages:
        customer_items.setdefault(package.customer_id, []).append(package.properties["epc"])\
            if package.properties.get("epc") else None

    for line in lines:
        for items in line.items_list:
            customer_items.setdefault(line.package.customer_id, []).append(items)
        line.status = "DELETED"
        line.save()

    for customer_id, items in customer_items.items():
        items_to_update = moves_logic.update_items_state(
            items=items, state="PRESENT", reporter=reporter, include_states=["PACKED", "VERIFIED"],
            return_objects=True)
        items_to_update.update(type_return=type_return)
        for item in items_to_update:
            in_trace = Trace(
                item_id=item.id,
                reporter=reporter,
                created_by=reporter,
                modified_by=reporter,
                reporter_source="WEB_APP",
                action="UNPACK",
                location=item.current_location,
                customer_id=customer_id,
                additional_data={"properties": item.properties}
            )
            traces.append(in_trace)
            items_list.append(item.id)
    if force_epcs:
        items_to_update = moves_logic.update_items_state(
            items=force_epcs, state="PRESENT", reporter=reporter, exclude_states=["PACKED", "VERIFIED", "PRESENT"],
            return_objects=True)
        items_to_update.update(type_return=type_return)
        for item in items_to_update:
            in_trace = Trace(
                item_id=item.id,
                reporter=reporter,
                created_by=reporter,
                modified_by=reporter,
                reporter_source="SYSTEM",
                action="RETURN",
                location=item.current_location,
                customer_id=item.customer_id,
                additional_data={"properties": item.properties}
            )
            traces.append(in_trace)
            items_list.append(item.id)

    Trace.objects.bulk_create(traces)
    return items_list


def destroy_packages(packages: [Package], reporter: User, customer_id=None, force_epcs=(),
                     type_return_id=None, force_destroy=False):
    if packages and packages[0].customer and type_return_id is None:
        type_return_id = packages[0].customer.properties.get("destroy_packages_type_return_id")
    if customer_id and type_return_id is None:
        customer = Customer.objects.filter(id=customer_id).first()
        type_return_id = customer.properties.get("destroy_packages_type_return_id") if customer else None

    type_return = ReturnType.objects.filter(id=type_return_id).first()
    destroy_packages_lines(packages=packages, reporter=reporter, force_epcs=force_epcs, type_return=type_return)
    for package in packages:
        if force_destroy:
            package.external_id = None
            package.status = "DELETED"
        if package.status == "CREATED":
            package.status = "DELETED"
        package.properties.pop("epc", None)
        package.modified_by = reporter
        package.save()


def dispatch_package(package: Package, user: User):
    new_trace = Trace.objects.create(
        item_id=package.external_id,
        reporter=user,
        location=package.location,
        reporter_source="HANDHELD",
        action="DISPATCHED",
        customer=package.customer
    )
    new_trace.save()
    item = Item.objects.get(pk=package.external_id)
    item.state = 'DISPATCH'
    item.modified = datetime.now()
    item.save()

    package.status = 'DISPATCHED'
    package.modified = datetime.now()
    package.save()

    logging.info('Dispatch Package: {}, Status: {}'.format(package.pk, package.status))

    return package


def check_package(package: Package, user: User):
    in_trace = Trace.objects.create(
        item_id=package.external_id,
        reporter=user,
        location=package.location,
        reporter_source="HANDHELD",
        action="VERIFIED",
        customer=package.customer
    )
    in_trace.save()
    item = Item.objects.get(pk=package.external_id)
    item.state = 'VERIFIED'
    item.modified = datetime.now()
    item.save()

    package.status = 'VERIFIED'
    package.modified = datetime.now()
    package.save()

    logging.info('VERIFIED Packing: {}, Status: {}'.format(package.pk, package.status))
    return package


def process_epc_association(package_object: Package, reporter: User):
    epc = package_object.properties.get("epc")
    if epc:
        packages_to_destroy = Package.objects.filter(properties__epc=package_object.properties["epc"]) \
            .exclude(id=package_object.id)
        destroy_packages(packages=packages_to_destroy, reporter=reporter)
        item_object = moves_logic.update_items_state(
            items=[epc], state="PACKED", reporter=reporter, related_model_instance=package_object,
            return_objects=True)[0]
        in_trace = Trace.objects.create(
            item_id=epc,
            reporter=reporter,
            created_by=reporter,
            modified_by=reporter,
            location=item_object.current_location,
            reporter_source="HANDHELD",
            action="PACK",
            customer=item_object.customer,
            additional_data={"properties": item_object.properties}
        )
        in_trace.save()

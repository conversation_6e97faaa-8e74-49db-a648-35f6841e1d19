# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from stock.models import PrintOrderLineStockLabel, PrintOrderStockLabel, Shipment, Package
from inventory.models import Location


def create_label_lines_from_array(order: PrintOrderStockLabel, lines: list):
    new_lines = []
    for line in lines:
        new_lines.append(
            PrintOrderLineStockLabel(
                print_order_label=order,
                shipment_id=line.get('shipment_id'),
                package_id=line.get('package_id'),
                amount=line['amount'],
                initial_serial=line.get('initial_serial'),
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    PrintOrderLineStockLabel.objects.bulk_create(new_lines)
    return new_lines


def create_from_shipment(shipment: Shipment, lines: list, location: Location, template_label_id, customer_id):
    new_order = PrintOrderStockLabel.objects.create(
        location=location,
        batch_number=shipment.external_id,
        batch_type="SHIPMENT",
        template_label_id=template_label_id,
        customer_id=customer_id,
        created_by=shipment.created_by,
        modified_by=shipment.modified_by
    )
    create_label_lines_from_array(order=new_order, lines=lines)
    return new_order


def transform_packages_to_label_lines(packages: list):
    label_lines = []
    serial = 0
    for package in packages:
        serial += 1
        label_line = {
            "package_id": package.id,
            "amount": 1,
            "initial_serial": serial
        }
        label_lines.append(label_line)
    return label_lines


def create_from_shipment_packages(shipment_ids, location_id, template_label_id, customer_id):
    shipment_objects = Shipment.objects.filter(pk__in=shipment_ids).exclude(status="CANCELED")
    location_object = Location.objects.get(pk=location_id)

    print_labels = []
    for shipment in shipment_objects:
        lines = Package.objects.filter(shipment=shipment)
        new_lines = transform_packages_to_label_lines(packages=lines)
        print_label = create_from_shipment(shipment=shipment, lines=new_lines, location=location_object,
                                           template_label_id=template_label_id, customer_id=customer_id)
        print_labels.append(print_label)

    return print_labels

# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import csv
import os
from collections import defaultdict
from django.core.files.base import File

import logging
from datetime import datetime

import stock.utils as stock_utils
from inventory.models import Item, Trace


# Creation logic
def arrange_items_by_sku(items_ids):
    item_objects = Item.objects.filter(pk__in=items_ids).exclude(state='INACTIVE')
    skus_dict = defaultdict(list)
    for item in item_objects:
        skus_dict[item.sku.pk].append(item)
    return skus_dict


# Execution logic

def update_items_sub_location(items=[], sub_location_id=None):
    """
    Updates given Items sub location
    """
    items = Item.objects.filter(pk__in=items).exclude(state='INACTIVE')
    items_to_update = [item.pk for item in items]
    logging.info("Updating  %s items to %s sublocation", len(items), sub_location_id)
    items.update(current_sub_location_id=sub_location_id, state='PRESENT', modified=datetime.now())
    # Return updated items ids
    return items_to_update


def move_items(items=[], destination=None, sublocation=None):
    """
    Moves items to given location
    """
    assert len(items), 'EPC list cannot be  empty'
    assert destination, 'Destination cannot be None'
    items = Item.objects.exclude(current_location=destination, state='PRESENT') \
        .filter(pk__in=items).exclude(state='INACTIVE')
    items_to_update = [item.pk for item in items]
    logging.info("Moving  %s items to %s", len(items), destination)
    items.update(current_location=destination, state='PRESENT', modified=datetime.now(),
                 current_sub_location=sublocation)
    # Return updated items ids
    return items_to_update


def update_items_state(items=[], state=None, rfid_label=None, verified=None, exclude_states=[], include_states=[]):
    """
    Updates Items to given state
    """
    # if you provide exclude_states, items in those states wont be updated.
    # if you provide include_states, ONLY items in those states will be updated.
    assert len(items), 'Item list cannot be  empty'
    assert state, 'New State cannot be None'
    assert not (bool(exclude_states) and bool(include_states)), \
        'You should specify either included, excluded states or None, but not both'

    items = Item.objects.filter(pk__in=items).exclude(state='INACTIVE')
    items_to_update = [item.pk for item in items]

    if exclude_states:
        items = items.exclude(state__in=exclude_states)
    if include_states:
        items = items.filter(state__in=include_states)

    logging.info("Changing  %s items to %s state and %s verified and %s rfid_label", len(items_to_update),
                 state, rfid_label, verified)
    items.update(state=state,  modified=datetime.now(), rfid_label=rfid_label, verified=verified)

    return items_to_update


def update_move_progress(move):
    """
    Updates move status according to its lines, if all lines are completed,move is completed
    """
    assert move, 'Move cannot be None'
    # I know this algorithm could be more efficient, improve it when you can.
    total_completed_lines = 0
    for line in move.lines.all():
        if line.status == "COMPLETED":
            total_completed_lines += 1

    if total_completed_lines == len(move.lines.all()):
        move.status = "COMPLETED"
    else:
        move.status = "INCOMPLETE"
    move.save()


def execute_simple_entry(move):
    # TODO: Add flag to update pending moves to  location
    assert move, 'Move cannot be None'
    assert move.type == "SIMPLE_ENTRY", 'Move type must  be Simple Entry, found: {} '.format(move.type)
    assert move.destination, 'Move destination cannot be None'
    logging.info("Executing simple entry  %s with %s lines", move, move.lines.count())

    def process_line(line_object):
        items_in_line = line_object.items_list
        updated_items_ids = move_items(items=items_in_line, destination=move.destination)   # 1. Actually Move Items
        line.status = "COMPLETED"    # 2. Update Line to completed, verify si es completed or invalid
        line.add_received_items(items=updated_items_ids, replace=True, commit=False)  # 3. Update received Items
        line.save()
        return updated_items_ids

    # Process Line by line
    items = []
    for line in move.lines.all():
        updated_item_ids = process_line(line)
        items.extend(updated_item_ids)
    # Set move status to completed
    move.status = "COMPLETED"
    move.save()
    # Create traces
    traces = []
    for item in items:
        in_trace = Trace(
            item_id=item,
            location=move.destination,
            reporter=move.created_by,
            reporter_source="HANDHELD",
            action="SIMPLE_ENTRY",
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)

    return items


def execute_transfer(move):
    assert move, 'Move cannot be None'
    assert move.type == "TRANSFER", 'Move type must  be Transfer, found: {} '.format(move.type)
    assert move.destination, 'Move destination cannot be None'
    logging.info("Executing transfer  %s with %s lines", move, move.lines.count())

    def process_line(line_object):
        items_in_line = line_object.items_list
        updated_items_ids = update_items_state(items=items_in_line, state="IN_TRANSFER")  # 1. Update items to in transfer
        line.status = "INCOMPLETE"  # 2. Update Line status to incomplete
        line.add_expected_items(items=updated_items_ids, replace=True, commit=False)  # 3. Update received Items
        line.save()
        return updated_items_ids

    # Process Line by line
    items = []
    for line in move.lines.all():
        updated_item_ids = process_line(line)
        items.extend(updated_item_ids)
    # Set move status to completed
    move.status = "INCOMPLETE"
    move.save()
    # Create traces
    traces = []
    for item in items:
        in_trace = Trace(
            item_id=item,
            location=move.source,
            reporter=move.created_by,
            reporter_source="WEB_APP",
            action="TRANSFER_OUT",
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)

    return items


def execute_action_receive(move, user, item_ids=[]):
    assert move, 'Move cannot be None'
    assert move.type == "TRANSFER", 'Move type must  be Transfer, found: {} '.format(move.type)
    assert move.status == "INCOMPLETE", 'Move status must be INCOMPLETE, found: {}'.format(move.status)
    assert move.destination, 'Move destination cannot be None'
    logging.info("Executing receive action in move %s with %s lines", move.pk, move.lines.count())

    def process_line(line_object, incoming_item_ids):
        expected_items = line_object.expected_items_list         # 1. items expected of the move.
        received_items = line_object.received_items_list         # 2. items already received of the move.
        pending_items = list(set(expected_items)-set(received_items))   # 3. items pending to receive

        items_found_to_receive = list(set(pending_items).intersection(incoming_item_ids))   # 4. items found to receive

        if not items_found_to_receive:
            return []

        updated_items_ids = move_items(items=items_found_to_receive, destination=move.destination)   # 5. Actually Receive Items
        logging.debug("Evaluating line %s . Expected  %s items", line.pk, expected_items)
        logging.debug("Evaluating line %s . Item found to receive  %s items", line.pk, items_found_to_receive)
        line.add_received_items(items=updated_items_ids, replace=False, commit=False)  # 6.update received_items in the line.

        if set(line.expected_items_list) == set(line.received_items_list):   # 7. validation if complete line of items.
            line.status = "COMPLETED"
        else:                               # 8. update state of lines and move.
            line.status = "INCOMPLETE"
        line.modified_by = user
        line.save()
        return updated_items_ids

    # Process Line by line
    items = []
    for line in move.lines.all():
        updated_items = process_line(line, item_ids)
        items.extend(updated_items)
    # Update move progress
    update_move_progress(move)
    trace = []
    for item in items:                        # 9. create trace for all items_moves.
        in_trace = Trace(
            item_id=item,
            location=move.destination,
            reporter=user,
            reporter_source="HANDHELD",
            action="TRANSFER_IN",
        )
        trace.append(in_trace)
    Trace.objects.bulk_create(trace)
    return items


def get_move_items(move):
    items = move.items_list
    return items


def generate_move_items_file(move):
    """
    Generates Move Items File
    """

    items_move = get_move_items(move=move)
    # Create lines for items file lines (counted)
    sorted_summary_sku = stock_utils.sort_items_by_sku(items_ids=items_move)
    abs_path = os.path.abspath(os.path.dirname(__file__))

    file_name = "move_{}_items_file.csv".format(move.pk)
    path = os.path.join(abs_path, "../tmp/" + file_name)
    move_items_file_tmp = open(path, 'wb')
    writer = csv.writer(move_items_file_tmp)

    for result in sorted_summary_sku:
        sku = result['sku']
        items = result['items']

        for item in items:
            writer.writerow([
                item,
                sku.id,
                sku.external_id.encode('utf-8')
                ]
            )
    move_items_file_tmp.close()
    summary_file = open(path, "r")
    file_name = "move_{}_items_file.csv".format(move.pk)
    move.items_file.save(file_name, File(summary_file))
    os.remove(path)


def generate_move_summary_file(move):
    """
    Generates Move Summary File
    """
    items_move = get_move_items(move=move)
    # Create lines for summary file sku lines (counted)
    sorted_summary_sku = stock_utils.sort_items_by_sku(items_ids=items_move)
    abs_path = os.path.abspath(os.path.dirname(__file__))

    file_name = "move_{}_summary_file.csv".format(move.pk)
    path = os.path.join(abs_path, "../tmp/" + file_name)
    move_summary_file_tmp = open(path, 'wb')
    writer = csv.writer(move_summary_file_tmp)
    for result in sorted_summary_sku:
        sku = result['sku']
        items = result['items']

        writer.writerow([
            sku.id,
            sku.external_id.encode('utf-8'),
            len(items)]
        )
    move_summary_file_tmp.close()
    summary_file = open(path, "r")
    file_name = "move_{}_summary_file.csv".format(move.pk)
    move.summary_file.save(file_name, File(summary_file))
    os.remove(path)


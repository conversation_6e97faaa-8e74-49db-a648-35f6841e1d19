# -*- coding: utf-8 -*-

from __future__ import unicode_literals
import logging

from datetime import datetime
from django.db import transaction

from common.models import User
from ..models import Shipment, ShipmentLine, Package
from operations.models import Packing, PackingLine


def insert_shipment_from_message(message):
    logging.info("Attempting to insert shipment from message:%s" % message)

    with transaction.atomic():

        shipment, created = Shipment.objects.get_or_create(
            external_id=message.get('shipment_id'),
            defaults={
                'expected_arrival_date': message.get('expected_arrival_date'),
                'consolidation_gate_number': message.get('consolidation_gate_id'),
                'source_id': message.get('source_id'),
                'destination_id': message.get('destination_id'),
                'total_expected_items': message.get('shipment_amount'),
                'properties': message.get('properties', None),
                'order_id': message.get('order_id', None),
                'wave': message.get('wave', None),
            }
        )
        for packing in message.get('packings'):
            packing_object, created = Packing.objects.get_or_create(
                external_id=packing['external_id'],
                defaults={
                    'shipment_id': shipment.pk
                }
            )
            for line in packing['lines']:
                line_object, created = PackingLine.objects.get_or_create(
                    sku_id=line['sku_id'],
                    amount=line['amount'],
                    defaults={
                        'packing': packing_object
                    }
                )

    packings = Packing.objects.filter(shipment_id=shipment.id)
    total_packings = len(packings)
    shipment.total_expected_items = total_packings
    shipment.save()
    return shipment


def create_shipment_lines(shipment: Shipment, lines):
    new_lines = []
    for line in lines:
        new_lines.append(
            ShipmentLine(
                shipment=shipment,
                ean=line['ean'],
                amount=line['amount'],
                properties=line['properties'],
                sku_properties=line['sku_properties'],
                created_by=shipment.created_by,
                modified_by=shipment.modified_by
            )
        )
    ShipmentLine.objects.bulk_create(new_lines)
    return new_lines


def update_total_expected_items(shipment_id, reporter_id):
    amount_list = ShipmentLine.objects.filter(shipment_id=shipment_id).values_list('amount', flat=True)
    total_expected_items = sum(amount_list)
    shipment_object = Shipment.objects.get(id=shipment_id)
    shipment_object.total_expected_items = total_expected_items
    shipment_object.modified_by_id = reporter_id
    shipment_object.save()


def dispatch_shipment(shipment: Shipment, user: User):
    shipment.status = 'DISPATCHED'
    shipment.modified = datetime.now()
    shipment.save()

    logging.info('Dispatch Shipment: {}, Status: {}'.format(shipment.pk, shipment.status))

    return shipment


def check_shipment(shipment: Shipment, user: User):
    shipment.status = 'VERIFIED'
    shipment.modified = datetime.now()
    shipment.save()
    logging.info('VERIFIED Shipment: {}, Status: {}'.format(shipment.pk, shipment.status))
    return shipment

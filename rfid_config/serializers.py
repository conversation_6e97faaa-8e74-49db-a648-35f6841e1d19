# -*- coding: utf-8 -*-
from rest_framework import serializers

from customers.serializers import CustomerNestedSerializer
from .models import *


class CustomerMetadataSerializer(serializers.ModelSerializer):
    customer = CustomerNestedSerializer(read_only=True)
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(),
                                                     source='customer', write_only=True,
                                                     required=False, allow_null=True)

    class Meta:
        model = CustomerMetadata
        fields = '__all__'

# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models
from customers.models import Customer
from django.contrib.postgres.fields import Array<PERSON>ield
from django.db.models import <PERSON><PERSON><PERSON><PERSON>


# Create your models here.

class CustomerMetadata(models.Model):
    CODING_TYPE = (
        ('GRAI_96', 'GRAI_96'),
        ('SGTIN_96', 'SGTIN_96')
    )
    namespace = models.CharField(max_length=50, primary_key=True)
    business_name = models.CharField(max_length=140)
    company_prefixes = ArrayField(models.IntegerField(), size=10)
    reference_count = models.BigIntegerField(default=0)
    customer_number = models.CharField(max_length=3)
    epc_hex_read_length = models.IntegerField(default=24)
    epc_hex_write_length = models.IntegerField(default=24)
    default_template_id = models.IntegerField(default=1)
    customer_logo = models.ImageField(upload_to='customer_metadata', null=True, blank=True)
    default_template_label = models.IntegerField(default=1, null=True, blank=True)
    coding_type = models.CharField(max_length=10, choices=CODING_TYPE, default="SGTIN96")
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)

    class Meta:
        ordering = ['namespace']

    def __str__(self):
        return '{}-{}'.format(self.namespace, self.business_name)

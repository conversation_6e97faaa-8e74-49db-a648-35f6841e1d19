# -*- coding: utf-8 -*-
from django.apps import apps
from django.contrib import admin

from .models import *

# auto-register all models
app = apps.get_app_config('rfid_config')


class CustomerMetadataAdmin(admin.ModelAdmin):
    list_display = ('namespace', 'business_name', 'company_prefixes', 'customer_number', 'customer')


admin.site.register(CustomerMetadata, CustomerMetadataAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

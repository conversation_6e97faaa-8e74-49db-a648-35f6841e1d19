# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-04-16 21:04
from __future__ import unicode_literals

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0007_auto_20190712_1606'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerMetadata',
            fields=[
                ('namespace', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('business_name', models.CharField(max_length=140)),
                ('company_prefixes', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(),
                                                                               size=10)),
                ('customer_number', models.CharField(max_length=3)),
                ('epc_hex_read_length', models.IntegerField(default=24)),
                ('epc_hex_write_length', models.IntegerField(default=24)),
                ('default_template_id', models.IntegerField(default=1)),
                ('customer_logo', models.ImageField(blank=True, null=True, upload_to='customer_metadata')),
                ('default_template_label', models.IntegerField(blank=True, default=1, null=True)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE,
                                               to='customers.Customer')),
            ],
            options={
                'ordering': ['namespace'],
            },
        ),
    ]

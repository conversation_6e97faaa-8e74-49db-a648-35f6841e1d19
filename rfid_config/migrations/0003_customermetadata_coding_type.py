# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-06-04 20:18
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('rfid_config', '0002_customermetadata_reference_count'),
    ]

    operations = [
        migrations.AddField(
            model_name='customermetadata',
            name='coding_type',
            field=models.CharField(choices=[('GRAI96', 'Grai96'), ('SGTIN96', 'Sgtin96')], default='SGTIN96', max_length=10),
        ),
    ]

# -*- coding: utf-8 -*-
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilt<PERSON>, OrderingFilter

from rfid_config.serializers import CustomerMetadataSerializer
from rest_framework import viewsets

from .filters import *
from .models import *


class CustomerMetadataViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A simple ViewSet for viewing and editing customer metadata.
    """
    queryset = CustomerMetadata.objects.all()
    serializer_class = CustomerMetadataSerializer
    filter_class = CustomerMetadataFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

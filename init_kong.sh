#!/usr/bin/env bash
#!/usr/bin/env bash

for i in "$@"
do
case $i in
  --d-settings=*)
  DJANGO_SETTINGS_MODULE="${i#*=}"

  ;;
  --k-settings=*)
  KONG_SETTINGS_MODULE="${i#*=}"
  ;;
  --kong-mode=*)
  KONG_MODE="${i#*=}"
  ;;
esac
done


if [ "$DJANGO_SETTINGS_MODULE" = "" ]; then # Check settings args
  echo " missing --d-settings argument"
  exit 128
fi

if  [ "$KONG_SETTINGS_MODULE" = "" ]; then # Check settings args
  echo " missing --k-settings argument "
  exit 128
fi

if [ "$KONG_MODE" = "" ] ; then # Check settings args
  echo " missing --kong-mode argument"
  exit 128
fi


if [ "$KONG_MODE" != "web" ] && [ "$KONG_MODE" != "worker" ]; then # Check valid kong mode
  echo $KONG_MODE "is an invalid kong-mode value, use web or worker"
  exit 128
fi



export DJANGO_SETTINGS_MODULE
export KONG_SETTINGS_MODULE

echo "DJANGO SETTINGS is " $DJANGO_SETTINGS_MODULE "."
echo "KONG SETTINGS is " $KONG_SETTINGS_MODULE  "."
echo "KONG_MODE is " $KONG_MODE   "."

echo "Activating virtual envionment"
source env/bin/activate
echo "installing dependencies..."
pip install -r requirements.txt # install requirement


if [ "$KONG_MODE" = "web" ] ; then
    python manage.py migrate                  # Apply database migrations
    python manage.py collectstatic  --noinput          # Collect static files

    # Prepare log files and start outputting logs to stdout
    # touch /srv/logs/gunicorn.log
    # touch /srv/logs/access.log
    # tail -n 0 -f /srv/logs/*.log &

    # Start Gunicorn processes
    echo Starting Gunicorn.
    exec gunicorn main.wsgi:application \
        --name kong_web \
        --bind 0.0.0.0:8000 \
        --workers 3 \
        --log-level=info \
        --limit-request-line=0
        #--log-file=/srv/logs/gunicorn.log \
        #--access-logfile=/srv/logs/access.log
fi

if [ "$KONG_MODE" = "worker" ] ; then
    # python manage.py collectstatic --noinput  # Collect static files
    celery -A main worker -l info
fi
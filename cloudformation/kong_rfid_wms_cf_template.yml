AWSTemplateFormatVersion: 2010-09-09
Parameters:
  ClientName:
    Description: nombre del cliente
    Type: String
  Environment:
    Description: nombre del ambiente
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - demos
      - staging
      - prod  

Resources:   
  ################### SQS ###########################
  SQSQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "${Environment}-${ClientName}-messages-sqs"
      DelaySeconds: 0
      MaximumMessageSize: 262144
      MessageRetentionPeriod: 345600
      ReceiveMessageWaitTimeSeconds: 0
      VisibilityTimeout: 30      
  SQSQueueCelery:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: !Sub "celery"
      DelaySeconds: 0
      MaximumMessageSize: 262144
      MessageRetentionPeriod: 345600
      ReceiveMessageWaitTimeSeconds: 0
      VisibilityTimeout: 30  

################### Logs Groups ###########################
  LogGroupKong:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/ecs/${Environment}-${ClientName}-kong"
      RetentionInDays: 30
      Tags:
        - Key: environment
          Value: !Ref Environment
        - Key: account
          Value: !Ref ClientName
  LogGroupKongWorker:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub "/ecs/${Environment}-${ClientName}-worker"
      RetentionInDays: 30
      Tags:
        - Key: environment
          Value: !Ref Environment
        - Key: account
          Value: !Ref ClientName

################## Outputs #######################

Outputs:
  QueueURL:
    Value: !Ref SQSQueue
    Description: URL of the SQS Created
  QueueARN:
    Description: "Kong SQS Queue ARN"
    Value: !GetAtt SQSQueue.Arn
  QueueCeleryURL:
    Value: !Ref SQSQueueCelery
    Description: URL of the SQS Created
  QueueCeleryARN:
    Description: "Kong Celery SQS Queue ARN"
    Value: !GetAtt SQSQueueCelery.Arn
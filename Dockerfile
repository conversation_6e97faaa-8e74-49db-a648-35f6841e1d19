FROM python:3.10

ENV PYTHONUNBUFFERED=1
ENV WEBAPP_DIR=/kong_core_app

RUN mkdir $WEBAPP_DIR

WORKDIR $WEBAPP_DIR

ADD requirements.txt $WEBAPP_DIR/

#RUN pip install django-createsuperuserwithpassword

RUN pip install -r requirements.txt

ADD . $WEBAPP_DIR/

RUN chmod +x $WEBAPP_DIR/run_worker.sh $WEBAPP_DIR/init_worker.sh $WEBAPP_DIR/run_api.sh $WEBAPP_DIR/init_api.sh $WEBAPP_DIR/init_kong.sh $WEBAPP_DIR/update_kong.sh
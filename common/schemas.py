import coreapi
import coreschema
from rest_framework import schemas


class DefaultViewSchema(schemas.AutoSchema):

    def _allows_filters(self, path, method):
        """
        Determine whether to include filter Fields in schema.

        Default implementation looks for ModelViewSet or GenericAPIView
        actions/methods that cause filtering on the default implementation.

        Override to adjust behaviour for your view.

        Note: Introduced in v3.7: Initially "private" (i.e. with leading underscore)
            to allow changes based on user experience.
        """
        if getattr(self.view, 'filter_backends', None) is None:
            return False

        return method.lower() in ["get", "put", "patch", "delete"]

    def get_pagination_fields(self, path, method):
        view = self.view

        if method != "GET" and view.name != "Delete":
            return []

        pagination = getattr(view, "pagination_class", None)
        if not pagination:
            return []

        paginator = view.pagination_class()
        return paginator.get_schema_fields(view)


class MoveTotalSummaryViewSchema(schemas.AutoSchema):

    def get_manual_fields(self, path, method):
        extra_fields = []
        if path == '/inventory/moves/totals-by-location/':
            extra_fields = [
                coreapi.Field(
                    name="parent_id",
                    description="Location tree by related parent location",
                    location="query",
                    schema=coreschema.String(),
                    required=False
                ),
                coreapi.Field(
                    name="customer_id",
                    description="customer",
                    schema=coreschema.String(),
                    required=False
                )
            ]
            manual_fields = super().get_manual_fields(path, method)
            return manual_fields + extra_fields
        elif path == '/inventory/moves/type-totals-by-location/':
            extra_fields = [
                coreapi.Field(
                    name="sub_location_id",
                    description="Location tree by related location id",
                    location="query",
                    schema=coreschema.String(),
                    required=False
                ),
                coreapi.Field(
                    name="sub_location__type_id",
                    description="Location tree by related location type",
                    location="query",
                    schema=coreschema.String(),
                    required=False
                ),
                coreapi.Field(
                    name="parent_id",
                    description="Location tree by related parent location",
                    location="query",
                    schema=coreschema.String(),
                    required=False
                ),
                coreapi.Field(
                    name="amount_type",
                    description="Define amount field to be grouped in response body, "
                                "options 'AMOUNT' or 'PRICE', default to 'AMOUNT'",
                    location="query",
                    schema=coreschema.Enum(("AMOUNT", "PRICE")),
                    required=False
                ),
                coreapi.Field(
                    name="customer_id",
                    description="customer",
                    schema=coreschema.String(),
                    required=False
                )
            ]
            manual_fields = super().get_manual_fields(path, method)
            return manual_fields + extra_fields

        elif path == '/inventory/moves/type-summary-by-location/':
            extra_fields = [
                coreapi.Field(
                    name="sub_location_id",
                    description="Location tree by related location id",
                    location="query",
                    schema=coreschema.String(),
                    required=True
                ),
                coreapi.Field(
                    name="group_by_date",
                    description="Additionally group lines by date and return this in response body, "
                                "options 'TRUE' or 'FALSE', default to 'FALSE'",
                    location="query",
                    schema=coreschema.String(),
                    required=False
                ),
                coreapi.Field(
                    name="customer_id",
                    description="customer",
                    schema=coreschema.String(),
                    required=False
                )
            ]
            manual_fields = super().get_manual_fields(path, method)
            return manual_fields + extra_fields

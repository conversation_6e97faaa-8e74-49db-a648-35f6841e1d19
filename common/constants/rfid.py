# RFID Technology constants

SGTIN_EPC_PARTITION_CHOICES = (
    (0, '0: 40(12)-4(1)'),
    (1, '1:37(11)-7(2)'),
    (2, '2:34(10)-10(3)'),
    (3, '3:30(9)-14(4)'),
    (4, '4:27(8)-17(5)'),
    (5, '5:24(7)-20(6)'),
    (6, '6:20(6)-24(7)'),
)

SGTIN_FILTER_VALUES = (
    (0, '0 - All others'),
    (1, '1 - Point of Sale (POS) Trade Item'),
    (2, '2 - Full Case for Transport'),
    (3, '3 - Reserved'),
    (4, '4 - Inner Pack Trade Item Grouping for Handling'),
    (5, '5 - Reserved'),
    (6, '6 - Unit Load'),
    (7, '7 - Unit inside Trade Item or component inside a product not intended for individual sale'),
)

GRAI_EPC_PARTITION_CHOICES = (
    (0, '0: 40(12)-4(0)'),
    (1, '1:37(11)-7(1)'),
    (2, '2:34(10)-10(2)'),
    (3, '3:30(9)-14(3)'),
    (4, '4:27(8)-17(4)'),
    (5, '5:24(7)-20(5)'),
    (6, '6:20(6)-24(6)'),
)

GRAI_FILTER_VALUES = (
    (0, '0 - All others'),
    (1, '1 - Reserved'),
    (2, '2 - Reserved'),
    (3, '3 - Reserved'),
    (4, '4 - Reserved'),
    (5, '5 - Reserved'),
    (6, '6 - Reserved'),
    (7, '7 - Reserved'),
)

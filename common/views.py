from django import http
from common.tasks import worker_connection_check


def server_check_view(request):
    message = request.GET.get("message", "")
    return http.HttpResponse("message: {}".format(message))


def worker_check_view(request):
    message = request.GET.get("message", "")
    worker_connection_check.delay(message=message)
    return http.HttpResponse("message: {}, review worker logs".format(message))


def sentry_check_view(request):
    message = request.GET.get("message", "")
    raise Exception("message: {}".format(message))

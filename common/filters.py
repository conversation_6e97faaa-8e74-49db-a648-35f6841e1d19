# -*- coding: utf-8 -*-
from django.db.models import Q
from django_filters import rest_framework as filters


class BaseFilter(filters.FilterSet):
    created_min = filters.DateFilter(field_name="created", lookup_expr='date__gte')
    created_max = filters.DateFilter(field_name="created", lookup_expr='date__lte')
    created = filters.DateFilter(field_name="created", lookup_expr='date__exact')

    modified_min = filters.DateFilter(field_name="modified", lookup_expr='date__gte')
    modified_max = filters.DateFilter(field_name="modified", lookup_expr='date__lte')
    modified = filters.DateFilter(field_name="modified", lookup_expr='date__exact')

    created_by_id = filters.NumberFilter()
    modified_by_id = filters.NumberFilter()

    def filter_queryset(self, queryset):
        return super(BaseFilter, self).filter_queryset(queryset=queryset)


class ChoiceInFilter(filters.BaseInFilter, filters.ChoiceFilter):
    pass


# -*- coding: utf-8 -*-
from __future__ import unicode_literals

#from apscheduler.scheduler import Scheduler
from apscheduler.schedulers.background import BackgroundScheduler
import logging

from celery import shared_task


sched = BackgroundScheduler()
sched.start()


@shared_task(bind=True)
def worker_connection_check(self, message=""):
    """
    Executes worker connection check
    """
    logging.warning("Worker connection successfully. Message = {}".format(message))

# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import logging
from datetime import datetime

from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from .fields import TimestampField
from .models import *
from core_config.models import UserConfig, JsonMapConfig
from jsonschema import Draft7Validator, validators


def validate_customer(user: User, data: dict, is_many: bool = False):
    pk_field_name = "customer_ids" if is_many else "customer_id"
    object_name = "customers" if is_many else "customer"
    if not data.get(object_name) and not data.get(pk_field_name):
        user_config = UserConfig.objects.filter(user=user).first()
        if user_config and user_config.customer and not user_config.customer.customer_set.all():
            data[object_name] = [user_config.customer] if is_many else user_config.customer
            data[pk_field_name] = [user_config.customer_id] if is_many else user_config.customer_id
        else:
            raise serializers.ValidationError(
                {pk_field_name: [
                    "Send this field in request or Configure user config with a valid customer."]})
    return data


class UserNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name',
                  'email']


class BaseSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True)
    created_by = UserNestedSerializer(read_only=True)
    modified_by = UserNestedSerializer(read_only=True)

    def __init__(self, *args, **kwargs):
        super(BaseSerializer, self).__init__(*args, **kwargs)
        self.add_timestamp_fields()

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        validated_data['modified_by'] = self.context['request'].user
        return super(BaseSerializer, self).create(validated_data)

    def update(self, instance, validated_data):
        validated_data['modified_by'] = self.context['request'].user
        return super(BaseSerializer, self).update(instance, validated_data)

    def refresh_from_instance(self, instance, created: bool = None):
        serializer = type(self)(instance=instance)
        if created is not None:
            serializer._data = serializer.data
            serializer._data["created_now"] = created
        return serializer

    def update_or_create(self, **kwargs):
        self.validated_data['modified_by'] = self.context['request'].user
        instance, created = self.Meta.model.objects.update_or_create(defaults=self.validated_data, **kwargs)
        if created:
            instance.created_by = self.context['request'].user
            instance.save()
        return instance, created

    def add_timestamp_fields(self):
        for field in list(self.fields):
            if type(self.fields[field]) == serializers.DateTimeField or type(
                    self.fields[field]) == serializers.DateField:
                field_name = "{}_timestamp".format(field)
                self.fields[field_name] = TimestampField(source=self.fields[field].source)

    def is_valid(self, raise_exception=False):
        try:
            return super(BaseSerializer, self).is_valid(raise_exception=raise_exception)
        except Exception as errors:
            logging.warning(type(self))
            logging.warning(self.initial_data)
            logging.warning(errors.args)
            raise ValidationError(errors.args[0])


class StringListSerializer(serializers.Serializer):
    count = serializers.SerializerMethodField(read_only=True)
    results = serializers.ListField(child=serializers.CharField(max_length=150), read_only=True)

    def get_count(self, obj):
        return len(obj.get('results'))


class EmptySerializer(serializers.Serializer, serializers.JSONField):
    id = serializers.IntegerField(required=False, allow_null=True, write_only=True)

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass

    def to_internal_value(self, data):
        for key in data.keys():
            if type(key) == str and key[-3:] == "_id" and data[key] == "null":
                data[key] = None
        return super(EmptySerializer, self).to_internal_value(data=data)

    def is_valid(self, raise_exception=False):
        try:
            return super(EmptySerializer, self).is_valid(raise_exception=raise_exception)
        except Exception as errors:
            logging.warning(type(self))
            logging.warning(self.initial_data)
            logging.warning(errors.args)
            raise ValidationError(errors.args[0])

    class Meta:
        pass


class AuditCalculateSerializer(serializers.Serializer, serializers.JSONField):

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass

    def to_internal_value(self, data):
        for key in data.keys():
            if type(key) == str and key[-3:] == "_id" and data[key] == "null":
                data[key] = None
        return super(AuditCalculateSerializer, self).to_internal_value(data=data)

    def is_valid(self, raise_exception=False):
        try:
            return super(AuditCalculateSerializer, self).is_valid(raise_exception=raise_exception)
        except Exception as errors:
            logging.warning(type(self))
            logging.warning(self.initial_data)
            logging.warning(errors.args)
            raise ValidationError(errors.args[0])

    class Meta:
        pass

class BaseJSONValidatorSerializer(BaseSerializer):

    def is_valid(self, raise_exception=False):
        assert self.Meta.model.model_id, 'model_id is required to exec json validation'
        model_field = None
        try:
            valid_response = super(BaseSerializer, self).is_valid(raise_exception=raise_exception)
            json_map_configs = JsonMapConfig.objects.filter(model_id=self.Meta.model.model_id)
            for json_map_config in json_map_configs:
                if not json_map_config.params.get("external_id_to_validate") or \
                        json_map_config.params.get("external_id_to_validate", "") \
                        in self.initial_data.get("external_id", ""):
                    model_field = json_map_config.model_field.lower()
                    DefaultValidatingDraft7Validator(json_map_config.json_schema).validate(
                        self.initial_data.get(model_field, {}))
            return valid_response
        except Exception as errors:
            errors_message = errors.args[0]

            if model_field:
                def dict_creation(x, y):
                    return {x: y}

                if hasattr(errors, 'path'):
                    path = errors.path
                    value = errors.args[0]
                    for position in range(len(path), 0, -1):
                        value = dict_creation(path[position - 1], value)
                    errors_message = {model_field: value}
                else:
                    errors_message = {model_field: errors}

            logging.warning(self.initial_data)
            logging.warning(errors.args)
            raise ValidationError(errors_message)


def extend_extras(validator_class):
    validate_properties = validator_class.VALIDATORS["properties"]

    def set_extras(validator, properties, instance, schema):
        for property_key, sub_schema in properties.items():
            if isinstance(instance.get(property_key, ""), datetime):
                instance[property_key] = instance[property_key].astimezone().isoformat()
            if sub_schema["type"] == "string" and instance.get(property_key, "") is not None and \
                    type(instance.get(property_key, "")) != str:
                instance[property_key] = str(instance[property_key])
            if sub_schema["type"] == "integer" and instance.get(property_key, "") is not None and \
                    type(instance.get(property_key, 0)) != int:
                instance[property_key] = int(instance[property_key])
            if sub_schema["type"] == "number" and instance.get(property_key, "") is not None and \
                    type(instance.get(property_key, 0.0)) != float:
                instance[property_key] = float(instance[property_key])
            if "default" in sub_schema:
                instance.setdefault(property_key, sub_schema["default"])
            if sub_schema.get("null") is True and instance.get(property_key, False) is None:
                instance.pop(property_key)

        for error in validate_properties(
                validator, properties, instance, schema,
        ):
            yield error

    return validators.extend(
        validator_class, {"properties": set_extras},
    )


DefaultValidatingDraft7Validator = extend_extras(Draft7Validator)


class EmptyJSONValidatorSerializer(EmptySerializer):
    json_validator_model = None

    def __init__(self, *args, **kwargs):
        self.json_validator_model = kwargs.pop('json_validator_model', None)
        super(EmptyJSONValidatorSerializer, self).__init__(*args, **kwargs)

    def run_validation(self, data=None):
        model_field = None
        try:
            valid_response = super(EmptyJSONValidatorSerializer, self).run_validation(data=data)
            if self.json_validator_model:
                json_map_configs = JsonMapConfig.objects.filter(model_id=self.json_validator_model.model_id)
                for json_map_config in json_map_configs:
                    if not json_map_config.params.get("external_id_to_validate") or \
                            json_map_config.params.get("external_id_to_validate", "")\
                            in self.root.initial_data.get("external_id", ""):
                        model_field = json_map_config.model_field.lower()
                        DefaultValidatingDraft7Validator(json_map_config.json_schema).validate(
                            data.get(model_field, {}))
            return valid_response
        except Exception as errors:
            errors_message = errors.args[0]
            if model_field:
                errors_message = {model_field: [errors.args[0]]}
            raise ValidationError(errors_message)

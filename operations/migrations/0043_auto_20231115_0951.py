# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2023-11-15 14:51
from __future__ import unicode_literals

from django.db import migrations, models
import operations.models


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0042_auto_20230922_1557'),
    ]

    operations = [
        migrations.AlterField(
            model_name='distribution',
            name='verified_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=operations.models.get_distribution_send_files_path),
        ),
        migrations.AlterField(
            model_name='historicaldistribution',
            name='verified_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpacking',
            name='items_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpacking',
            name='summary_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpacking',
            name='verified_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='packing',
            name='items_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=operations.models.get_packing_files_path),
        ),
        migrations.AlterField(
            model_name='packing',
            name='summary_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=operations.models.get_packing_files_path),
        ),
        migrations.AlterField(
            model_name='packing',
            name='verified_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=operations.models.get_verified_file_path),
        ),
    ]

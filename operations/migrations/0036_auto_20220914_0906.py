# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-09-14 14:06
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0035_auto_20220810_1414'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalstoreorder',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('CANCELLED', 'Cancelled')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalstoreorderline',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('CANCELLED', 'Cancelled')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='storeorder',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('CANCELLED', 'Cancelled')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='storeorderline',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('CANCELLED', 'Cancelled')], default='INCOMPLETE', max_length=20),
        ),
    ]

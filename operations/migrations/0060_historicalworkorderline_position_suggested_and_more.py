# Generated by Django 4.2.11 on 2025-04-20 03:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("inventory", "0103_alter_historicalmoveline_timestamp_and_more"),
        ("operations", "0059_alter_historicalcontact_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalworkorderline",
            name="position_suggested",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="inventory.location",
            ),
        ),
        migrations.AddField(
            model_name="workorderline",
            name="position_suggested",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="inventory.location",
            ),
        ),
    ]

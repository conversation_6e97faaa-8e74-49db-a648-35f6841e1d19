# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2021-06-10 19:35
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0018_auto_20210204_1317'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalproductionorder',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='historicalpurchaseorder',
            name='buyer',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='operations.Contact'),
        ),
        migrations.AddField(
            model_name='historicalpurchaseorder',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='productionorder',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='buyer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='purchase_buyer', to='operations.Contact'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='provider',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_provider', to='operations.Contact'),
        ),
    ]

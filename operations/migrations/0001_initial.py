# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 17:05
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import operations.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0002_auto_20190415_1203'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customers', '__first__'),
    ]

    operations = [
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('name', models.TextField(max_length=100)),
                ('type_contact', models.CharField(choices=[('PROVIDER', 'Provider'), ('CELL_PROVIDER', 'Cell Provider'), ('CLIENT', 'Client')], default='PROVIDER', max_length=20)),
                ('type_identification', models.CharField(blank=True, choices=[('CC', 'Cc'), ('NIT', 'Nit'), ('TI', 'Ti'), ('PASSPORT', 'Passport')], default='NIT', max_length=20, null=True)),
                ('identification', models.CharField(blank=True, max_length=14, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Distribution',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20)),
                ('finished', models.BooleanField(default=False)),
                ('finished_date', models.DateField(null=True)),
                ('closed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('destination', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_distribution', to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('source', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_distribution', to='inventory.Location')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='DistributionLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField()),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('distribution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='operations.Distribution')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Packing',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=40, null=True, unique=True)),
                ('prefix', models.CharField(default='PK', max_length=2)),
                ('type', models.CharField(choices=[('BOX', 'Box')], default='BOX', max_length=15)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('VERIFIED', 'Verified'), ('DISPATCHED', 'Dispatched'), ('ISSUED', 'Issued')], default='CREATED', max_length=15)),
                ('tracking', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'tracking': []}, null=True)),
                ('items_file', models.FileField(blank=True, null=True, upload_to=operations.models.get_packing_files_path)),
                ('summary_file', models.FileField(blank=True, null=True, upload_to=operations.models.get_packing_files_path)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='PackingLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField()),
                ('amount_verified', models.IntegerField(blank=True, default=0, null=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('VERIFIED', 'Verified'), ('DISPATCHED', 'Dispatched'), ('ISSUED', 'Issued')], default='CREATED', max_length=15)),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('packing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='operations.Packing')),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='ProductionOrder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('IN_QUALITY', 'In Quality'), ('COMPLETED', 'Completed'), ('OVERLOADED', 'Overloaded')], default='CREATED', max_length=20)),
                ('prefix', models.CharField(default='PDO', max_length=3)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('entry_location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='entry_location_production', to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='operations.Contact')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='ProductionOrderLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField()),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('IN_QUALITY', 'In Quality'), ('COMPLETED', 'Completed'), ('OVERLOADED', 'Overloaded')], default='CREATED', max_length=20)),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('production_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='operations.ProductionOrder')),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='StoreOrder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20)),
                ('expected_date', models.DateField(blank=True, null=True)),
                ('arrival_date', models.DateField(blank=True, null=True)),
                ('automatic_distribution', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
                ('destination', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_store_orders', to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('requester', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='operations.Contact')),
                ('source', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_store_orders', to='inventory.Location')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='StoreOrderLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField()),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20)),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
                ('store_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='operations.StoreOrder')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='WorkOrder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20)),
                ('finished', models.BooleanField(default=False)),
                ('finished_date', models.DateField(blank=True, null=True)),
                ('assigned_to', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('closed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='packings_closed', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('destination', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_work_orders', to='inventory.Location')),
                ('distribution', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='work_orders', to='operations.Distribution')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('source', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_work_orders', to='inventory.Location')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='WorkOrderLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField()),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
                ('work_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='operations.WorkOrder')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.AddField(
            model_name='distribution',
            name='store_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='operations.StoreOrder'),
        ),
    ]

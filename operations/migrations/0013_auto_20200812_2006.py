# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-08-13 01:06
from __future__ import unicode_literals

from django.db import migrations, models
import operations.models


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0012_auto_20200507_2035'),
    ]

    operations = [
        migrations.AddField(
            model_name='distribution',
            name='verified_file',
            field=models.FileField(blank=True, null=True, upload_to=operations.models.get_distribution_send_files_path),
        ),
        migrations.AddField(
            model_name='distributionline',
            name='assigned',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='distribution',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('DISTRIBUTED', 'Distributed')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='distributionline',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('DISTRIBUTED', 'Distributed')], default='INCOMPLETE', max_length=20),
        ),
    ]

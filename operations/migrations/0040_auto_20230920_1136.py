# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2023-09-20 16:36
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0071_auto_20230920_1136'),
        ('operations', '0039_auto_20230704_1515'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalproductionorder',
            name='type',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.ItemType'),
        ),
        migrations.AddField(
            model_name='historicalpurchaseorder',
            name='type',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.ItemType'),
        ),
        migrations.AddField(
            model_name='productionorder',
            name='type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.ItemType'),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.ItemType'),
        ),
    ]

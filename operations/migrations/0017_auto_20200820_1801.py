# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2020-08-20 23:01
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0016_auto_20200814_1512'),
    ]

    operations = [
        migrations.AddField(
            model_name='distribution',
            name='assigned',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='distribution',
            name='distributed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='distributionline',
            name='amount_verified',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='historicaldistribution',
            name='assigned',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicaldistribution',
            name='distributed',
            field=models.<PERSON>oleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicaldistributionline',
            name='amount_verified',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='distribution',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('VERIFIED', 'Verified')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='distributionline',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('VERIFIED', 'Verified')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicaldistribution',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('VERIFIED', 'Verified')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicaldistributionline',
            name='status',
            field=models.CharField(choices=[('ISSUED', 'Issued'), ('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('PROCESSING', 'Processing'), ('CLOSED', 'Closed'), ('VERIFIED', 'Verified')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalpackingline',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'results': []}, null=True),
        ),
        migrations.AlterField(
            model_name='packingline',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'results': []}, null=True),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2024-02-14 15:05
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0043_auto_20231115_0951'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalproductionorder',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('IN_QUALITY', 'In Quality'), ('COMPLETED', 'Completed'), ('OVERLOADED', 'Overloaded'), ('COMPLETED', 'Completed')], default='CREATED', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalproductionorderline',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('IN_QUALITY', 'In Quality'), ('COMPLETED', 'Completed'), ('OVERLOADED', 'Overloaded'), ('COMPLETED', 'Completed')], default='CREATED', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalpurchaseorder',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='CREATED', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalpurchaseorderline',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='CREATED', max_length=20),
        ),
        migrations.AlterField(
            model_name='productionorder',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('IN_QUALITY', 'In Quality'), ('COMPLETED', 'Completed'), ('OVERLOADED', 'Overloaded'), ('COMPLETED', 'Completed')], default='CREATED', max_length=20),
        ),
        migrations.AlterField(
            model_name='productionorderline',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('IN_QUALITY', 'In Quality'), ('COMPLETED', 'Completed'), ('OVERLOADED', 'Overloaded'), ('COMPLETED', 'Completed')], default='CREATED', max_length=20),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='CREATED', max_length=20),
        ),
        migrations.AlterField(
            model_name='purchaseorderline',
            name='status',
            field=models.CharField(choices=[('CREATED', 'Created'), ('INCOMPLETE', 'Incomplete'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='CREATED', max_length=20),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2025-02-17 23:24
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('operations', '0051_auto_20240816_1125'),
    ]

    operations = [
        migrations.CreateModel(
            name='Properties',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=40, unique=True)),
                ('display_name', models.Char<PERSON>ield(blank=True, max_length=40, null=True)),
                ('model', models.CharField(blank=True, max_length=40, null=True)),
                ('params', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='historicalpacking',
            name='badge',
            field=models.CharField(blank=True, choices=[('CORPORATE_SALES', 'corporate_sales')], max_length=35, null=True),
        ),
        migrations.AddField(
            model_name='packing',
            name='badge',
            field=models.CharField(blank=True, choices=[('CORPORATE_SALES', 'corporate_sales')], max_length=35, null=True),
        ),
        migrations.AlterField(
            model_name='historicalpacking',
            name='type',
            field=models.CharField(choices=[('BOX', 'Box')], default='BOX', max_length=15),
        ),
        migrations.AlterField(
            model_name='packing',
            name='type',
            field=models.CharField(choices=[('BOX', 'Box')], default='BOX', max_length=15),
        ),
        migrations.AddField(
            model_name='historicalpacking',
            name='params',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='operations.Properties'),
        ),
        migrations.AddField(
            model_name='packing',
            name='params',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='operations.Properties'),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-03-18 17:24
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0008_auto_20220318_0903'),
        ('operations', '0021_auto_20210818_1452'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExtrasActivity',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('store_orders', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True)),
                ('expected_date', models.DateField(blank=True, null=True)),
                ('work_order_external_id', models.CharField(max_length=150)),
                ('content_type', models.<PERSON>r<PERSON><PERSON>(choices=[('INTERNAL', 'Internal'), ('EXTERNAL', 'External')], default='INCOMPLETE', max_length=20)),
                ('properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
    ]

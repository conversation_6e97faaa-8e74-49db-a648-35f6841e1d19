# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-08-14 16:01
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import operations.models


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0014_auto_20200813_1142'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalpacking',
            name='verified_file',
            field=models.TextField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='historicalpacking',
            name='work_order',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='operations.WorkOrder'),
        ),
        migrations.AddField(
            model_name='historicalpackingline',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='packing',
            name='verified_file',
            field=models.FileField(blank=True, null=True, upload_to=operations.models.get_verified_file_path),
        ),
        migrations.AddField(
            model_name='packing',
            name='work_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='packings', to='operations.WorkOrder'),
        ),
        migrations.AddField(
            model_name='packingline',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
    ]

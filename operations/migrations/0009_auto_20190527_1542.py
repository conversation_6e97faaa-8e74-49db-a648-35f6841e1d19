# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-05-27 20:42
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0008_storeorder_properties'),
    ]

    operations = [
        migrations.AddField(
            model_name='contact',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcontact',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='contact',
            name='type_contact',
            field=models.CharField(choices=[('PROVIDER', 'Provider'), ('CELL_PROVIDER', 'Cell Provider'), ('CLIENT', 'Client'), ('SHIPPER', 'Shipper'), ('SHIP_TO', 'Ship To'), ('SHIP_FROM', 'Ship From')], default='PROVIDER', max_length=20),
        ),
        migrations.AlterField(
            model_name='contact',
            name='type_identification',
            field=models.CharField(blank=True, choices=[('CC', 'Cc'), ('NIT', 'Nit'), ('TI', 'Ti'), ('PASSPORT', 'Passport'), ('SHIPPER_NUMBER', 'Shipper Number')], default='NIT', max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='historicalcontact',
            name='type_contact',
            field=models.CharField(choices=[('PROVIDER', 'Provider'), ('CELL_PROVIDER', 'Cell Provider'), ('CLIENT', 'Client'), ('SHIPPER', 'Shipper'), ('SHIP_TO', 'Ship To'), ('SHIP_FROM', 'Ship From')], default='PROVIDER', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalcontact',
            name='type_identification',
            field=models.CharField(blank=True, choices=[('CC', 'Cc'), ('NIT', 'Nit'), ('TI', 'Ti'), ('PASSPORT', 'Passport'), ('SHIPPER_NUMBER', 'Shipper Number')], default='NIT', max_length=20, null=True),
        ),
    ]

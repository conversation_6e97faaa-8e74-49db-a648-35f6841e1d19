# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from inventory.models import SKU, Item
from ..models import Distribution, StoreOrderLine, StoreOrder


def create_lines_from_array(order, lines):
    """
    creates store order lines from {sku,amount} array
    """
    new_lines = []
    for line in lines:
        new_lines.append(
            StoreOrderLine(
                store_order=order,
                sku_id=line['sku_id'],
                amount=line['amount'],
                items={"items": []},
                created_by=order.created_by,
                modified_by=order.modified_by,
                params=line.get('params', {})
            )
        )
    StoreOrderLine.objects.bulk_create(new_lines)

    return new_lines


def refresh_store_order_progress(store_order_id):
    """
    Refresh store order progress according to child distributions
    :param store_order_id:
    :return:
    """
    store_order_object = StoreOrder.objects.get(pk=store_order_id)

    total_completed_lines = 0
    for line in store_order_object.lines.all():
        if line.amount == line.in_distribution_amount:
            total_completed_lines += 1
            line.status = "COMPLETED"
        else:
            line.status = "INCOMPLETE"
        line.save()

    if total_completed_lines == len(store_order_object.lines.all()):
        store_order_object.status = "COMPLETED"

    else:
        store_order_object.status = "INCOMPLETE"
    store_order_object.save()


def completedStoreOrder(store_order_ids=(), user_object=None):
    distribution_objects = Distribution.objects.filter(store_order_id__in=store_order_ids)
    distribution_ids = [distribution.pk for distribution in distribution_objects]

    from .distributions import completedDistribution
    completedDistribution(distribution_ids=distribution_ids, user_object=user_object)
    return distribution_objects


def sku_verification(sku: dict):
    validate_sku = SKU.objects.filter(external_id=sku["sku_external_id"]).first()
    if validate_sku:
        validate_inventory = Item.objects.filter(sku_id=validate_sku.id, state="PRESENT")
        if not validate_inventory:
            return False
        else:
            if len(validate_inventory) >= int(sku['amount']):
                return validate_inventory
            else:
                return False
    return None


def validate_inventory(detail: dict):
    list_sku = []
    list_inventory = []
    lines_store_order = []
    extras = detail['store_orders']
    for order in extras:
        lines_sku = order["lines"]
        if lines_sku:
            for line in lines_sku:
                validate_sku = sku_verification(sku=line)
                if validate_sku is None:
                    list_sku.append(line['sku_external_id'])
                if validate_sku is False:
                    list_inventory.append(line['sku_external_id'])
        else:
            lines_store_order.append(order['external_id'])

    if list_sku:
        body = {"message": "Sku with external_id does not exist {}".format(list_sku)}
        return body
    if list_inventory:
        body = {"message": "Skus with external_id does not have inventory {}".format(list_inventory)}
        return body
    if lines_store_order:
        body = {"message": "Store Order in external_id {} does not contain lines".format(lines_store_order)}
        return body

    return False


def validate_store_order(detail: dict):

    list_store_order = []
    extras = detail['store_orders']
    for order in extras:
        external_id = order["external_id"]
        store_order_object = StoreOrder.objects.filter(external_id=external_id).first()
        if store_order_object:
            list_store_order.append(external_id)
        else:
            pass

    if list_store_order:
        body = {"message": "Store Order in external_id {} already exists in the system".format(list_store_order)}
        return body

    return False


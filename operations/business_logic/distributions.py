# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from operations.business_logic import work_orders
from ..models import DistributionLine, Distribution, StoreOrder, WorkOrderLine


def create_automatic_headers(order):
    store_order = StoreOrder.objects.get(pk=order.pk)

    distribution = Distribution.objects.create(
        external_id=store_order.external_id if store_order.external_id else None,
        store_order=store_order,
        source=store_order.source,
        destination=store_order.destination,
        created_by=store_order.created_by,
        modified_by=store_order.modified_by,
        customer=store_order.customer,
        item_type=store_order.item_type
    )

    return distribution


def create_lines_from_array(order, lines):
    """
    creates distribution lines from {sku,amount} array
    """
    new_lines = []
    for line in lines:
        new_lines.append(
            DistributionLine(
                distribution=order,
                sku_id=line['sku_id'],
                amount=line['amount'],
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    DistributionLine.objects.bulk_create(new_lines)

    return new_lines


def close_distribution(distribution, user):
    # TODO you cant close COMPLETED Distributions

    # 1. Close distribution lines.
    distribution_lines = distribution.lines.all()
    for line in distribution_lines:
        line.amount = line.packed_amount
        line.status = "COMPLETED"
        line.save()

    # 2. Close child work orders.
    child_work_orders = distribution.work_orders.all()
    for order in child_work_orders:
        work_orders.close_work_order(order, user)

    # 3. Update  distribution  final state
    distribution.status = "COMPLETED"
    distribution.closed_by = user
    distribution.modified_by = user
    distribution.save()

    if distribution.store_order:
        from .store_orders import refresh_store_order_progress
        refresh_store_order_progress(distribution.store_order_id)


def update_distributed_action(distribution_ids):
    orders = Distribution.objects.filter(pk__in=distribution_ids).update(distributed=True)
    return orders


def refresh_distribution_progress(distribution_object):
    total_completed_lines = 0
    for line in distribution_object.lines.all():
        if line.amount == line.in_work_order_amount:
            total_completed_lines += 1
            line.status = "COMPLETED"
        else:
            line.status = "INCOMPLETE"
        line.save()

    if total_completed_lines == len(distribution_object.lines.all()):
        distribution_object.status = "COMPLETED"
    else:
        distribution_object.status = "INCOMPLETE"

    distribution_object.save()

    if distribution_object.store_order:
        from .store_orders import refresh_store_order_progress
        refresh_store_order_progress(distribution_object.store_order_id)


def refreshProgressVerified(distribution_object):
    total_completed_lines = 0
    for line in distribution_object.lines.all():
        if line.amount == line.amount_verified:
            total_completed_lines += 1
            line.assigned = True
        else:
            line.assigned = False
        line.save()

    if total_completed_lines == len(distribution_object.lines.all()):
        distribution_object.assigned = True
    else:
        distribution_object.assigned = False
    distribution_object.save()


def completedDistribution(distribution_ids=(), user_object=None):
    distribution_objects = Distribution.objects.filter(pk__in=distribution_ids)
    work_order_objects = list()

    def multipleWorkOrder(external_id: str):
        work_order_line_objects = WorkOrderLine.objects.filter(properties__store_order__contains=external_id)
        work_order_objects = [work_order_line.work_order for work_order_line in work_order_line_objects]
        if work_order_objects:
            return work_order_objects[0]

    def refreshWorkOrderStatus(work_order_objects):
        for order in work_order_objects:
            order.status = 'COMPLETED'
            order.modified_by = user_object
            for line in order.lines.all():
                line.status = 'COMPLETED'
                line.modified_by = user_object
                line.save()
            order.save()

    def refreshDistributionStatus(distribution_objects):
        for distribution in distribution_objects:
            distribution.status = 'COMPLETED'
            distribution.modified_by = user_object
            for line in distribution.lines.all():
                line.status = 'COMPLETED'
                line.modified_by = user_object
                line.save()
            distribution.save()

    def refreshStoreOrderStatus(distribution_objects):
        for distribution in distribution_objects:
            if distribution.store_order:
                from .store_orders import refresh_store_order_progress
                refresh_store_order_progress(distribution.store_order_id)

    for distribution in distribution_objects:
        multiple_work_orders = multipleWorkOrder(distribution.external_id)
        if distribution.work_orders.all():
            work_order_objects.append(distribution.work_orders.all()[0])
            refreshWorkOrderStatus(work_order_objects)
        elif multiple_work_orders:
            work_order_objects.append(multiple_work_orders)
            refreshWorkOrderStatus(work_order_objects)
        else:
            break

    refreshDistributionStatus(distribution_objects)
    refreshStoreOrderStatus(distribution_objects)
    return distribution_objects


def refresh_distribution_progress_multiple(distribution_object):
    total_completed_lines = 0
    for line in distribution_object.lines.all():
        if line.amount == line.in_work_order_amount:
            total_completed_lines += 1
            line.status = "COMPLETED"
        else:
            line.status = "DISTRIBUTED"
        line.save()

    if total_completed_lines == len(distribution_object.lines.all()):
        distribution_object.status = "COMPLETED"
    else:
        distribution_object.status = "DISTRIBUTED"

    distribution_object.save()

    if distribution_object.store_order:
        from .store_orders import refresh_store_order_progress
        refresh_store_order_progress(distribution_object.store_order_id)


def refresh_distribution_close_multiple(distribution_external_id):
    distribution_object = Distribution.objects.get(external_id=distribution_external_id)
    distribution_object.lines.update(status='INCOMPLETE')

    total_completed_lines = 0
    for line in distribution_object.lines.all():
        if line.amount == line.in_work_order_amount:
            total_completed_lines += 1
            line.status = "COMPLETED"
        else:
            line.status = "INCOMPLETE"
        line.save()

    if total_completed_lines == len(distribution_object.lines.all()):
        distribution_object.status = "COMPLETED"
    else:
        distribution_object.status = "INCOMPLETE"

    distribution_object.save()

    if distribution_object.store_order:
        from .store_orders import refresh_store_order_progress
        refresh_store_order_progress(distribution_object.store_order_id)
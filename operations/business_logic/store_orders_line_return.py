from ..models import *

def create_return_lines_from_array(order, lines, type_sku_in_lines="OBJECT"):
    """
        create shipping return lines from {sku,amount} array.
    """
    storeorder_return_lines = []
    for line in lines:
        sku_id = None
        if type_sku_in_lines == "OBJECT":
            sku_id = line["sku"].id
        elif type_sku_in_lines == "ID":
            sku_id = line["sku_id"]
        storeorder_return_lines.append(
            StoreOrderReturnLine(
                store_order_return=order,
                sku_id=sku_id,
                amount=line['amount'],
                costs=line.get('costs', {}),
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    StoreOrderReturnLine.objects.bulk_create(storeorder_return_lines)
    return lines



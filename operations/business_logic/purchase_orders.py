# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from common.models import User
import integrations.tasks as integration_tasks
import integrations.utils as integration_utils
from inventory.models import SKU
import inventory.utils as inventory_utils
from inventory.business_logic import moves as moves_logic
from rfid.business_logic.print_orders import create_gtin_lines_from_array_purchase
from rfid.models import GTINPrintOrderLine, PrintOrder
from operations.business_logic import production_orders, store_orders
from ..models import PurchaseOrder, PurchaseOrderLine, ProductionOrderLine, StoreOrderLine, ProductionOrder


def create_lines_from_array(order, lines):
    """
    creates purchase order lines from {sku,amount} array
    """
    new_lines = []
    for line in lines:
        new_lines.append(
            PurchaseOrderLine(
                purchase_order=order,
                sku=line['sku'],
                amount=line['amount'],
                external_id=line.get('external_id_line', None),
                items={"items": []},
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    PurchaseOrderLine.objects.bulk_create(new_lines)

    return new_lines


def transform_lines_external_to_id(lines):
    """
    creates purchase order lines from {sku,amount} array
    """
    skus_list = []
    new_lines = []
    validated_sku_amount = {}  # {"--sku_id--", "--amount--"}

    for line in lines:
        skus_list.append(line['sku_id'])
        if validated_sku_amount.get(line['sku_id']):
            validated_sku_amount[line['sku_id']] = validated_sku_amount[line['sku_id']] + line['amount']
        else:
            validated_sku_amount[line['sku_id']] = line['amount']

    skus_ids = SKU.objects.filter(external_id__in=skus_list).values('id', 'external_id')

    for line in lines:
        validated_amount = validated_sku_amount.pop(line['sku_id'], None)
        if validated_amount:
            for ids in skus_ids:
                sku_external_id = ids.get('external_id')
                sku_id = ids.get('id')
                dict_lines = {}
                if sku_external_id == line['sku_id']:
                    dict_lines['sku_id'] = sku_id
                    dict_lines['amount'] = validated_amount
                    new_lines.append(dict_lines)
                    break

    return new_lines


def fill_order(purchase_order: PurchaseOrder, item_ids: list, reporter: User):
    new_items = []
    skus_sorted = inventory_utils.sort_items_by_sku(items_ids=item_ids, customer_id=purchase_order.customer_id,
                                                    filter_states=["PRESENT"], output_format="DICT")
    sku_ids = list(skus_sorted.keys())
    purchase_order_lines = purchase_order.lines.filter(sku_id__in=sku_ids).exclude(status='COMPLETED')
    for purchase_order_line in purchase_order_lines:
        reprint_amount = 0
        sku_item_ids = []
        item_ids_to_update = []
        sku_item_ids = skus_sorted[purchase_order_line.sku_id]
        for item in sku_item_ids:
            if item in set(purchase_order_line.expected_items):
                item_ids_to_update.append(item)

        for item_update in item_ids_to_update[:]:
            if item_update in purchase_order_line.items["items"]:
                # Do not take something that is already on the lines
                item_ids_to_update.remove(item_update)
            else:
                pass

        # Reprint
        reprint_amount = purchase_order_line.amount - len(purchase_order_line.items["items"])
        if len(item_ids_to_update) > reprint_amount:
            item_ids_to_update = item_ids_to_update[:reprint_amount]

        purchase_order_line.add_items(items=item_ids_to_update)
        new_items.extend(item_ids_to_update)

    has_pending_lines = purchase_order.lines.exclude(status='COMPLETED').exists()
    location_external_id = purchase_order.entry_location.external_id
    if has_pending_lines:
        purchase_order.status = 'CREATED'
    else:
        purchase_order.status = 'COMPLETED'
        if location_external_id != '11':
            module_config = integration_utils.verified_integration_module(
                customer_id=purchase_order.customer_id, kong_module__name='PURCHASE_ORDER_COMPLETE',
                active_integration=True)
            if module_config:
                request_body = {
                    "detail-type": "PURCHASE_ORDER_COMPLETE",
                    "purchase_order_id": purchase_order.id
                }
                integration_tasks.integration_module_request(
                    integration_module_id=module_config.id, body=request_body)

        module_integrator = integration_utils.verified_integration_module(
            customer_id=purchase_order.customer_id, kong_module__name='AUTOMATIC_TRANSFERS',
            active_integration=True)
        if module_integrator:

            request_body = {
                "detail-type": "AUTOMATIC_TRANSFERS",
                "order_id": str(purchase_order.pk),
                "type_order_external_id": "PO",
                "params": {"model_name": type(purchase_order).__name__,
                           "model_external_id": purchase_order.external_id,
                           "model_pk": purchase_order.pk},
                "items": new_items,
                "entry_location_name": purchase_order.entry_location.name,
                "customer_id": purchase_order.customer.pk,
                "type": "TRANSFER",
                "entry_location_id": purchase_order.entry_location.pk
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_integrator.id, body=request_body)

    purchase_order.save()
    return purchase_order


def cancel_progress(purchase_order: PurchaseOrder, reporter: User,
                    order_line_id: str = None, all_lines: bool = False):
    order_lines = purchase_order.lines.all()
    order_lines_to_update = order_lines
    if order_line_id and not all_lines:
        order_lines_to_update = order_lines.filter(id=order_line_id)
    for line in order_lines_to_update:
        totals_item_list = line.items["items"]
        if totals_item_list:
            moves_logic.update_items_state(items=totals_item_list, state='PRESENT', reporter=reporter)
    order_lines_to_update.update(items={"items": []}, modified_by=reporter, status="CREATED")
    purchase_order.modified_by = reporter
    purchase_order.status = "CREATED"
    purchase_order.save()


def destroy_lines_order(order, lines, print_order: PrintOrder, model_name):
    for line in lines:
        if model_name in ("PurchaseOrder", "ProductionOrder"):
            if model_name == 'PurchaseOrder':
                purchase_order_line = PurchaseOrderLine.objects.filter(purchase_order_id=order.pk,
                                                                       sku_id=line["sku_id"], amount=line["amount"]).delete()
                print_order_line = GTINPrintOrderLine.objects.filter(print_order_id=print_order.pk, sku_id=line["sku_id"],
                                                             amount=line["amount"]).delete()
            elif model_name == 'ProductionOrder':
                purchase_order_line = ProductionOrderLine.objects.filter(production_order_id=order.pk,
                                                                       sku_id=line["sku_id"],
                                                                       amount=line["amount"]).delete()
                print_order_line = GTINPrintOrderLine.objects.filter(print_order_id=print_order.pk,
                                                                     sku_id=line["sku_id"],
                                                                     amount=line["amount"]).delete()
        else:
            if model_name == 'StoreOrder':
                StoreOrderLine.objects.filter(store_order_id=order.pk,
                                              sku_id=line["sku_id"],amount=line["amount"]).delete()


def create_lines_order(order, lines, print_order: PrintOrder, model_name):
    for line in lines:
        if model_name in ("PurchaseOrder", "ProductionOrder"):
            if model_name == 'PurchaseOrder':
                create_lines_from_array(order=order, lines=lines)
                create_gtin_lines_from_array_purchase(order=print_order, lines=lines)
            elif model_name == 'ProductionOrder':
                production_orders.create_lines_from_array(order=order, lines=lines)
                create_gtin_lines_from_array_purchase(order=print_order, lines=lines)
        else:
            if model_name == 'StoreOrder':
                store_orders.create_lines_from_array(order=order, lines=lines)


def release_purchase_order(order, items: list):
    PurchaseOrder.objects.filter(pk=order.pk).update(status="CREATED")
    lines = PurchaseOrderLine.objects.filter(purchase_order_id=order.pk)
    position = None
    for line in lines:
        for item in items:
            line_to_updated = PurchaseOrderLine.objects.filter(purchase_order_id=order.pk, pk=line.pk,
                                                               items__items__contains=[item]).first()
            if line_to_updated:
                position = line_to_updated.items["items"].index(item)
                line_to_updated.items["items"].pop(position)
                line_to_updated.status = "INCOMPLETE"
                line_to_updated.save()
    return lines


def release_production_order(order, items:list):
    ProductionOrder.objects.filter(pk=order.pk).update(status="CREATED")
    lines = ProductionOrderLine.objects.filter(production_order_id=order.pk)
    position = None
    for line in lines:
        for item in items:
            line_to_updated = ProductionOrderLine.objects.filter(production_order_id=order.pk, pk=line.pk,
                                                               items__items__contains=[item]).first()
            if line_to_updated:
                position = line_to_updated.items["items"].index(item)
                line_to_updated.items["items"].pop(position)
                line_to_updated.status = "INCOMPLETE"
                line_to_updated.save()
    return lines


def validate_order_state(order):
    purchase_order_object = PurchaseOrder.objects.filter(pk=order.pk).first()

    if purchase_order_object:
        if purchase_order_object.status == "COMPLETED":
            return True
        else:
            return None


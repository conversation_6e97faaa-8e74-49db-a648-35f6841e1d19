# -*- coding: utf-8 -*-

from __future__ import unicode_literals

import csv
import io
import json
import logging
import os
from django.db.models import Q
from collections import defaultdict
from datetime import datetime

from django.db.models.expressions import RawSQL

import inventory.utils as inventory_utils
from django.core.files.base import File
from django.db import transaction
import integrations.utils as integration_utils
import integrations.tasks as integration_tasks

from common.models import User
from customers.models import Customer
from inventory.business_logic import moves as moves_logic
from inventory.models import Item, Location, Move, SKU, Trace
import inventory.tasks as inventory_tasks
from operations.business_logic import work_orders
from operations.models import Distribution, DistributionLine, PackingLine, Packing, WorkOrder, WorkOrderLine
from django.db.models.expressions import RawSQL

def arrange_items_by_sku(items_ids):
    item_objects = Item.objects.filter(pk__in=items_ids).exclude(state='INACTIVE')
    skus_dict = defaultdict(list)
    for item in item_objects:
        skus_dict[item.sku.pk].append(item)
    return skus_dict


def create_lines_from_items(packing, items):
    new_lines = []
    traces = []
    packing.status = "FILLED"
    packing.save()
    items = [item[:24] for item in items]
    skus_dict = arrange_items_by_sku(items)
    for sku_id, item_list in skus_dict.items():
        new_lines.append(
            PackingLine(
                packing=packing,
                sku_id=sku_id,
                amount=len(item_list),
                items={"items": [item.pk for item in item_list]},
                created_by=packing.created_by,
                modified_by=packing.modified_by
            )
        )

    item_objects = Item.objects.filter(pk__in=items).exclude(state='INACTIVE')
    for item in item_objects:
        in_trace = Trace(
            item=item,
            reporter=packing.created_by,
            reporter_source="HANDHELD",
            action="PACK",
            customer=packing.customer,
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)

    PackingLine.objects.bulk_create(new_lines)
    item_objects.update(state='PACKED', modified=datetime.now())

    return new_lines


def create_lines_from_array(packing, lines):
    new_lines = []
    for line in lines:
        new_lines.append(
            PackingLine(
                packing=packing,
                sku_id=line['sku_id'],
                amount=line['amount'],
                items={"items": []},
                created_by=packing.created_by,
                modified_by=packing.modified_by
            )
        )
    PackingLine.objects.bulk_create(new_lines)
    return new_lines


def check_packing(work_order_id, items):
    if items:
        work_order = WorkOrder.objects.get(pk=work_order_id)
        lines = WorkOrderLine.objects.filter(work_order_id=work_order_id)
        skus_dict = arrange_items_by_sku(items_ids=items)

        for sku_id, item_list in skus_dict.items():
            for line in lines:
                if line.sku_id == sku_id:
                    total_work_order = work_order.total_amount
                    total_packed = work_order.packed_amount
                    amount_available = (total_work_order - total_packed)
                    if amount_available <= 0:
                        return False
                    elif amount_available > 0:
                        return True
    else:
        work_order = WorkOrder.objects.get(pk=work_order_id)
        lines = WorkOrderLine.objects.filter(work_order_id=work_order_id)
        for line in lines:
            total_work_order = work_order.total_amount
            total_packed = work_order.packed_amount
            amount_available = (total_work_order - total_packed)
            if amount_available <= 0:
                return False
            elif amount_available > 0:
                return True


def destroy_packing_items(packing):
    line = PackingLine.objects.filter(packing=packing)
    new_items = []
    traces = []
    for items in line:
        for line in items.items_list:
            new_items.append(line)

    for item in new_items:
        in_trace = Trace(
            item_id=item,
            reporter=packing.created_by,
            reporter_source="WEB_APP",
            action="UNPACK",
            customer=packing.customer
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)

    Item.objects.filter(pk__in=new_items).exclude(state='INACTIVE').update(state='PRESENT', modified=datetime.now())
    return new_items

def validate_state_items(items):
    allowed_states = ['PRESENT', 'UNAVAILABLE']
    invalid_items = list(Item.objects.filter(pk__in=items).exclude(state__in=allowed_states))
    if len(invalid_items) > 0:
        return True
    else:
        return False
def validate_packing_picking(packing):
    packing_lines = packing.lines.all()
    total_items_filled = sum(len(line.items.get("items", [])) for line in packing.lines.all())
    total_amount = sum(line.amount for line in packing.lines.all())
    if (total_items_filled == total_amount):
        packing.status = 'FILLED'
    for line in packing_lines:
        total_items_line_filled = len(line.items.get("items", []))

        if (line.amount == total_items_line_filled):
            line.status = 'FILLED'
            line.save()


def fill_packing(packing, reporter: User, item_ids=list):
    skus_dict = arrange_items_by_sku(item_ids)
    packing_lines = packing.lines.all()
    #   TODO:  Verify sku amounts with given item ids
    with transaction.atomic():
        if packing.work_order:
            check_packing(work_order_id=packing.work_order.pk, items=item_ids)
        for sku_id, item_list in skus_dict.items():
            filtered_lines = filter(lambda line: line.sku_id == sku_id, packing_lines)
            for line in filtered_lines:
                line.add_items(items=[item.pk for item in item_list])

        moves_logic.update_items_state(items=item_ids, state='PACKED', reporter=reporter)
        work_orders.refresh_work_order_progress(packing.work_order_id)
        validate_packing_picking(packing)
    packing.save()
    return packing


def generate_packing_verified_file(packing):
    """
    Generates Packing verified File
    """

    packing_items = get_packing_items(packing=packing)
    # Create lines for summary file sku lines (counted)
    sorted_summary_sku = inventory_utils.sort_items_by_sku(items_ids=packing_items, customer_id=packing.customer_id)
    abs_path = os.path.abspath(os.path.dirname(__file__))

    file_name = "packing_{}_summary_file.txt".format(packing.pk)
    path = os.path.join(abs_path, "../../inventory/tmp/" + file_name)
    packing_verified_file_tmp = io.open(path, 'w', encoding='utf-8')
    if packing.work_order:
        work_order = packing.work_order.external_id
        work_order_id = packing.work_order.pk
        location = packing.work_order.source.pk
        if packing.work_order.distribution:
            distribution = packing.work_order.distribution.external_id
            distribution_id = packing.work_order.distribution.pk
            if packing.work_order.distribution.store_order:
                store_order = packing.work_order.distribution.store_order.external_id
                store_order_id = packing.work_order.distribution.store_order.id

            else:
                store_order = 'NULL'
                store_order_id = 'NULL'

        else:
            distribution = 'NULL'
            distribution_id = 'NULL'
            store_order = 'NULL'
            store_order_id = 'NULL'

    else:
        location = 'NULL'
        work_order = 'NULL'
        work_order_id = 'NULL'

        distribution = 'NULL'
        distribution_id = 'NULL'
        store_order = 'NULL'
        store_order_id = 'NULL'

    packing_verified_file_tmp.write( "Packing_location: {}\n"
                                    "Packing: {},{}\n"
                                    "Work_order: {},{}\n"
                                    "Distribution: {},{}\n"
                                    "Store_order: {},{}\n"
                                    "Created_by: {}\n"
                                    "Type_box: {}\n \n"
                                   .format(location, packing.pk, packing.external_id, work_order_id, work_order,
                                           distribution_id, distribution, store_order_id, store_order,
                                           packing.created_by.username, packing.type))

    packing_verified_file_tmp.write("LINES \n")

    for result in sorted_summary_sku:
        sku = result['sku']
        items = result['items']
        line = str('{},{},{}').format(sku.id, sku.external_id, len(items))
        packing_verified_file_tmp.write(line)
        packing_verified_file_tmp.write("\n")

    packing_verified_file_tmp.close()
    """
    summary_file = open(path, 'rb')
    file_name = "packing_{}_summary_file.txt".format(packing.pk)

    packing.verified_file.save(file_name, File(summary_file))
    """
    file_name = "packing_{}_summary_file.txt".format(packing.pk)
    with open(path, mode="rb") as f:
        packing.verified_file = File(f, name=file_name)
        packing.save(update_fields=["verified_file"])
    os.remove(path)

    logging.warning('Generate file to Packing verified: {}'.format(packing.pk))



def received_elements(skus_list, packing, pedido):
    lines_caja = packing.lines.all()
    lines_pedidos = DistributionLine.objects.filter(distribution__external_id=pedido, assigned=False)

    for line_pe in lines_pedidos:
        line_pe.assigned = True
        line_pe.save()
        if line_pe.sku in skus_list:
            line = lines_caja.get(sku_id=line_pe.sku.id)
            line.amount_verified += line_pe.amount
            line.properties['store_order'].append(str(pedido))
            line.save()


def refresh_packing_progress(packing):
    total_completed_lines = 0
    for line in packing.lines.all():
        if line.status == 'VERIFIED':
            total_completed_lines += 1

    if total_completed_lines == len(packing.lines.all()):
        packing.status = 'VERIFIED'
    else:
        packing.status = 'DISPATCHED'

    packing.save()


def get_packing_items(packing):
    items = packing.items_list
    return items


def generate_packing_items_file(packing):
    """
    Generates Packing Items File
    """
    # packing = Packing.objects.get(pk=packing_id)
    packing_items = get_packing_items(packing=packing)
    # Create lines for items file lines (counted)
    sorted_summary_sku = inventory_utils.sort_items_by_sku(items_ids=packing_items, customer_id=packing.customer.pk)
    abs_path = os.path.abspath(os.path.dirname(__file__))

    file_name = "packing_{}_items_file.txt".format(packing.pk)
    path = os.path.join(abs_path, "../../inventory/tmp/" + file_name)
    packing_items_file_tmp = io.open(path, 'w', encoding='utf-8')

    if packing.work_order:
        work_order = packing.work_order.external_id
        work_order_id = packing.work_order.pk
        location = packing.work_order.source.pk
        if packing.work_order.distribution:
            distribution = packing.work_order.distribution.external_id
            distribution_id = packing.work_order.distribution.pk
            if packing.work_order.distribution.store_order:
                store_order = packing.work_order.distribution.store_order.external_id
                store_order_id = packing.work_order.distribution.store_order.id

            else:
                store_order = 'NULL'
                store_order_id = 'NULL'

        else:
            distribution = 'NULL'
            distribution_id = 'NULL'
            store_order = 'NULL'
            store_order_id = 'NULL'

    else:
        location = 'NULL'
        work_order = 'NULL'
        work_order_id = 'NULL'
        distribution = 'NULL'
        distribution_id = 'NULL'
        store_order = 'NULL'
        store_order_id = 'NULL'

    packing_items_file_tmp.write("Packing_location: {}\n"
                                 "Packing: {},{}\n"
                                 "Work_order: {},{}\n"
                                 "Distribution: {},{}\n"
                                 "Store_order: {},{}\n \n".format(
        location, packing.pk, packing.external_id, work_order_id, work_order, distribution_id, distribution,
        store_order_id, store_order))

    packing_items_file_tmp.write("LINES \n")

    for result in sorted_summary_sku:
        sku = result['sku']
        items = result['items']

        for item in items:
            line = str('{},{},{},{}').format(item, sku.id, sku.external_id, sku.display_name)
            packing_items_file_tmp.write(line)
            packing_items_file_tmp.write("\n")

    packing_items_file_tmp.close()
    """"
    summary_file = open(path, 'rb')
    file_name = "packing_{}_items_file.txt".format(packing.pk)
    packing.refresh_from_db()
    packing.items_file.save(file_name, File(summary_file))
    """
    file_name = "packing_{}_items_file.txt".format(packing.pk)

    with open(path, mode="rb") as f:
        packing.items_file = File(f, name=file_name)
        packing.save(update_fields=["items_file"])
    os.remove(path)


def generate_packing_summary_file(packing):
    """
    Generates Packing Summary File
    """
    packing_items = get_packing_items(packing=packing)
    # Create lines for summary file sku lines (counted)
    sorted_summary_sku = inventory_utils.sort_items_by_sku(items_ids=packing_items, customer_id=packing.customer.pk)
    abs_path = os.path.abspath(os.path.dirname(__file__))

    file_name = "packing_{}_summary_file.txt".format(packing.pk)
    path = os.path.join(abs_path, "../../inventory/tmp/" + file_name)
    packing_summary_file_tmp = io.open(path, 'w', encoding='utf-8')

    if packing.work_order:
        work_order = packing.work_order.external_id
        work_order_id = packing.work_order.pk
        location = packing.work_order.source.pk
        if packing.work_order.distribution:
            distribution = packing.work_order.distribution.external_id
            distribution_id = packing.work_order.distribution.pk
            if packing.work_order.distribution.store_order:
                store_order = packing.work_order.distribution.store_order.external_id
                store_order_id = packing.work_order.distribution.store_order.id

            else:
                store_order = 'NULL'
                store_order_id = 'NULL'

        else:
            distribution = 'NULL'
            distribution_id = 'NULL'
            store_order = 'NULL'
            store_order_id = 'NULL'

    else:
        location = 'NULL'
        work_order = 'NULL'
        work_order_id = 'NULL'

        distribution = 'NULL'
        distribution_id = 'NULL'
        store_order = 'NULL'
        store_order_id = 'NULL'

    packing_summary_file_tmp.write("Packing_location: {}\n"
                                   "Packing: {},{}\n"
                                   "Work_order: {},{}\n"
                                   "Distribution: {},{}\n"
                                   "Store_order: {},{}\n \n".format(
        location, packing.pk, packing.external_id, work_order_id, work_order, distribution_id, distribution,
        store_order_id, store_order))

    packing_summary_file_tmp.write("LINES \n")

    for result in sorted_summary_sku:
        sku = result['sku']
        items = result['items']
        line = str('{},{},{}').format(sku.id, sku.external_id, len(items))
        packing_summary_file_tmp.write(line)
        packing_summary_file_tmp.write("\n")

    packing_summary_file_tmp.close()
    """"
    summary_file = open(path, 'rb')
    file_name = "packing_{}_summary_file.txt".format(packing.pk)

    packing.refresh_from_db()
    packing.summary_file.save(file_name, File(summary_file))
    """
    file_name = "packing_{}_summary_file.txt".format(packing.pk)
    with open(path, mode="rb") as f:
        packing.summary_file = File(f, name=file_name)
        packing.save(update_fields=["summary_file"])

    logging.warning('Generate file to Packing create: {}'.format(packing.pk))
    os.remove(path)


def items_available(items, location_id):
    verified_states = ["PACKED", "IN_TRANSFER", "DISPATCH", "VERIFIED"]
    items_unavailable = Item.objects.filter(pk__in=items, state__in=verified_states)
    # items_available = Item.objects.filter(pk__in=items, state='PRESENT').exclude(current_location_id=location_id)

    if len(items_unavailable) > 0:  # or len(items_available) > 0:
        return False
    else:
        return True


def validate_packing_lines(work_order_id, items, customer_id):
    q_objects = Q()
    for item in items:
        q_objects |= Q(items__items__contains=[item])
    items_db = PackingLine.objects.filter(packing__work_order_id=work_order_id).filter(q_objects)
    if len(items_db) > 0:  # or len(items_available) > 0:
        return False
    else:
        return True


def search_packing(sku_id):
    sku_object = SKU.objects.get(pk=sku_id)

    packings_sku = PackingLine.objects.filter(sku=sku_object)

    lines_available = []

    for packing in packings_sku:
        if packing.amount == packing.amount_verified:
            logging.info('The amount is verified')
        elif packing.amount > packing.amount_verified:
            lines_available.append(packing)

    lines_response = []

    for line in lines_available:
        packing = line.packing
        amount = line.amount - line.amount_verified

        dict_lines = {}
        dict_lines = {'packing': packing, 'amount': amount}

        lines_response.append(dict_lines)

    return lines_response


def generate_file_packing_list(packings=[], move=None):
    """
    Generates Packing list File
    """

    abs_path = os.path.abspath(os.path.dirname(__file__))
    file_name = "packing_dispatch_list.csv"
    path = os.path.join(abs_path, "../tmp/" + file_name)
    packing_file_tmp = open(path, 'w')
    writer = csv.writer(packing_file_tmp)

    for packing_object in packings:
        packing = packing_object
        items = packing_object.items_list
        sorted_audit_items = inventory_utils.sort_items_by_sku(items_ids=items, customer_id=packing.customer.pk)
        """
        writer.writerow(
            [packing.external_id.encode('utf-8')]
        )
        """
        for result in sorted_audit_items:
            sku = result['sku']
            items = result['items']

            writer.writerow(
                [
                    sku.external_id.encode('utf-8'),
                    len(items),
                ],
            )

        writer.writerow('\n')

    packing_file_tmp.close()
    #summary_file = open(path, "rb")
    file_name = "packing_dispatch_list.csv"
    #move.packing_list_file.save(file_name, File(summary_file))
    logging.warning('Generate file to Packing list of move: {}'.format(move.pk))
    #os.remove(path)


def create_move_from_packings(source, destination, created_by, external_id, customer, packings=[]):
    packing_item_ids = []

    packing_ids = []
    for pack in packings:
        packing_item_ids.extend(
            pack.items_list
        )
        packing_ids.append(pack.id)

    with transaction.atomic():
        move = Move.objects.create(
            source=source,
            destination=destination,
            type='TRANSFER',
            external_id=external_id,
            created_by=created_by,
            modified_by=created_by,
            customer=customer
        )  # create move header
        moves_logic.create_lines_from_items(move=move, items=packing_item_ids)
        
        for packing_id in packing_ids:
            update_state_of_packing(packing_id)
        
        # update_state_of_packing(packing_ids[0])

        for pack in packings:
            related_model_instance = move
            if related_model_instance:
                model_name = type(related_model_instance).__name__
                model_pk = related_model_instance.pk
                model_external_id = related_model_instance.external_id
                new_data_json = json.dumps({
                    "model": model_name,
                    "model_pk": model_pk,
                    "model_external_id": model_external_id
                })
                Packing.objects.filter(id=pack.id).update(
                    params=RawSQL(
                        """
                        (
                            COALESCE(params::jsonb, '{}'::jsonb)
                            - 'model'
                            - 'model_pk'
                            - 'model_external_id'
                        ) || %s::jsonb
                        """,
                        [new_data_json]
                    )
                )
        amount_packings_without_work_orders = Packing.objects.filter(id__in=packing_ids, work_order__isnull=True).count()
        if amount_packings_without_work_orders == len(packing_ids):
            module_config = integration_utils.verified_integration_module(
                customer_id=customer.pk, kong_module__name='SEND_LPN_DINAMIX',
                active_integration=True)
            if module_config:
                request_body = {
                    "detail-type": "CONFIGS_LPN",
                    "packing_ids": packing_ids,
                    "information": True,
                    "destination_location_external_id": move.destination.external_id,
                    "destination_location_name": move.destination.name,
                    "source_external_id": move.source.external_id,
                    "move_id": move.pk,
                    "move_external_id": move.external_id,
                    "creation": True
                }
                integration_tasks.integration_module_request(
                    integration_module_id=module_config.id, body=request_body)
        #Packing.objects.filter(pk__in=packing_ids).update(status="DISPATCHED", modified=datetime.now())
        #PackingLine.objects.filter(packing_id__in=packings).update(status="DISPATCHED", modified=datetime.now())

    return move


def update_state_of_packing(packing):
    packing_obj = Packing.objects.get(pk=packing)
    packing_obj.status = "DISPATCHED"
    packing_obj.modified = datetime.now()
    packing_obj.save(update_fields=["status", "modified"])

    packing_lines = PackingLine.objects.filter(packing_id=packing)
    for packing_line in packing_lines:
        packing_line.status = "DISPATCHED"
        packing_line.modified = datetime.now()
        packing_line.save(update_fields=["status", "modified"])


def update_state_of_items(packing, location: Location, reporter: User, reporter_source: str):
    packing_object = Packing.objects.get(pk=packing.pk)
    line = PackingLine.objects.filter(packing=packing)
    new_items = []
    for items in line:
        for line in items.items_list:
            new_items.append(line)
    related_model_instance = packing_object
    properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) - 'model' - 'model_pk' - 'model_external_id'"
    properties_query_params = []
    if related_model_instance:
        model_name = type(related_model_instance).__name__
        model_pk = related_model_instance.pk
        model_external_id = related_model_instance.external_id
        item_properties_str = json.dumps({
            "model": model_name,
            "model_pk": model_pk,
            "model_external_id": model_external_id
        })
        properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) || %s::jsonb"
        properties_query_params = [item_properties_str]

    items = Item.objects.filter(pk__in=new_items)
    items.update(modified=datetime.now().astimezone(), modified_by=reporter,
                 properties=RawSQL(properties_query_str, properties_query_params))
    traces = []
    for item in new_items:
        in_trace = Trace(
            created_by=reporter,
            modified_by=reporter,
            item_id=item,
            reporter=reporter,
            location=location,
            reporter_source=reporter_source,
            action="VERIFIED",
            customer=packing.customer
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)
    Item.objects.filter(pk__in=new_items).update(
        state='VERIFIED', modified=datetime.now().astimezone(), modified_by=reporter)

    packing.modified = datetime.now().astimezone()
    packing.modified_by = reporter
    if not packing.work_order:
        packing.status = 'VERIFIED'
        Item.objects.filter(pk__in=new_items).update(
            state='VERIFIED', modified=datetime.now().astimezone(), modified_by=reporter)
        packing.save()
        if packing.type != "BOX":
            packing.status = 'DISPATCHED'
            Item.objects.filter(pk__in=new_items).update(
                state='DISPATCH', modified=datetime.now().astimezone(), modified_by=reporter)
            packing.save()
    elif packing.work_order:
        if packing.work_order.destination and not packing.work_order.item_type:
            move = create_move_from_packings(packings=[packing], external_id=f"{packing.work_order.external_id}-{packing.id}",
                                             source=packing.work_order.source, destination=packing.work_order.destination,
                                             created_by=packing.modified_by, customer=packing.customer)
            inventory_tasks.apply_move(move.pk)
            update_state_of_packing(packing.pk)

            print("Integration Create Move to Arco")
            event_type = "ARCO_MOVE_SHIPPING"
            module_config = integration_utils.verified_integration_module(customer_id=2,
                                                                          kong_module__name=event_type,
                                                                          active_integration=True)
            if module_config:
                request_body = {
                    "detail-type": event_type,
                    "move_id": move.pk,
                    "source_external_id": move.source.external_id,
                    "destination_external_id": move.destination.external_id
                }
                integration_tasks.integration_module_request(integration_module_id=module_config.id,
                                                             body=request_body)

        else:
            update_state_of_packing(packing.pk)
            Packing.objects.filter(pk=packing.pk).update(status='DISPATCHED')
            Item.objects.filter(pk__in=new_items).update(
                state='DISPATCH', modified=datetime.now().astimezone(), modified_by=reporter)
    logging.warning('Verified Packing: {}, Status: {}'.format(packing.pk, packing.status))

    return new_items


def validate_amount(lines_packing, sku, ditribution_amount):
    if lines_packing.filter(sku_id=sku).values('amount_verified')[0].get("amount_verified") == ditribution_amount:
        return True
    else:
        return False


# Verified amounts of distributions web with packing.
def validate_lines(packing_object):
    distribution_external_ids = packing_object.work_order.distribution_list

    packing_lines = packing_object.lines.all()
    distribution_lines = DistributionLine.objects.filter(distribution__external_id__in=distribution_external_ids). \
        exclude(assigned=True)

    packing_sku_dict = set()
    packing_lines_in_distribution_lines = []

    for line in packing_lines:
        packing_sku_dict.add(line.sku.id)

    for external_id in distribution_external_ids:
        distribution_lines_filter = distribution_lines.filter(distribution__external_id=external_id)
        distribution_sku_dict = set()
        for line in distribution_lines_filter:
            distribution_sku_dict.add(line.sku.id)

        result = distribution_sku_dict & packing_sku_dict
        if result:
            result = list(result)
            packing_lines_in_distribution_lines.extend(result)

    # Get objects of lines with sku in distributions and packings.
    packing_lines_objects = packing_lines.filter(sku_id__in=list(packing_lines_in_distribution_lines))
    distribution_lines_objects = distribution_lines.filter(sku_id__in=list(packing_lines_in_distribution_lines))

    def execute_results(distribution_line_object, packing_line_object, amount):
        results = {
            'store_order': distribution_line_object.distribution.external_id,
            'amount': amount
        }
        distribution_line_object.amount_verified += amount
        packing_line_object.amount_verified += amount
        packing_line_object.properties['results'].append(results)
        packing_line_object.save(update_fields=["amount_verified", "properties"])
        distribution_line_object.save()

    for packing_line in packing_lines_objects:
        for distribution_line in distribution_lines_objects:
            # Validation sku.
            if packing_line.sku.id == distribution_line.sku.id:
                # Validate amount verified of packing.
                if packing_line.amount_verified < packing_line.amount:
                    # Validate amount verified of distribution.
                    if distribution_line.amount_verified < distribution_line.amount:
                        # Validate amount packing verified vs amount verified distribution line.
                        new_amount_packing_line = packing_line.amount - packing_line.amount_verified
                        new_amount_distribution_line = distribution_line.amount - distribution_line.amount_verified

                        if new_amount_packing_line != 0:
                            if new_amount_packing_line >= new_amount_distribution_line:
                                execute_results(distribution_line_object=distribution_line,
                                                packing_line_object=packing_line, amount=new_amount_distribution_line)

                            elif new_amount_packing_line <= new_amount_distribution_line:
                                execute_results(distribution_line_object=distribution_line,
                                                packing_line_object=packing_line, amount=new_amount_packing_line)
    return distribution_lines_objects


def generate_file_multiple(packing_object):
    # Get all packing line.
    for packing_line in packing_object.lines.all():
        results = packing_line.properties['results']
        # Get distribution in line.
        for result in results:
            distribution_object = Distribution.objects.get(external_id=result['store_order'])
            # Generate file by distribution.
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "distribution_{}_{}_verified_summary_file.txt".format(distribution_object.id,
                                                                              distribution_object.external_id)
            path = os.path.join(abs_path, "../../inventory/tmp/" + file_name)
            distribution_summary_file_tmp = io.open(path, 'w', encoding='utf-8')
            # Order by headers file.
            distribution_summary_file_tmp.write("Packing_location: {}\n"
                                                "Packing: {},{}\n"
                                                "Work_order: {},{}\n"
                                                "Distribution: {},{}\n"
                                                "Store_order: {},{}\n \n".format('NULL', packing_line.packing.id,
                                                                                 packing_line.packing.external_id,
                                                                                 packing_line.packing.work_order.id,
                                                                                 packing_line.packing.work_order.external_id,
                                                                                 distribution_object.id,
                                                                                 distribution_object.external_id,
                                                                                 distribution_object.store_order.id,
                                                                                 distribution_object.store_order.external_id
                                                                                 ))
            distribution_summary_file_tmp.write("LINES \n")
            # Order by sku/amount.
            line = str('{},{},{}').format(packing_line.sku.id, packing_line.sku.external_id, result['amount'])
            distribution_summary_file_tmp.write(line)
            distribution_summary_file_tmp.write("\n")
            distribution_summary_file_tmp.close()
            summary_file = open(path, 'rb')
            distribution_object.verified_file.save(file_name, File(summary_file))
            os.remove(path)
    return packing_object


def update_properties_items(packing, location: Location, reporter: User, reporter_source: str):
    packing_object = Packing.objects.get(pk=packing.pk)
    line = PackingLine.objects.filter(packing=packing)
    new_items = []
    for items in line:
        for line in items.items_list:
            new_items.append(line)
    related_model_instance = packing_object
    properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) - 'model' - 'model_pk' - 'model_external_id'"
    properties_query_params = []
    if related_model_instance:
        model_name = type(related_model_instance).__name__
        model_pk = related_model_instance.pk
        model_external_id = related_model_instance.external_id
        item_properties_str = json.dumps({
            "model": model_name,
            "model_pk": model_pk,
            "model_external_id": model_external_id
        })
        properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) || %s::jsonb"
        properties_query_params = [item_properties_str]

    items = Item.objects.filter(pk__in=new_items)
    items.update(modified=datetime.now().astimezone(), modified_by=reporter,
                 properties=RawSQL(properties_query_str, properties_query_params))


def receive_packing(packing, item_ids, reporter):
    move_object = Move.objects.filter(pk=int(packing.params.get("model_pk"))).first()
    module_config = integration_utils.verified_integration_module(
        customer_id=move_object.customer.id, kong_module__name='RECEPTION_LPN',
        active_integration=True)

    # packing_ids = list(Packing.objects.filter(params__model_pk=move_object.pk).values_list('id', flat=True))
    packing_id = packing.id
    if module_config:
        move_external_id = None
        if move_object.external_id:
            move_external_id = move_object.external_id
        request_body = {
            "detail-type": "CONFIGS_LPN",
            "packing_ids": [packing_id],
            "destination_location_external_id": move_object.destination.external_id,
            "destination_location_name": move_object.destination.name,
            "dispatch": True,
            "move_external_id": move_external_id,
            "move_id": move_object.pk
        }
        integration_tasks.integration_module_request(integration_module_id=module_config.id,
                                                     body=request_body)
    inventory_tasks.receive_items.delay(list_items=item_ids, move_id=move_object.pk, user_id=reporter.pk)
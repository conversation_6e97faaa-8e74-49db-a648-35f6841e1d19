# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from common.models import User
import integrations.tasks as integration_tasks
import integrations.utils as integration_utils
from inventory.business_logic import moves as moves_logic
from inventory.models import SKU
import inventory.utils as inventory_utils
from ..models import ProductionOrder, ProductionOrderLine


def transform_lines_external_to_id(lines):
    """
    creates production order lines from {sku,amount} array
    """
    skus_list = []
    new_lines = []
    validated_sku_amount = {}  # {"--sku_id--", "--amount--"}

    for line in lines:
        skus_list.append(line['sku_id'])
        if validated_sku_amount.get(line['sku_id']):
            validated_sku_amount[line['sku_id']] = validated_sku_amount[line['sku_id']] + line['amount']
        else:
            validated_sku_amount[line['sku_id']] = line['amount']

    skus_ids = SKU.objects.filter(external_id__in=skus_list).values('id', 'external_id')

    for line in lines:
        validated_amount = validated_sku_amount.pop(line['sku_id'], None)
        if validated_amount:
            for ids in skus_ids:
                sku_external_id = ids.get('external_id')
                sku_id = ids.get('id')
                dict_lines = {}
                if sku_external_id == line['sku_id']:
                    dict_lines['sku_id'] = sku_id
                    dict_lines['amount'] = validated_amount
                    new_lines.append(dict_lines)
                    break

    return new_lines


def create_lines_from_array(order, lines):
    """
    creates production order lines from {sku,amount} array
    """
    new_lines = []
    for line in lines:
        new_lines.append(
            ProductionOrderLine(
                production_order=order,
                sku=line['sku'],
                amount=line['amount'],
                items={"items": []},
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    ProductionOrderLine.objects.bulk_create(new_lines)

    return new_lines


def fill_order(production_order: ProductionOrder.objects, item_ids: list, reporter: User):
    new_items = []
    skus_sorted = inventory_utils.sort_items_by_sku(items_ids=item_ids, customer_id=production_order.customer_id,
                                                    filter_states=["PRESENT"], output_format="DICT")
    sku_ids = list(skus_sorted.keys())
    production_order_lines = production_order.lines.filter(sku_id__in=sku_ids).exclude(status='COMPLETED')
    for production_order_line in production_order_lines:
        reprint_amount = 0
        sku_item_ids = []
        item_ids_to_update = []
        sku_item_ids = skus_sorted[production_order_line.sku_id]
        for item in sku_item_ids:
            if item in set(production_order_line.expected_items):
                item_ids_to_update.append(item)

        for item_update in item_ids_to_update[:]:
            if item_update in production_order_line.items["items"]:
                # Do not take something that is already on the lines
                item_ids_to_update.remove(item_update)
            else:
                pass

        # Reprint
        reprint_amount = production_order_line.amount - len(production_order_line.items["items"])
        if len(item_ids_to_update) > reprint_amount:
            item_ids_to_update = item_ids_to_update[:reprint_amount]

        production_order_line.add_items(items=item_ids_to_update)
        new_items.extend(item_ids_to_update)

    has_pending_lines = production_order.lines.exclude(status='COMPLETED').exists()
    location_external_id = production_order.entry_location.external_id
    if has_pending_lines:
        production_order.status = 'CREATED'
    else:
        production_order.status = 'COMPLETED'

        module_integrator = integration_utils.verified_integration_module(
            customer_id=production_order.customer_id, kong_module__name='AUTOMATIC_TRANSFERS',
            active_integration=True)
        if module_integrator:
            request_body = {
                "detail-type": "AUTOMATIC_TRANSFERS",
                "order_id": str(production_order.pk),
                "type_order_external_id": "PO",
                "params": {"model_name": type(production_order).__name__,
                           "model_external_id": production_order.external_id,
                           "model_pk": production_order.pk},
                "items": production_order_line.items["items"],
                "entry_location_name": production_order.entry_location.name,
                "customer_id": production_order.customer.pk,
                "type": "TRANSFER",
                "entry_location_id": production_order.entry_location.pk
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_integrator.id, body=request_body)

        module_config = integration_utils.verified_integration_module(
            customer_id=production_order.customer_id, kong_module__name='PRODUCTION_ORDER_COMPLETE',
            active_integration=True)
        if location_external_id != '11':
            if module_config:
                request_body = {
                    "detail-type": "PRODUCTION_ORDER_COMPLETE",
                    "production_order_id": production_order.id
                }
                integration_tasks.integration_module_request(
                    integration_module_id=module_config.id, body=request_body)
    production_order.save()
    return production_order


def cancel_progress(production_order: ProductionOrder, reporter: User,
                    order_line_id: str = None, all_lines: bool = False):
    order_lines = production_order.lines.all()
    order_lines_to_update = order_lines
    if order_line_id and not all_lines:
        order_lines_to_update = order_lines.filter(id=order_line_id)
    for line in order_lines_to_update:
        totals_item_list = line.items["items"]
        if totals_item_list:
            moves_logic.update_items_state(items=totals_item_list, state='PRESENT', reporter=reporter)
    order_lines_to_update.update(items={"items": []}, modified_by=reporter, status="CREATED")
    production_order.modified_by = reporter
    production_order.status = "CREATED"
    production_order.save()


def validate_order_state(order):
    production_order_object = ProductionOrder.objects.filter(pk=order.pk).first()

    if production_order_object:
        if production_order_object.status == "COMPLETED":
            return True
        else:
            return None


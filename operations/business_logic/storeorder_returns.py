from common.models import User
import integrations.tasks as integration_tasks
import integrations.utils as integration_utils
from inventory.business_logic import moves as moves_logic
from inventory.models import Trace
import inventory.utils as inventory_utils
from operations.models import StoreOrderReturn


def fill_order(order_return: StoreOrderReturn, item_ids: list, reporter: User, reporter_source: str = "HANDHELD"):

    skus_sorted = inventory_utils.sort_items_by_sku(items_ids=item_ids, customer_id=order_return.customer_id,
                                                    filter_states=["DISPATCH"], output_format="DICT",)
    sku_ids = list(skus_sorted.keys())
    shipping_return_lines = order_return.lines.filter(sku_id__in=sku_ids).exclude(status='COMPLETED')
    item_ids_to_update = []
    for shipping_return_line in shipping_return_lines:
        sku_item_ids = skus_sorted[shipping_return_line.sku_id]
        item_ids_to_update += sku_item_ids
        shipping_return_line.add_items(items=sku_item_ids)

    if item_ids_to_update:
        item_ids_to_update = moves_logic.move_items_return(
            items=item_ids_to_update, destination=order_return.entry_location, reporter=reporter,
            related_model_instance=order_return)
        # Create traces
        traces = []
        for item in item_ids_to_update:
            in_trace = Trace(
                item_id=item.id,
                location=item.current_location,
                reporter=reporter,
                reporter_source=reporter_source,
                action="RETURN",
                customer=item.customer,
                additional_data=item.properties,
                created_by=reporter,
                modified_by= reporter
            )
            traces.append(in_trace)
        Trace.objects.bulk_create(traces)
    has_pending_lines = order_return.lines.exclude(status='COMPLETED').exists()
    if has_pending_lines:
        order_return.status = 'CREATED'
    else:
        order_return.status = 'COMPLETED'
        module_config = integration_utils.verified_integration_module(
            customer_id=order_return.customer_id, kong_module__name='SHIPPING_RETURN_COMPLETE',
            active_integration=True)
        if module_config:
            request_body = {
                "detail-type": "SHIPPING_RETURN_COMPLETE",
                "production_order_id": order_return.id
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_config.id, body=request_body)
    order_return.save()
    return order_return


def cancel_progress(shipping_return: StoreOrderReturn, reporter: User, return_line_id: str = None,
                    all_lines: bool = False, reporter_source: str = "HANDHELD"):
    order_lines = shipping_return.lines.all()
    order_lines_to_update = order_lines
    if return_line_id and not all_lines:
        order_lines_to_update = order_lines.filter(id=return_line_id)
    for line in order_lines_to_update:
        totals_item_list = line.items["items"]
        if totals_item_list:
            totals_item_list = moves_logic.update_items_state(
                items=totals_item_list, state='DISPATCH', reporter=reporter)
            # Create traces
            traces = []
            for item_id in totals_item_list:
                in_trace = Trace(
                    item_id=item_id,
                    location=shipping_return.entry_location,
                    reporter=reporter,
                    reporter_source=reporter_source,
                    action="VERIFIED",
                    customer=shipping_return.customer,
                    additional_data={"model": "SHIPPING_RETURN", "pk": shipping_return.pk}
                )
                traces.append(in_trace)
            Trace.objects.bulk_create(traces)
    order_lines_to_update.update(items={"items": []}, modified_by=reporter, status="CREATED")
    shipping_return.modified_by = reporter
    shipping_return.status = "CREATED"
    shipping_return.save()

# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from common.models import User
# Create your tests here.
from django.contrib.auth.models import Permission
from django.test import Client
from django.urls import reverse
from inventory.models import SKU, SKUGroup, Location, Warehouse, Item, SubLocation
from operations.models import Packing, PackingLine, WorkOrder, WorkOrderLine, Contact, StoreOrder, StoreOrderLine, \
    Distribution, DistributionLine, ProductionOrder, ProductionOrderLine, PurchaseOrder, PurchaseOrderLine
from rest_framework.test import APITestCase
from rest_framework.utils import json


class OperationPackingAPITestCase(APITestCase):
    def setUp(self):
        self.client = Client()
        self.normal_user = User.objects.create_user(
            username="monkey", password="monkey1234", email="<EMAIL>", is_staff=True
        )
        self.normal_user.save()

        self.super_user = User.objects.create_superuser(
            username="super_monkey", password="super_monkey1234", email="<EMAIL>"
        )
        self.super_user.save()
        self.warehouse_mex = Warehouse.objects.create(
            name="MAIN WAREHOUSE MEXICO",
            display_name="Main - Warehouse -MEXICO",
        )
        self.warehouse_mex.save()

        # create source location.
        self.source = Location.objects.create(
            id=1,
            name="MAIN WAREHOUSE",
            display_name="Main - Warehouse",
            type='SIMPLE',
            warehouse=self.warehouse_mex,
        )
        self.source.save()
        self.destination = Location.objects.create(
            id=2,
            name="MAIN STORE",
            display_name="Main - Store",
            type='SIMPLE'
        )
        self.destination.save()
        self.sub_location = SubLocation.objects.create(
            code='A1',
            location=self.source
        )
        self.sub_location.save()
        # create sku_group.

        self.sku_group = SKUGroup.objects.create(
            name='ZAPATOS',
        )
        self.sku_group.save()
        self.sku = SKU.objects.create(
            id=1,
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004068",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku.save()
        self.item = Item.objects.create(
            id="9202226913468bc",
            serial=1,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item.save()
        self.item_two = Item.objects.create(
            id="9202226913468bd",
            serial=2,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,

        )
        self.item_two.save()

        self.item_three = Item.objects.create(
            id="9202226913468be",
            serial=3,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item_three.save()

        self.work_order = WorkOrder.objects.create(
            finished_date="2018-08-20",
            finished=True,
            assigned_to_id=self.normal_user.id,
            destination_id=self.destination.id,
            source_id=self.source.id,
            external_id="AA1",
            id=1
        )
        self.work_order.save()

        self.work_order_line = WorkOrderLine.objects.create(
            work_order_id=1,
            amount=2,
            sku_id=self.sku.id
        )
        self.work_order_line.save()

        self.packing = Packing.objects.create(
            id=1,
            external_id='A1',
            status='VERIFIED'
        )
        self.packing.save()

        self.packing_line = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line.save()
        self.packing_two = Packing.objects.create(
            id=2,
            external_id='A2',
            status='VERIFIED'
        )
        self.packing_two.save()

        self.packing_line_two = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line_two.save()

        permission = Permission.objects.get(name='Can add packing')
        permission_two = Permission.objects.get(name='Can add packing line')

        permission_delete = Permission.objects.get(name='Can delete packing')
        u = User.objects.get(pk=self.normal_user.pk)
        u.user_permissions.add(permission)
        u.user_permissions.add(permission_delete)
        u.user_permissions.add(permission_two)

    def test_POSTing_a_new_packing_with_items(self):
        data = {"items":
                    [self.item.id, self.item_two.id, self.item_three.id],
                "external_id": "ZA", "id": 3}
        url = reverse("packing-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_new_packing_with_lines(self):
        data = {"lines": [{"sku_id": self.sku, "amount": 2}],
                "external_id": "ZB", "id": 4}
        url = reverse("packing-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_lines_packing(self):
        data = {
            "packing": self.packing.pk,
            "sku_id": self.sku.id,
            "amount": 2,
            "items": {"items": [self.item.id, self.item_two.id]}
        }
        url = reverse("packing-line-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_new_packing_with_lines_and_work_order(self):
        data = {"lines": [{"sku_id": self.sku, "amount": 2}],
                "external_id": "ZC", "id": 5, "work_order_id": self.work_order.pk}
        url = reverse("packing-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_destroy_packing(self):
        url = reverse("packing-detail", args=[self.packing.pk])
        self.client.login(username="monkey", password="monkey1234")
        response = self.client.delete(url)
        self.assertEqual(response.status_code, 204)

    def test_POSTing_a_new_dispatch(self):
        data = {"packing_ids": [self.packing.id, self.packing_two.id], "destination_id": self.destination.id,
                "external_id": "ABC123"}
        url = reverse("packing-dispatch")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 202)

    def test_POSTing_a_fill_packing(self):
        data = {"item_ids": [self.item.pk, self.item_two.id, self.item_three.id]}
        url = reverse("packing-fill", args=[self.packing.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)

    def test_GETing_a_packing(self):
        url = reverse("packing-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_GETing_a_lines_packing(self):
        url = reverse("packing-lines", args=[self.packing.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_GETing_a_items_packing(self):
        url = reverse("packing-items", args=[self.packing.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)


class OperationsAPITestCase(APITestCase):
    def setUp(self):
        self.client = Client()
        self.normal_user = User.objects.create_user(
            username="monkey", password="monkey1234", email="<EMAIL>", is_staff=True
        )
        self.normal_user.save()

        self.super_user = User.objects.create_superuser(
            username="super_monkey", password="super_monkey1234", email="<EMAIL>"
        )
        self.super_user.save()
        self.contact = Contact.objects.create(
            name='camilo',
            type_contact='PROVIDER',
            type_identification='NIT',
            identification='1239487566H'
        )
        self.contact.save()

        self.warehouse_mex = Warehouse.objects.create(
            name="MAIN WAREHOUSE MEXICO",
            display_name="Main - Warehouse -MEXICO",
        )
        self.warehouse_mex.save()

        # create source location.
        self.source = Location.objects.create(
            id=1,
            name="MAIN WAREHOUSE",
            display_name="Main - Warehouse",
            type='SIMPLE',
            warehouse=self.warehouse_mex,
        )
        self.source.save()
        self.destination = Location.objects.create(
            id=2,
            name="MAIN STORE",
            display_name="Main - Store",
            type='SIMPLE'
        )
        self.destination.save()
        self.sub_location = SubLocation.objects.create(
            code='A1',
            location=self.source
        )
        self.sub_location.save()
        # create sku_group.

        self.sku_group = SKUGroup.objects.create(
            name='ZAPATOS',
        )
        self.sku_group.save()
        self.sku = SKU.objects.create(
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004068",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku.save()
        self.item = Item.objects.create(
            id="9202226913468bc",
            serial=1,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item.save()
        self.item_two = Item.objects.create(
            id="9202226913468bd",
            serial=2,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,

        )
        self.item_two.save()

        self.item_three = Item.objects.create(
            id="9202226913468be",
            serial=3,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item_three.save()

        self.work_order = WorkOrder.objects.create(
            finished_date="2018-08-20",
            finished=True,
            assigned_to_id=self.normal_user.id,
            destination_id=self.destination.id,
            source_id=self.source.id,
            external_id="AA1",
            id=1
        )
        self.work_order.save()

        self.work_order_line = WorkOrderLine.objects.create(
            work_order_id=1,
            amount=2,
            sku_id=self.sku.id
        )
        self.work_order_line.save()

        self.packing = Packing.objects.create(
            id=1,
            external_id='A1',
            status='VERIFIED'
        )
        self.packing.save()

        self.packing_line = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line.save()
        self.packing_two = Packing.objects.create(
            id=2,
            external_id='A2',
            status='VERIFIED'
        )
        self.packing_two.save()

        self.packing_line_two = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line_two.save()

        self.store_order = StoreOrder.objects.create(
            requester=self.contact,
            source=self.source,
            destination=self.destination,
            id=2
        )
        self.store_order.save()

        self.store_order_line = StoreOrderLine.objects.create(
            store_order=self.store_order,
            sku=self.sku,
            amount=2
        )
        self.store_order_line.save()

        self.distribution = Distribution.objects.create(
            finished=True,
            store_order=self.store_order,
            source=self.source,
            destination=self.destination
        )
        self.distribution.save()

        self.distribution_line = DistributionLine.objects.create(
            distribution=self.distribution,
            sku=self.sku,
            amount=2
        )
        self.distribution_line.save()

        self.work_order = WorkOrder.objects.create(
            id=2,
            assigned_to=self.normal_user,
            distribution=self.distribution,
            source=self.source,
            destination=self.destination
        )
        self.work_order.save()

        self.work_order_line = WorkOrderLine.objects.create(
            work_order=self.work_order,
            sku=self.sku,
            amount=2

        )
        self.work_order_line.save()

        permission = Permission.objects.get(name='Can add store order')
        permission_two = Permission.objects.get(name='Can add store order line')
        permission_three = Permission.objects.get(name='Can add distribution line')
        permission_four = Permission.objects.get(name='Can add distribution')
        permission_five = Permission.objects.get(name='Can add work order')
        permission_six = Permission.objects.get(name='Can add work order line')
        permission_delete = Permission.objects.get(name='Can delete store order')

        u = User.objects.get(pk=self.normal_user.pk)

        u.user_permissions.add(permission)
        u.user_permissions.add(permission_delete)
        u.user_permissions.add(permission_two)
        u.user_permissions.add(permission_three)
        u.user_permissions.add(permission_four)
        u.user_permissions.add(permission_five)
        u.user_permissions.add(permission_six)

    def test_POSTing_a_new_store_order(self):
        data = {"destination_id": self.destination.id,
                "source_id": self.source.id,
                "requester_id": self.contact.id,
                "lines": [{"sku_id": self.sku.id, "amount": 2}],
                "external_id": "ABC2"}

        url = reverse("store-order-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_lines_store_order(self):
        data = {
              "amount": 2,
              "sku_id": self.sku.id,
              "store_order_id": self.store_order.pk
        }
        url = reverse("store-order-line-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_GETing_a_lines_of_store_order(self):
        url = reverse("store-order-lines", args=[self.store_order.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_a_new_distribution_with_store_order(self):
        data = {
            "store_order_id": self.store_order.id,
            "lines": [
                {"sku_id": self.sku.id, "amount": 2}
            ],
            "destination_id": self.destination.id,
            "source_id": self.source.id,
            "external_id": "ABC1"
        }
        url = reverse("distribution-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_lines_distribution(self):
        data = {
              "amount": 2,
              "sku_id": self.sku.id,
              "distribution_id": self.distribution.pk
            }
        url = reverse("distribution-line-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_close_distribution(self):
        url = reverse("distribution-actions-close", args=[self.distribution.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url)
        self.assertEqual(response.status_code, 202)

    def test_GETing_a_new_distribution_with_store_order(self):
        url = reverse("distribution-lines", args=[self.distribution.id])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_a_new_distribution_without_store_order(self):
        data = {
            "lines": [
                {"sku_id": self.sku.id, "amount": 2}
            ],
            "destination_id": self.destination.id,
            "source_id": self.source.id,
            "external_id": "ABC1"
        }
        url = reverse("distribution-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_new_work_order_with_distribution(self):
        data = {
            "finished_date": "2018-08-05",
            "lines": [
                {"sku_id": self.sku.id, "amount": 2}
            ],
            "finished": True,
            "distribution_id": self.distribution.id,
            "assigned_to_id": self.normal_user.id,
            "destination_id": self.destination.id,
            "source_id": self.source.id,
            "external_id": "BC2",
            "id": 3
        }
        url = reverse("work-order-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_GETing_lines_work_order_with_distribution(self):
        url = reverse("work-order-lines", args=[self.work_order.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_a_lines_work_order(self):
        data = {
              "amount": 2,
              "sku_id": self.sku.id,
              "work_order_id": self.work_order.pk
            }
        url = reverse("work-order-line-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_close_work_order(self):
        url = reverse("work-order-actions-close", args=[self.work_order.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url)
        self.assertEqual(response.status_code, 202)

class OperationsProductionAPITestCase(APITestCase):
    def setUp(self):
        self.client = Client()
        self.normal_user = User.objects.create_user(
            username="monkey", password="monkey1234", email="<EMAIL>", is_staff=True
        )
        self.normal_user.save()

        self.super_user = User.objects.create_superuser(
            username="super_monkey", password="super_monkey1234", email="<EMAIL>"
        )
        self.super_user.save()
        self.contact = Contact.objects.create(
            name='camilo',
            type_contact='PROVIDER',
            type_identification='NIT',
            identification='1239487566H'
        )
        self.contact.save()

        self.warehouse_mex = Warehouse.objects.create(
            name="MAIN WAREHOUSE MEXICO",
            display_name="Main - Warehouse -MEXICO",
        )
        self.warehouse_mex.save()

        # create source location.
        self.source = Location.objects.create(
            id=1,
            name="MAIN WAREHOUSE",
            display_name="Main - Warehouse",
            type='SIMPLE',
            warehouse=self.warehouse_mex,
        )
        self.source.save()
        self.destination = Location.objects.create(
            id=2,
            name="MAIN STORE",
            display_name="Main - Store",
            type='SIMPLE'
        )
        self.destination.save()
        self.sub_location = SubLocation.objects.create(
            code='A1',
            location=self.source
        )
        self.sub_location.save()
        # create sku_group.

        self.sku_group = SKUGroup.objects.create(
            name='ZAPATOS',
        )
        self.sku_group.save()
        self.sku = SKU.objects.create(
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004068",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku.save()
        self.item = Item.objects.create(
            id="9202226913468bc",
            serial=1,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item.save()
        self.item_two = Item.objects.create(
            id="9202226913468bd",
            serial=2,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,

        )
        self.item_two.save()

        self.item_three = Item.objects.create(
            id="9202226913468be",
            serial=3,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item_three.save()

        self.work_order = WorkOrder.objects.create(
            finished_date="2018-08-20",
            finished=True,
            assigned_to_id=self.normal_user.id,
            destination_id=self.destination.id,
            source_id=self.source.id,
            external_id="AA1",
            id=1
        )
        self.work_order.save()

        self.work_order_line = WorkOrderLine.objects.create(
            work_order_id=1,
            amount=2,
            sku_id=self.sku.id
        )
        self.work_order_line.save()

        self.packing = Packing.objects.create(
            id=1,
            external_id='A1',
            status='VERIFIED'
        )
        self.packing.save()

        self.packing_line = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line.save()
        self.packing_two = Packing.objects.create(
            id=2,
            external_id='A2',
            status='VERIFIED'
        )
        self.packing_two.save()

        self.packing_line_two = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line_two.save()

        self.store_order = StoreOrder.objects.create(
            requester=self.contact,
            source=self.source,
            destination=self.destination,
            id=2
        )
        self.store_order.save()

        self.store_order_line = StoreOrderLine.objects.create(
            store_order=self.store_order,
            sku=self.sku,
            amount=2
        )
        self.store_order_line.save()

        self.distribution = Distribution.objects.create(
            finished=True,
            store_order=self.store_order,
            source=self.source,
            destination=self.destination
        )
        self.distribution.save()

        self.distribution_line = DistributionLine.objects.create(
            distribution=self.distribution,
            sku=self.sku,
            amount=2
        )
        self.distribution_line.save()

        self.work_order = WorkOrder.objects.create(
            id=2,
            assigned_to=self.normal_user,
            distribution=self.distribution,
            source=self.source,
            destination=self.destination
        )
        self.work_order.save()

        self.work_order_line = WorkOrderLine.objects.create(
            work_order=self.work_order,
            sku=self.sku,
            amount=2

        )
        self.work_order_line.save()

        self.production_order = ProductionOrder.objects.create(
            entry_location=self.destination,
            provider=self.contact
        )
        self.production_order.save()

        self.production_order_line = ProductionOrderLine.objects.create(
            production_order=self.production_order,
            sku=self.sku,
            amount=2
        )
        self.production_order_line.save()

        permission = Permission.objects.get(name='Can add production order')
        permission_two = Permission.objects.get(name='Can add production order line')

        u = User.objects.get(pk=self.normal_user.pk)

        u.user_permissions.add(permission)
        u.user_permissions.add(permission_two)

    def test_POSTing_a_new_production_order(self):
        data = {
            "external_id": "ABC1",
            "provider_id": self.contact.pk,
            "lines": [
                {"sku_id": self.sku.id, "amount": 2}
            ],
            "entry_location_id": self.destination.pk
        }
        url = reverse("production-order-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_GETing_a_lines_production_order(self):
        url = reverse("production-order-lines", args=[self.production_order.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_a_lines_production_order(self):
        data = {
              "amount": 2,
              "sku_id": self.sku.id,
              "production_order_id": self.production_order.pk
            }
        url = reverse("production-order-line-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)


class OperationsPurchaseOrderAPITestCase(APITestCase):
    def setUp(self):
        self.client = Client()
        self.normal_user = User.objects.create_user(
            username="monkey", password="monkey1234", email="<EMAIL>", is_staff=True
        )
        self.normal_user.save()

        self.super_user = User.objects.create_superuser(
            username="super_monkey", password="super_monkey1234", email="<EMAIL>"
        )
        self.super_user.save()
        self.contact = Contact.objects.create(
            name='camilo',
            type_contact='PROVIDER',
            type_identification='NIT',
            identification='1239487566H'
        )
        self.contact.save()

        self.warehouse_mex = Warehouse.objects.create(
            name="MAIN WAREHOUSE MEXICO",
            display_name="Main - Warehouse -MEXICO",
        )
        self.warehouse_mex.save()

        # create source location.
        self.source = Location.objects.create(
            id=1,
            name="MAIN WAREHOUSE",
            display_name="Main - Warehouse",
            type='SIMPLE',
            warehouse=self.warehouse_mex,
        )
        self.source.save()
        self.destination = Location.objects.create(
            id=2,
            name="MAIN STORE",
            display_name="Main - Store",
            type='SIMPLE'
        )
        self.destination.save()
        self.sub_location = SubLocation.objects.create(
            code='A1',
            location=self.source
        )
        self.sub_location.save()
        # create sku_group.

        self.sku_group = SKUGroup.objects.create(
            name='ZAPATOS',
        )
        self.sku_group.save()
        self.sku = SKU.objects.create(
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004068",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku.save()
        self.item = Item.objects.create(
            id="9202226913468bc",
            serial=1,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item.save()
        self.item_two = Item.objects.create(
            id="9202226913468bd",
            serial=2,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,

        )
        self.item_two.save()

        self.item_three = Item.objects.create(
            id="9202226913468be",
            serial=3,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item_three.save()

        self.work_order = WorkOrder.objects.create(
            finished_date="2018-08-20",
            finished=True,
            assigned_to_id=self.normal_user.id,
            destination_id=self.destination.id,
            source_id=self.source.id,
            external_id="AA1",
            id=1
        )
        self.work_order.save()

        self.work_order_line = WorkOrderLine.objects.create(
            work_order_id=1,
            amount=2,
            sku_id=self.sku.id
        )
        self.work_order_line.save()

        self.packing = Packing.objects.create(
            id=1,
            external_id='A1',
            status='VERIFIED'
        )
        self.packing.save()

        self.packing_line = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line.save()
        self.packing_two = Packing.objects.create(
            id=2,
            external_id='A2',
            status='VERIFIED'
        )
        self.packing_two.save()

        self.packing_line_two = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line_two.save()

        self.store_order = StoreOrder.objects.create(
            requester=self.contact,
            source=self.source,
            destination=self.destination,
            id=2
        )
        self.store_order.save()

        self.store_order_line = StoreOrderLine.objects.create(
            store_order=self.store_order,
            sku=self.sku,
            amount=2
        )
        self.store_order_line.save()

        self.distribution = Distribution.objects.create(
            finished=True,
            store_order=self.store_order,
            source=self.source,
            destination=self.destination
        )
        self.distribution.save()

        self.distribution_line = DistributionLine.objects.create(
            distribution=self.distribution,
            sku=self.sku,
            amount=2
        )
        self.distribution_line.save()

        self.work_order = WorkOrder.objects.create(
            id=2,
            assigned_to=self.normal_user,
            distribution=self.distribution,
            source=self.source,
            destination=self.destination
        )
        self.work_order.save()

        self.work_order_line = WorkOrderLine.objects.create(
            work_order=self.work_order,
            sku=self.sku,
            amount=2

        )
        self.work_order_line.save()

        self.purchase_order = PurchaseOrder.objects.create(
            entry_location=self.destination,
            provider=self.contact
        )
        self.purchase_order.save()

        self.purchase_order_line = PurchaseOrderLine.objects.create(
            purchase_order=self.purchase_order,
            sku=self.sku,
            amount=2
        )
        self.purchase_order_line.save()

        permission = Permission.objects.get(name='Can add purchase order')
        permission_two = Permission.objects.get(name='Can add purchase order line')

        u = User.objects.get(pk=self.normal_user.pk)

        u.user_permissions.add(permission)
        u.user_permissions.add(permission_two)

    def test_POSTing_a_new_purchase_order(self):
        data = {
            "external_id": "ABC1",
            "provider_id": self.contact.pk,
            "lines": [
                {"sku_id": self.sku.id, "amount": 2}
            ],
            "entry_location_id": self.destination.pk
        }
        url = reverse("purchase-order-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_GETing_a_lines_purchase_order(self):
        url = reverse("purchase-order-lines", args=[self.purchase_order.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_a_lines_purchase_order(self):
        data = {
              "amount": 2,
              "sku_id": self.sku.id,
              "purchase_order_id": self.purchase_order.pk
            }
        url = reverse("purchase-order-line-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)



from inventory.models import Item
from rfid.models import GTINPrintOrderLine, PrintOrder


def sort_properties(criteria, items_ids, customer_id):
    item_objects = Item.objects.select_related("current_location") \
        .filter(pk__in=items_ids, sku__customer_id=customer_id)
    sorter_dict = {}  # {"-- sku --": {"-- state --": {"-- location --": [-- items --]}}}
    sorted_items = []
    result_location_types = []
    for item in item_objects:
        properties = None
        item_type = None
        production_order_properties = None
        purchase_order_properties = None
        if {"PROPERTIES"} & criteria:
            line = GTINPrintOrderLine.objects.filter(issued_items__epcs__contains=item.pk).first()
            print_order = PrintOrder.objects.filter(pk=line.print_order.pk).first()
            if print_order.production_order:
                production_order_properties = print_order.production_order.properties
                sorter_dict.setdefault(print_order.production_order, {
                    "production_order": print_order.production_order,
                    "properties": print_order.production_order.properties,
                    "purchase_order": None,
                    "items": []
                })["items"].append(item)
            if print_order.purchase_order:
                purchase_order_properties = print_order.purchase_order.properties
                sorter_dict.setdefault(print_order.purchase_order, {
                    "purchase_order": print_order.purchase_order,
                    "properties": print_order.purchase_order.properties,
                    "production_order": None,
                    "items": []
                })["items"].append(item)

    my_result = [{
        "production_order": value["production_order"],
        "purchase_order": value["purchase_order"],
        "properties": value["properties"],
        "items": value["items"]
    } for key, value in sorter_dict.items()]

    results = {
        "criteria": criteria,
        "results": my_result
    }
    return results
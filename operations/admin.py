# -*- coding: utf-8 -*-

from __future__ import unicode_literals

from django.apps import apps
from django.contrib import admin

from .models import *

# auto-register all models

app = apps.get_app_config('operations')


class ContactAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'last_name', 'type_contact', 'type_identification', 'identification')


class StoreOrderLineInline(admin.TabularInline):
    fields = ('items', 'sku', 'amount', 'status')
    model = StoreOrderLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class StoreOrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'requester', 'status', 'expected_date', 'source', 'destination', 'created')
    raw_id_fields = ('source', 'destination', 'requester')
    inlines = [
        StoreOrderLineInline
    ]
    readonly_fields = ('expected_date', 'arrival_date')
    fieldsets = (
        ('Main Details', {
            'fields': (('expected_date', 'arrival_date',),
                       ('external_id', 'requester', 'status'),
                       ('source', 'destination', 'customer'))
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class StoreOrderLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'sku', 'amount', 'status', 'store_order', 'items')
    raw_id_fields = ('store_order', 'sku')


class StoreOrderReturnLineInLine(admin.TabularInline):
    fields = ('sku', 'amount', 'items', 'verified')
    model = StoreOrderReturnLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class StoreOrderReturnAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'store_order_id', 'status', 'created')
    inlines = [
        StoreOrderReturnLineInLine
    ]
    raw_id_fields = ('created_by', 'modified_by', 'store_order')


class StoreOrderReturnLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'sku', 'amount', 'status', 'store_order_return', 'items')
    raw_id_fields = ('store_order_return', 'sku')


class DistributionLineInline(admin.TabularInline):
    fields = ('sku', 'amount', 'status', 'assigned')
    model = DistributionLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class DistributionAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'status', 'store_order', 'finished', 'source', 'created')
    raw_id_fields = ('store_order', 'source', 'destination')
    inlines = [
        DistributionLineInline
    ]
    readonly_fields = ('finished_date', 'finished')
    fieldsets = (
        ('Main Details', {
            'fields': (('finished_date', 'finished',),
                       ('external_id', 'status', 'store_order'),
                       ('source', 'destination', 'verified_file', 'customer')
                       )
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class DistributionLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'distribution', 'sku', 'amount', 'assigned')
    raw_id_fields = ('distribution', 'sku')


class WorkOrderLineInLine(admin.TabularInline):
    fields = ('sku', 'amount', 'status', 'properties')
    model = WorkOrderLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class WorkOrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'status', 'finished', 'distribution', 'type', 'source', 'destination',
                    'created', 'item_type')
    raw_id_fields = ('distribution', 'source', 'destination')
    inlines = [
        WorkOrderLineInLine
    ]
    readonly_fields = ('finished_date', 'finished')
    fieldsets = (
        ('Main Details', {
            'fields': (('finished_date', 'finished'),
                       ('external_id', 'status', 'distribution'),
                       ('source', 'destination', 'type', 'customer'))
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class WorkOrderLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'work_order', 'sku', 'amount', 'properties')
    raw_id_fields = ('work_order', 'sku')


class PackingLineInLine(admin.TabularInline):
    fields = ('sku', 'amount', 'items', 'amount_verified', 'properties')
    model = PackingLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class PackingAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'prefix', 'type', 'status', 'created', 'tracking', 'customer', 'work_order')
    raw_id_fields = ('location',)
    inlines = [
        PackingLineInLine
    ]
    readonly_fields = ('prefix',)
    fieldsets = (
        ('Main Details', {
            'fields': (('prefix', 'type', 'property_types'),
                       ('external_id', 'status', 'items_file', 'summary_file', 'tracking', 'customer',
                        'work_order', 'location'))
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class PackingLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'packing', 'sku', 'amount', 'items', 'amount_verified')
    raw_id_fields = ('packing', 'sku')


class ProductionOrderLineInLine(admin.TabularInline):
    fields = ('sku', 'amount', 'status', 'items')
    model = ProductionOrderLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class ProductionOrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'status', 'prefix', 'entry_location', 'provider', 'created', 'item_type')
    raw_id_fields = ('entry_location', 'provider')
    inlines = [
        ProductionOrderLineInLine
    ]
    readonly_fields = ('prefix',)
    fieldsets = (
        ('Main Details', {
            'fields': (('prefix', 'entry_location', 'provider'),
                       ('external_id', 'status', 'customer'))
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class ProductionOrderLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'production_order', 'sku', 'amount', 'status', 'created')
    raw_id_fields = ('production_order', 'sku')


class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'status', 'prefix', 'entry_location', 'provider', 'created', 'customer',
                    'item_type')
    raw_id_fields = ('entry_location', 'provider')


class PurchaseOrderLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'purchase_order', 'sku', 'amount', 'status', 'items', 'created')


class PropertiesAdmin(admin.ModelAdmin):
    fields = ('name', 'display_name', 'process', 'params', 'model')
    list_display = ('id', 'name', 'display_name', 'process', 'params')


admin.site.register(Contact, ContactAdmin)
admin.site.register(Packing, PackingAdmin)
admin.site.register(PackingLine, PackingLineAdmin)
admin.site.register(StoreOrder, StoreOrderAdmin)
admin.site.register(StoreOrderLine, StoreOrderLineAdmin)
admin.site.register(Distribution, DistributionAdmin)
admin.site.register(DistributionLine, DistributionLineAdmin)
admin.site.register(WorkOrder, WorkOrderAdmin)
admin.site.register(WorkOrderLine, WorkOrderLineAdmin)
admin.site.register(ProductionOrder, ProductionOrderAdmin)
admin.site.register(ProductionOrderLine, ProductionOrderLineAdmin)
admin.site.register(PurchaseOrder, PurchaseOrderAdmin)
admin.site.register(PurchaseOrderLine, PurchaseOrderLineAdmin)
admin.site.register(StoreOrderReturn, StoreOrderReturnAdmin)
admin.site.register(StoreOrderReturnLine, StoreOrderReturnLineAdmin)
admin.site.register(Properties, PropertiesAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

#!/bin/bash

set -e  # detener script si hay error

PYTHON_VERSION="3.10.18"
ENV_NAME="venv"

echo "=== Instalando dependencias necesarias ==="
sudo apt update
sudo apt install -y \
  make build-essential libssl-dev zlib1g-dev \
  libbz2-dev libreadline-dev libsqlite3-dev wget curl llvm \
  libncurses5-dev libncursesw5-dev xz-utils tk-dev \
  libffi-dev liblzma-dev git

echo "=== Instalando pyenv ==="
if [ ! -d "$HOME/.pyenv" ]; then
  curl https://pyenv.run | bash
else
  echo "pyenv ya está instalado en $HOME/.pyenv"
fi

# Configuración en ~/.bashrc
if ! grep -q 'pyenv init' ~/.bashrc; then
  echo "=== Configurando pyenv en ~/.bashrc ==="
  cat << 'EOF' >> ~/.bashrc

# Configuración pyenv
export PATH="$HOME/.pyenv/bin:$PATH"
eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"
EOF
else
  echo "pyenv ya está configurado en ~/.bashrc"
fi

# Recargar bashrc para que pyenv funcione en este script
export PATH="$HOME/.pyenv/bin:$PATH"
eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"

echo "=== Instalando Python $PYTHON_VERSION con pyenv ==="
if ! pyenv versions --bare | grep -q "^$PYTHON_VERSION$"; then
  pyenv install $PYTHON_VERSION
else
  echo "Python $PYTHON_VERSION ya está instalado en pyenv"
fi

echo "=== Usando Python $PYTHON_VERSION ==="
pyenv local $PYTHON_VERSION

# 🔹 Pausa para confirmar
read -p "¿Quieres crear el entorno virtual local en ./$ENV_NAME con Python $PYTHON_VERSION? (s/n): " opcion
if [[ "$opcion" != "s" ]]; then
  echo "❌ Cancelado por el usuario."
  exit 0
fi

# Crear venv en la carpeta actual
echo "=== Creando entorno virtual en ./$ENV_NAME ==="
python -m venv $ENV_NAME

## Activar entorno
#echo "=== Activando entorno virtual ==="
#source $ENV_NAME/bin/activate

# Verificación
echo "=== Verificación ==="
python --version
which python

echo "✅ Entorno virtual creado en $(pwd)/$ENV_NAME"

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-05-22 15:03
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0031_auto_20191126_1229'),
        ('customers', '0007_auto_20190712_1606'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('config', '0005_customermetadata_default_template_label'),
    ]

    operations = [
        migrations.CreateModel(
            name='ModuleIntegrationConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('action', models.CharField(blank=True, choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return')], default='VERIFIED', max_length=20, null=True)),
                ('active', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('current_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
    ]

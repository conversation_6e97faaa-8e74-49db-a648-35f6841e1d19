# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from django.urls import path
from rest_framework.routers import DefaultRouter

from .api_views import *

router = DefaultRouter()
router.register(r'tracking-lines', TrackingLineViewSet, 'trancking-line')
router.register(r'trackings', TrackingViewSet, 'trancking')
router.register(r'shipping-orders', ShippingOrderViewSet, 'shipping-order')
router.register(r'shipping-order-lines', ShippingOrderLineViewSet, 'shipping-order-line')
router.register(r'shipping-returns', ShippingReturnViewSet, 'shipping-return')
router.register(r'shipping-return-lines', ShippingReturnLineViewSet, 'shipping-return-line')
router.register(r'print-order-labels', PrintOrderLabelViewSet, 'print-order-label')
router.register(r'print-template-labels', PrintTemplateLabelViewSet, 'print-template-label')

# router.register(r'label-packings', LABELPackingViewSet, 'label-packing')

urlpatterns = router.urls

urlpatterns += [
    path('tracking-files/', TrackingFileView.as_view(), name='tracking-files'),
    path('tracking-multiple/', TrackingFromShippingOrderView.as_view(), name='tracking-multiple')

]

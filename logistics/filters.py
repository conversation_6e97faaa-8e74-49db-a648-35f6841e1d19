# -*- coding: utf-8 -*-

from __future__ import unicode_literals

import django_filters
from common.filters import *
from logistics.models import Tracking, TrackingLine, PrintOrderLabel, ShippingOrder, ShippingOrderLine,\
    ShippingReturn, ShippingReturnLine


class TrackingFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Tracking
        fields = ("external_id", "order_number", "user_name", "verified", "status",
                  "customer_id", "current_location_id")


class TrackingLineFilter(BaseFilter, filters.FilterSet):
    tracking_external_id = django_filters.CharFilter(field_name="tracking__external_id")

    class Meta:
        model = TrackingLine
        fields = ("tracking_external_id", "sku_id", "verified", "status")


class PrintOrderLabelFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = PrintOrderLabel
        fields = ['external_id', 'status', 'template_label_id', 'location_id', 'printed_by_id', 'batch_number',
                  'batch_type']


class ShippingOrderFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = ShippingOrder
        fields = ['external_id', 'package_order_id', 'contact_id', 'customer_id']


class ShippingOrderLineFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = ShippingOrderLine
        fields = ['shipping_order_id', 'sku_id', 'sku__external_id']


class ShippingReturnFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = ShippingReturn
        fields = ['external_id', 'status', 'shipping_order_id', 'customer_id']


class ShippingReturnLineFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = ShippingReturnLine
        fields = ['shipping_return_id', 'sku_id', 'sku__external_id', 'verified']

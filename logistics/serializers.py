# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from .models import *
from common.serializers import BaseSerializer, UserNestedSerializer
from rest_framework import serializers
from customers.serializers import CustomerNestedSerializer
from customers.models import Customer
from integrations.serializers import *
from operations.serializers import *
from inventory.models import *


class ShippingOrderSerializer(BaseSerializer):
    package_order = PackageOrderNestedSerializer(read_only=True)
    package_order_id = serializers.IntegerField(write_only=True, required=False)
    contact_information = serializers.JSONField(required=False)
    contact = ContactNestedSerializer(read_only=True)
    contact_external_id = serializers.SlugRelatedField(queryset=Contact.objects.filter(type_contact='CLIENT'),
                                                       slug_field='external_id', required=False,
                                                       source='contact', write_only=True)
    contact_id = serializers.PrimaryKeyRelatedField(queryset=Contact.objects.all(), source='contact',
                                                    write_only=True, required=False)
    source = LocationNestedSerializer(read_only=True)
    source_external_id = serializers.SlugRelatedField(queryset=Location.objects.all(), slug_field='external_id',
                                                      source='source', write_only=True, required=False)
    source_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='source',
                                                   write_only=True, required=False)

    lines = serializers.ListField(child=SKUAmountSerializer(), write_only=True)
    total_amount = serializers.ReadOnlyField()
    seller_external_id = serializers.SlugRelatedField(queryset=Contact.objects.filter(type_contact__in=['SELLER', "PROVIDER"]),
                                                      slug_field='external_id',
                                                      source='seller', write_only=True, required=False)
    seller_id = serializers.PrimaryKeyRelatedField(queryset=Contact.objects.all(), source='seller',
                                                   write_only=True, required=False)
    seller = ContactNestedSerializer(read_only=True)
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(), source='customer',
                                                     write_only=True, required=False)
    customer = CustomerNestedSerializer(read_only=True)

    class Meta:
        model = ShippingOrder
        fields = '__all__'
    def validate(self, data):
        type_identification = "CC"
        if not data.get('contact') and not self.partial:
            if data.get("contact_information"):
                contact_info = data["contact_information"]
                type_identification = contact_info.get("type_identification")
                contact = Contact.objects.filter(external_id=contact_info["external_id"]).first()
                if not contact:
                    customer = UserConfig.objects.filter(user=self.context["request"].user).first().customer
                    contact = Contact.objects.create(
                        external_id=contact_info["external_id"],
                        name=contact_info["name"],
                        last_name=contact_info["last_name"],
                        email=contact_info["email"],
                        type_contact="CLIENT",
                        identification=contact_info["external_id"],
                        type_identification=type_identification,
                        customer=customer
                    )
                data["contact"] = contact
            else:
                raise serializers.ValidationError(
                    {"contact_id or contact_external_id": ["Some of these fields are required."]}
                )
        validated_data = validate_customer(user=self.context['request'].user, data=data)
        return validated_data

class ShippingOrderNestedSerializer(BaseSerializer):
    package_order = PackageOrderNestedSerializer(read_only=True)
    contact = ContactNestedSerializer(read_only=True)
    seller = ContactNestedSerializer(read_only=True)

    class Meta:
        model = ShippingOrder
        fields = ('id', 'external_id', 'package_order', 'shipping_address', 'costs', 'contact', 'seller')


class ShippingOrderLineSerializer(BaseSerializer):
    shipping_order = ShippingOrderNestedSerializer(read_only=True)
    shipping_order_id = serializers.IntegerField(write_only=True)
    sku = SKUNestedSerializer(read_only=True)
    sku_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = ShippingOrderLine
        fields = '__all__'


class ShippingReturnNestedSerializer(BaseSerializer):
    class Meta:
        model = ShippingReturn
        fields = ("id", "shipping_order_id", "customer_id", "entry_location_id", "external_id",
                  "costs", "status")


class ShippingReturnSerializer(BaseSerializer):
    shipping_order = ShippingOrderNestedSerializer(read_only=True)
    shipping_order_external_id = serializers.SlugRelatedField(
        queryset=ShippingOrder.objects.all(), slug_field='external_id',
        source='shipping_order', write_only=True, required=False)
    shipping_order_id = serializers.PrimaryKeyRelatedField(
        queryset=ShippingOrder.objects.all(), source='shipping_order',
        write_only=True, required=False)
    lines = serializers.ListField(child=SKUAmountSerializer(), write_only=True)
    total_amount = serializers.IntegerField(read_only=True)
    customer = CustomerNestedSerializer(read_only=True)

    class Meta:
        model = ShippingReturn
        fields = '__all__'

    def validate(self, data):
        validated_data = validate_customer(user=self.context['request'].user, data=data)
        return validated_data


class ShippingReturnLineSerializer(BaseSerializer):
    shipping_return = ShippingReturnNestedSerializer(read_only=True)
    shipping_return_id = serializers.PrimaryKeyRelatedField(queryset=ShippingReturn.objects.all(),
                                                            source='shipping_return', write_only=True)
    sku = SKUNestedSerializer(read_only=True)
    sku_id = serializers.PrimaryKeyRelatedField(queryset=SKU.objects.all(),
                                                source='sku', write_only=True)

    class Meta:
        model = ShippingReturnLine
        fields = '__all__'


class FillShippingReturnLineSerializer(EmptySerializer):
    shipping_return_id = serializers.PrimaryKeyRelatedField(queryset=ShippingReturn.objects.all(),
                                                            source='shipping_return', write_only=True)
    item_ids = serializers.ListField(child=serializers.PrimaryKeyRelatedField(queryset=Item.objects.all()))
    entry_location_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(),
                                                           source='entry_location', write_only=True)


class CancelShippingReturnProgressSerializer(EmptySerializer):
    shipping_return_id = serializers.PrimaryKeyRelatedField(queryset=ShippingReturn.objects.all(),
                                                            source='shipping_return', write_only=True)
    shipping_return_line_id = serializers.PrimaryKeyRelatedField(
        queryset=ShippingReturnLine.objects.all(), source='shipping_return_line', write_only=True, required=False)
    all_lines = serializers.BooleanField(default=False, write_only=True, required=False)

    def validate(self, data):
        if data.get('shipping_return_line') and data['shipping_return_line'].shipping_return != \
                data['shipping_return']:
            raise serializers.ValidationError(
                {"shipping_return_line_id": ["Line does not belong to shipping return."]})
        if not data.get('shipping_return_line') and not data.get('all_lines', False):
            raise serializers.ValidationError(
                {"all_lines": ["This fields is required in true if shipping_return_line_id is not passed."]})
        return data


class TrackingSerializer(BaseSerializer):
    lines = serializers.ListField(child=SKUAmountSerializer(), write_only=True)
    current_location = LocationNestedSerializer(read_only=True)
    current_location_id = serializers.PrimaryKeyRelatedField(
        queryset=Location.objects.all(), source="current_location", write_only=True, required=False)
    customer = CustomerNestedSerializer(read_only=True)
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(), source="customer",
                                                     write_only=True, required=False)
    shipping_order = ShippingOrderNestedSerializer(read_only=True)
    shipping_order_id = serializers.PrimaryKeyRelatedField(queryset=ShippingOrder.objects.all(),
                                                           source="shipping_order", write_only=True, required=False)

    total_expected_items = serializers.ReadOnlyField()

    class Meta:
        model = Tracking
        fields = '__all__'


class TrackingNestedSerializer(BaseSerializer):
    shipping_order = ShippingOrderNestedSerializer(read_only=True)

    class Meta:
        model = Tracking
        fields = ("id", "external_id", "order_number", "verified", "graphic_image", "shipping_order",
                  "total_expected_items")


class TrackingLineSerializer(BaseSerializer):
    sku_id = serializers.CharField(max_length=140, write_only=True)
    sku = SKUNestedSerializer(read_only=True)
    tracking_id = serializers.IntegerField(write_only=True)
    tracking = TrackingNestedSerializer(read_only=True)

    class Meta:
        model = TrackingLine
        fields = '__all__'


class TrackingVerifiedSerializer(EmptySerializer):
    item_ids = serializers.ListField(child=serializers.CharField(max_length=150), write_only=True)
    verified = serializers.BooleanField(write_only=True, required=False)


class TrackingFileSerializer(serializers.Serializer):
    tracking_file = serializers.FileField(write_only=True)
    customer = CustomerNestedSerializer(read_only=True)
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(),
                                                     write_only=True)


class TrackingFromShippingOrderSerializer(serializers.Serializer):
    shipping_order_ids = serializers.PrimaryKeyRelatedField(many=True, queryset=ShippingOrder.objects.all().
                                                            values_list('id', flat=True))
    ship_date = serializers.DateField(write_only=True)
    conveyor_id = serializers.IntegerField(write_only=True, required=False)
    tracking_type = serializers.CharField(max_length=40, required=True, write_only=True)
    location_id = serializers.IntegerField(write_only=True, required=True)


class TrackingPackingSerializer(serializers.Serializer):
    tracking_ids = serializers.ListField(child=serializers.IntegerField(), write_only=True)


class LabelReferenceNestedSerializer(BaseSerializer, serializers.ModelSerializer):
    class Meta:
        model = LabelReference
        fields = ('id', 'name', 'dimensions', 'photo_url', 'margins', 'parameters')


class PrintTemplateLabelSerializer(BaseSerializer, serializers.ModelSerializer):
    label_reference = LabelReferenceNestedSerializer(read_only=True)

    class Meta:
        model = PrintTemplateLabel
        fields = '__all__'


class LABELPrintOrderLineSerializer(BaseSerializer, serializers.ReadOnlyField):
    tracking = TrackingNestedSerializer(read_only=True)

    class Meta:
        model = LabelPrintOrderLine
        fields = '__all__'


class LabelSerializer(serializers.Serializer):
    tracking_external_id = serializers.CharField(write_only=True, max_length=50)

    def validate_tracking(self, value):
        """template
        Check that tracking exist.
        """
        if value is None:
            raise serializers.ValidationError("Tracking must not be empty")

        if Tracking.objects.filter(external_id=value).count() == 0:
            raise serializers.ValidationError("{} not found".format(value))
        return value


class PrintOrderLineLabelSerializer(serializers.Serializer):
    label = LabelSerializer(many=True, write_only=True)


class PrintOrderLabelSerializer(BaseSerializer, serializers.ModelSerializer):
    printed_by = UserNestedSerializer(read_only=True)

    template_label = PrintTemplateLabelSerializer(read_only=True)
    template_label_id = serializers.PrimaryKeyRelatedField(queryset=PrintTemplateLabel.objects.all(),
                                                           source='template_label', write_only=True, allow_null=True,
                                                           required=False)

    location = LocationNestedSerializer(read_only=True)
    location_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='location',
                                                     write_only=True)

    created_by = UserNestedSerializer(read_only=True)
    modified_by = UserNestedSerializer(read_only=True)

    lines = PrintOrderLineLabelSerializer(write_only=True)
    status = serializers.CharField(max_length=25, read_only=True)
    prefix = serializers.CharField(max_length=3, read_only=True)

    class Meta:
        model = PrintOrderLabel
        fields = '__all__'
        read_only_fields = ('batch_type', 'batch_number')


class PrintOrderLabelTotalSerializer(serializers.Serializer):
    total_label_used = serializers.IntegerField()


# LABEL Packing Serializer
class LABELPackingSerializer(serializers.Serializer):
    packing_ids = serializers.ListField(child=serializers.IntegerField(), write_only=True)
    location = serializers.IntegerField(write_only=True)

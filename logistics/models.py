# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import uuid

from common.models import BaseModel, User
from django.contrib.postgres.fields import <PERSON><PERSON>yField
from django.db.models import <PERSON><PERSON><PERSON>ield
from django.db import models
from django.db.models.aggregates import Sum
from inventory.models import SKU, Location
from rfid.exceptions import StatusUnavailableForPrint
from customers.models import Customer
from simple_history.models import HistoricalRecords
from integrations.models import PackageOrder
from operations.models import Contact
from integrations.models import IntegrationType, IntegrationParam


class ShippingOrder(BaseModel):
    SHIPPING_STATUS = (
        ("DISPATCHED", "Dispatched"),
        ("CLOSED", "Closed"),
        ("CREATED", "Created")
    )
    external_id = models.CharField(max_length=50, unique=True, null=True, blank=True)
    billing_address = JSONField(null=True, blank=True)
    shipping_address = JSONField(null=True, blank=True)
    costs = JSONField(null=True, blank=True, default={})
    status = models.CharField(max_length=20, choices=SHIPPING_STATUS, default="CREATED")
    package_order = models.ForeignKey(PackageOrder, null=True, blank=True, on_delete=models.SET_NULL)
    source = models.ForeignKey(Location, null=True, blank=True, on_delete=models.PROTECT)
    contact = models.ForeignKey(Contact, related_name='contact', null=True, blank=True, on_delete=models.PROTECT)
    seller = models.ForeignKey(Contact, related_name='seller', null=True, blank=True, on_delete=models.PROTECT)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.external_id, self.contact)

    @property
    def total_amount(self):
        sum_calculation = ShippingOrderLine.objects.filter(shipping_order=self).aggregate(total_amount=Sum('amount'))
        return sum_calculation.get('total_amount') or 0


class ShippingOrderLine(BaseModel):
    shipping_order = models.ForeignKey(ShippingOrder, related_name='lines', on_delete=models.PROTECT)
    sku = models.ForeignKey(SKU, null=True, blank=True, on_delete=models.PROTECT)
    amount = models.IntegerField(null=True, blank=True)
    costs = JSONField(null=True, blank=True, default=dict)
    location_param = models.ForeignKey(IntegrationParam, null=True, blank=True,
                                       limit_choices_to={"group__name": "WAREHOUSE"}, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.sku.id, self.amount)


class ShippingReturn(BaseModel):
    RETURN_STATUS = (
        ("CREATED", "Created"),
        ("COMPLETED", "Completed"),
        ("CANCELED", "Canceled")
    )
    external_id = models.CharField(max_length=50, unique=True, null=True, blank=True)
    costs = JSONField(null=True, blank=True, default=dict)
    status = models.CharField(max_length=20, choices=RETURN_STATUS, default="CREATED")
    shipping_order = models.ForeignKey(ShippingOrder, null=True, blank=True, on_delete=models.PROTECT)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    entry_location = models.ForeignKey(Location, null=True, blank=True, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.id, self.shipping_order_id)

    @property
    def total_amount(self):
        sum_calculation = ShippingReturnLine.objects.filter(shipping_return=self).aggregate(total_amount=Sum('amount'))
        return sum_calculation.get('total_amount') or 0


class ShippingReturnLine(BaseModel):
    shipping_return = models.ForeignKey(ShippingReturn, related_name='lines', on_delete=models.PROTECT)
    sku = models.ForeignKey(SKU, null=True, blank=True, on_delete=models.PROTECT)
    amount = models.IntegerField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=ShippingReturn.RETURN_STATUS, default="CREATED")
    costs = JSONField(null=True, blank=True, default=dict)
    items = JSONField(null=True, blank=True, default={"items": []})
    verified = models.BooleanField(default=False)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.sku.id, self.amount)

    def update_status(self):
        in_line_amount = len(self.items.get("items", []))
        expected_amount = self.amount
        if in_line_amount == expected_amount:
            self.status = "COMPLETED"
        elif in_line_amount > expected_amount:
            self.status = "OVERLOADED"
        else:
            self.status = "CREATED"

    def add_items(self, items=[], replace=False, commit=True):
        if replace:
            new_items = {"items": items}
            self.items = new_items
        else:
            new_item_list = list(set(self.items.get("items", []) + items))
            new_items = {"items": new_item_list}
            self.items = new_items
        self.update_status()
        if commit:
            self.save()


def get_upload_to_tracking(self, filename):
    """
    Calculates file path for audits
    """
    new_filename = "{}_{}".format(uuid.uuid4().hex, filename)
    return 'tracking-labels/{}-{}'.format(self.external_id, new_filename)


class Tracking(BaseModel):
    TRACKING_STATUS = (
        ("COMPLETED", "Completed"),
        ("INCOMPLETE", "Incomplete"),
        ("DRAFT", "Draft"),
        ("PROCESSING", "Processing"),
        ("INVALID", "Invalid"),
        ("UNASSIGNED", "Unassigned"),
        ("PRINTED", "Printed")
    )
    external_id = models.CharField(max_length=100, null=True, blank=True, unique=True)
    shipping_date = models.CharField(blank=True, max_length=30, null=True)
    order_number = models.CharField(max_length=150, null=True, blank=True)
    status = models.CharField(max_length=20, choices=TRACKING_STATUS, default="INCOMPLETE")
    user_name = models.CharField(max_length=100, null=True, blank=True)
    current_location = models.ForeignKey(Location, null=True, blank=True, on_delete=models.PROTECT)
    verified = models.BooleanField(blank=True, default=False)
    graphic_image = models.FileField(upload_to=get_upload_to_tracking, null=True, blank=True)
    properties = JSONField(null=True, blank=True, default=dict)  # {"Ship_From":"Beckett Simon","Order_Total":"0.00"}
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    shipping_order = models.ForeignKey(ShippingOrder, null=True, blank=True, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}'.format(self.external_id)

    @property
    def total_expected_items(self):
        sum_calculation = TrackingLine.objects.filter(tracking=self).aggregate(total_expected_items=Sum('amount'))
        return sum_calculation.get('total_expected_items') or 0


class TrackingLine(BaseModel):
    tracking = models.ForeignKey(Tracking, related_name='lines', on_delete=models.PROTECT)
    sku = models.ForeignKey(SKU, null=True, blank=True, on_delete=models.PROTECT)
    amount = models.IntegerField(null=True, blank=True)
    amount_verified = models.IntegerField(null=True, blank=True, default=0)
    items = JSONField(null=True, blank=True, default={"epcs": []})
    status = models.CharField(max_length=20, choices=Tracking.TRACKING_STATUS, default="INCOMPLETE")
    verified = models.BooleanField(default=False)
    packing = JSONField(null=True, blank=True, default=dict([("ids", [])]))  # {"ids":[2,3]}
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.id, self.sku_id, self.verified)


def get_upload_to(self, filename):
    """
    Calculates file path for audits
    """
    new_filename = "{}_{}".format(uuid.uuid4().hex, filename)
    return 'tracking-file/{}'.format(new_filename)


class TrackingFile(BaseModel):
    tracking_file = models.FileField(upload_to=get_upload_to, null=True, blank=True)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}'.format(self.id)


class LabelReference(BaseModel):
    name = models.CharField(max_length=140)
    dimensions = ArrayField(models.IntegerField(), size=2)
    parameters = JSONField(null=True, blank=True, default=dict)
    photo_url = models.URLField(null=True, blank=True)
    margins = JSONField(null=True, blank=True, default=dict)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}'.format(self.name)


class PrintTemplateLabel(BaseModel):
    name = models.CharField(max_length=140)
    description = models.TextField(null=True, blank=True)
    url_template = models.TextField()
    print_offset = ArrayField(models.IntegerField(), size=2, default=[0, 0])
    label_reference = models.ForeignKey(LabelReference, null=True, blank=True, on_delete=models.PROTECT)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}'.format(self.name)


class PrintOrderLabel(BaseModel):
    PRINT_STATUS = (
        ("ISSUED", "Issued"),
        ("IN_PROCESS", "In Process"),
        ("PRINTED", "Printed"),
        ("ABORTED", "Aborted")
    )

    BATCH_TYPE_LABEL = (
        ("PACKING", "Packing"),
        ("STORE_ORDER", "Store Order")
    )
    external_id = models.CharField(max_length=30, null=True, blank=True, unique=True)
    status = models.CharField(max_length=20, choices=PRINT_STATUS, default="ISSUED")
    prefix = models.CharField(max_length=3, default='POL')
    template_label = models.ForeignKey(PrintTemplateLabel, null=True, blank=True, on_delete=models.PROTECT)
    location = models.ForeignKey(Location, related_name='print_order_labels', on_delete=models.PROTECT)
    batch_number = models.CharField(max_length=100, null=True, blank=True)
    batch_type = models.CharField(max_length=20, choices=BATCH_TYPE_LABEL, default="PACKING")
    total_label_used = models.IntegerField(default=0, null=True, blank=True)
    printed_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.PROTECT)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.prefix, self.pk)

    def init_print(self, user):
        self.printed_by = user
        self.status = "IN_PROCESS"
        self.save()

    def finish_print(self, total_label_used):
        if self.status == "IN_PROCESS":
            self.status = "PRINTED"
            self.total_label_used = total_label_used
            self.save()
        else:
            raise StatusUnavailableForPrint

    def aborted_print(self):
        self.status = "ABORTED"
        self.save()


class LabelPrintOrderLine(BaseModel):
    print_order_label = models.ForeignKey(PrintOrderLabel, related_name='label_lines', on_delete=models.PROTECT)
    tracking = models.ForeignKey(Tracking, on_delete=models.PROTECT)

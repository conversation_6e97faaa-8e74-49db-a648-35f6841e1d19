# -*- coding: utf-8 -*-

from __future__ import unicode_literals

from inventory.models import Location
from operations.models import Packing

from ..models import LabelPrintOrderLine, Tracking, PrintOrderLabel


def transform_lines_tracking(lines):
    """
        creates production order lines from {sku,amount} array
        """
    trackings_list = []
    new_lines = []

    for line in lines:
        trackings_list.append(line['tracking_external_id'])

    trackings_ids = Tracking.objects.filter(external_id__in=trackings_list).values('id', 'external_id')
    for ids in trackings_ids:
        tracking_external_id = ids.get('external_id')
        tracking_id = ids.get('id')
        dict_lines = {}
        for line in lines:
            if tracking_external_id == line['tracking_external_id']:
                dict_lines['tracking_id'] = tracking_id
                new_lines.append(dict_lines)
                break

    return new_lines


def create_label_lines_from_array(order, lines):
    new_lines = []
    for line in lines:
        new_lines.append(
            LabelPrintOrderLine(
                print_order_label=order,
                tracking_id=line['tracking_id'],
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    LabelPrintOrderLine.objects.bulk_create(new_lines)
    return new_lines


def create_from_packing(packing, lines, location):
    new_order = PrintOrderLabel.objects.create(
        location=location,
        batch_number=packing.external_id,
        batch_type="PACKING",
        packing=packing,
        created_by=packing.created_by,
        modified_by=packing.modified_by
    )

    create_label_lines_from_array(packing=packing, lines=lines)
    return new_order


def create_print_label_from_packing(packings_ids, location_id):
    packings_objects = Packing.objects.filter(pk__in=packings_ids)
    location_object = Location.objects.get(pk=location_id)

    print_labels = []

    for packing in packings_objects:
        lines = packing.lines
        print_label = create_from_packing(packing, lines, location_object)
        print_labels.append(print_label)

    return packings_objects

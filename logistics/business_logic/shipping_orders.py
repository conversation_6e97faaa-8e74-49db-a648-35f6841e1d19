from ..models import *


def create_lines_from_array(order, lines, type_sku_in_lines="OBJECT"):
    """
        create shipping order lines from {sku,amount} array.
    """

    shipping_order_lines = []
    for line in lines:
        sku_id = None
        if type_sku_in_lines == "OBJECT":
            sku_id = line["sku"].id
        elif type_sku_in_lines == "ID":
            sku_id = line["sku_id"]
        shipping_order_lines.append(
            ShippingOrderLine(
                shipping_order_id=order.pk,
                sku_id=sku_id,
                amount=line['amount'],
                costs=line.get('costs', {}),
                created_by=order.created_by,
                modified_by=order.modified_by,
            )
        )
    ShippingOrderLine.objects.bulk_create(shipping_order_lines)

    return lines


def create_inf_to_trackings(order: ShippingOrder, lines):
    """
        create trackings from a shipping order.
    """
    tracking = Tracking(
            shipping_order_id=order.pk,
            external_id=order.external_id,
            customer_id=order.customer_id,
            created_by=order.created_by,
            modified_by=order.modified_by,
            current_location=order.source
    )
    tracking.save()

    def create_inf_to_tracking_line(type_sku_in_lines="OBJECT"):
        """
            From a shipping order the tracking lines are created from {sku,amount} array.
        """
        tracking_line = []
        for line in lines:
            sku_id = None
            if type_sku_in_lines == "OBJECT":
                sku_id = line["sku"].id
            elif type_sku_in_lines == "ID":
                sku_id = line["sku_id"]
            tracking_line.append(
                TrackingLine(
                    tracking_id=tracking.id,
                    sku_id=sku_id,
                    amount=line['amount'],
                    created_by=tracking.created_by,
                    modified_by=tracking.modified_by,
                )
            )
        TrackingLine.objects.bulk_create(tracking_line)
        return lines

    create_inf_to_tracking_line(type_sku_in_lines="OBJECT")

    return tracking


def create_shipping_order(external_id, created, updated, billing_address, shipping_address,
                          contact, customer, user, costs):
    """
       create shipping order object.
    """

    shipping_order_object = ShippingOrder.objects.create(
        external_id=external_id,
        created=created,
        modified=updated,
        billing_address=billing_address,
        shipping_address=shipping_address,
        costs=costs,
        contact=contact,
        customer=customer,
        created_by=user,
        modified_by=user
    )
    return shipping_order_object


def create_contact(data, integration_config):
    contact = Contact.objects.create(
        created_by=integration_config.integrator_user,
        modified_by=integration_config.integrator_user,
        external_id=data['id'],
        name=data['first_name'],
        last_name=data['last_name'],
        type_identification='CC',
        type_contact='CLIENT',
        email=data["email"],
        properties={"last_order_id": data['last_order_id'], "addresses": data['addresses']},
        customer_id=integration_config.customer.id
    )
    return contact


def save_shipping_order(shipping_order_id, package_order):
    shipping_order = ShippingOrder.objects.get(pk=shipping_order_id)
    shipping_order.package_order = package_order
    shipping_order.save()

    return shipping_order


def create_lines(order, lines):
    new_lines = []

    for line in lines:
        new_lines.append(
            TrackingLine(
                tracking=order,
                sku_id=line.get("sku_id"),
                amount=line.get("amount"),
                created_by=order.created_by,
                modified_by=order.modified_by,
            )
        )
    TrackingLine.objects.bulk_create(new_lines)
    return new_lines


def group_by_sku(lines):
    list_dict = []
    new_skus = []

    for line in lines:
        new_dict = {}
        sku = line['sku_id']
        amount = line['amount']
        if new_skus:
            if sku in new_skus:
                for dict in list_dict:
                    if dict['sku_id'] == sku:
                        dict['amount'] += amount
            else:
                new_dict['sku_id'] = sku
                new_dict['amount'] = amount
                list_dict.append(new_dict)
                new_skus.append(sku)
        else:
            new_dict['sku_id'] = sku
            new_dict['amount'] = amount
            list_dict.append(new_dict)
            new_skus.append(sku)
    return list_dict


def validate_orders(order_ids):
    orders = ShippingOrder.objects.filter(pk__in=order_ids, status='CREATED')
    if len(orders) == len(order_ids):
        return True
    else:
        return False


def create_tracking_from_shipping_orders(orders, user_config, location, ship_date, conveyor, type):
    shipping_orders = []
    tracking = Tracking.objects.create(
        location=location,
        customer=user_config.customer,
        created_by=user_config.user,
        modified_by=user_config.user,
        ship_date=ship_date,
        conveyor=conveyor,
        type=type
    )
    new_lines = []
    for order in orders:
        shipping_orders.append(order.pk)
        lines = order.lines.all()
        for line in lines:
            flipped = {}
            sku = line.sku.id
            amount = line.amount
            flipped['sku_id'] = sku
            flipped['amount'] = amount
            new_lines.append(flipped)

    lines = group_by_sku(new_lines)
    create_lines(tracking, lines)
    tracking.shipping_order.set(shipping_orders)
    tracking.external_id = tracking.id
    tracking.save()
    ShippingOrder.objects.filter(pk__in=shipping_orders).update(status='DISPATCHED')
    return tracking


def create_lines_with_costs(order, lines):
    """
        create shipping order lines from {sku_id,amount} array.
    """

    shipping_order_lines = []
    for line in lines:
        shipping_order_lines.append(
            ShippingOrderLine(
                shipping_order_id=order.pk,
                sku_id=line['sku_id'],
                amount=line['amount'],
                costs=line['costs'],
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    ShippingOrderLine.objects.bulk_create(shipping_order_lines)
    return lines


def create_return_lines_from_array(order, lines, type_sku_in_lines="OBJECT"):
    """
        create shipping return lines from {sku,amount} array.
    """
    shipping_return_lines = []
    for line in lines:
        sku_id = None
        if type_sku_in_lines == "OBJECT":
            sku_id = line["sku"].id
        elif type_sku_in_lines == "ID":
            sku_id = line["sku_id"]
        shipping_return_lines.append(
            ShippingReturnLine(
                shipping_return_id=order.pk,
                sku_id=sku_id,
                amount=line['amount'],
                costs=line.get('costs', {}),
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    ShippingReturnLine.objects.bulk_create(shipping_return_lines)
    return lines

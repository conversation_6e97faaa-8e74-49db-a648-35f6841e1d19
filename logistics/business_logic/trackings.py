# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from datetime import datetime
import itertools
from operator import itemgetter

from django.db.models.expressions import RawSQL

from common.models import User
from inventory.models import Item, Trace
from operations.business_logic import production_orders, packings
from operations.models import PackingLine
from logistics.models import TrackingLine, Tracking


def create_lines_tracking(user_id, tracking, lines=[]):
    user = User.objects.get(pk=user_id)

    tracking_lines = []
    for line in lines:
        tracking_lines.append(
            TrackingLine(
                tracking_id=tracking.pk,
                sku_id=line['sku_id'],
                amount=line['amount'],
                items={"epcs": []},
                created_by=user,
                modified_by=user
            )
        )
    TrackingLine.objects.bulk_create(tracking_lines)
    return lines


def validate_lines(items, reporter_id, tracking_id=None):
    reporter = User.objects.get(pk=reporter_id)
    items = [item.upper() for item in items]
    list_total_items = []
    traces = []
    tracking = Tracking.objects.get(external_id=tracking_id)

    tracking.lines.update(verified=False, items=RawSQL("items || jsonb_build_object('epcs', '[]'::json)", []),
                          modified_by=reporter, modified=datetime.now())

    lines_items = Item.objects.filter(pk__in=items, sku__customer=tracking.customer).exclude(state="INACTIVE")

    for item in lines_items:
        for line in tracking.lines.all():
            if item.sku == line.sku:
                epc = item.pk
                total_items_line = line.items["epcs"]
                list_total_items.append(epc)
                if len(total_items_line) < line.amount:
                    line.items["epcs"].append(epc)
                    line.save()
                if len(total_items_line) == line.amount:
                    line.verified = True
                    line.save()

    unverified_lines = tracking.lines.filter(verified=False).count()

    tracking.verified = False
    if not unverified_lines:
        tracking.verified = True
        for item in list_total_items:
            in_trace = Trace(
                item_id=item,
                location=tracking.current_location,
                reporter=reporter,
                reporter_source="HANDHELD",
                action="VERIFIED",
                created_by=reporter,
                modified_by=reporter,
                customer=tracking.customer,
            )
            traces.append(in_trace)
        Trace.objects.bulk_create(traces)
        Item.objects.filter(pk__in=list_total_items).exclude(state="INACTIVE").update(
            state="DISPATCH", modified_by_id=reporter, modified=datetime.now())

    tracking.save()
    return lines_items


def file_object_tracking(tracking, location, user):
    tracking_objects = []
    tracking.tracking_file.seek(0)
    customer = tracking.customer
    content = tracking.tracking_file.read()
    content.decode("utf-8", "ignore")
    body_new = content.decode('utf-8').replace('\n', ';')
    line_body = body_new.split(';')
    line_list = [line_body[i:i + 7] for i in range(0, len(line_body), 7)]
    line_list.pop()

    for line in line_list:
        json_line_tracking = {}
        lines_tracking = []
        ship_date = line[0].replace("/", "-")
        user_name = line[1]
        order_number = line[2]
        type_tracking = line[3]
        tracking_number = line[4]
        skus = line[5].split(' ')
        quantity = int(line[6])

        for sku_id in skus:
            json_line_tracking = {"sku_id": sku_id, "amount": 1}
            lines_tracking.append(json_line_tracking)

        groups = itertools.groupby(sorted(lines_tracking, key=itemgetter('sku_id')), key=itemgetter('sku_id'))
        result = [{'sku_id': v, 'amount': sum(dicc['amount'] for dicc in diccs)} for v, diccs in groups]
        lines = production_orders.transform_lines_external_to_id(result)

        tracking = Tracking.objects.create(
            order_number=order_number,
            external_id=tracking_number,
            total_expected_items=quantity,
            location=location,
            user_name=user_name,
            ship_date=ship_date,
            status="INCOMPLETE",
            created_by=user,
            modified_by=user,
            type=type_tracking,
            customer=customer
        )
        tracking.save()

        for line in lines:
            lines_create = TrackingLine(
                tracking=tracking,
                sku_id=line['sku_id'],
                amount=line['amount'],
                status="INCOMPLETE",
                created_by=user,
                modified_by=user,
                items={'epcs': []}
            )
            lines_create.save()

        tracking_objects.append(tracking)
    return tracking_objects


def process_associated_tracking(tracking):
    tracking_lines = TrackingLine.objects.filter(tracking_id=tracking, verified=False, status="INCOMPLETE")
    packing_lines = PackingLine.objects.filter(status="DISPATCHED")

    def exact_amount_packing(line_packing, line_tracking):
        line_packing.status = 'VERIFIED'
        line_packing.amount_verified += amount_available
        line_tracking.amount_verified += amount_available
        line_tracking.status = 'COMPLETED'
        if packing_external not in line_tracking.packing["ids"]:
            line_tracking.packing["ids"].append(packing_external)
        if tracking.external_id not in line_packing.packing.tracking["tracking"]:
            line_packing.packing.tracking["tracking"].append(tracking.external_id)
        if packing_external not in tracking.packings["ids"]:
            tracking.packings["ids"].append(packing_external)
            packings.refresh_packing_progress(line_packing.packing)

    def ample_amount_packing(line_packing, line_tracking):
        line_tracking.status = 'COMPLETED'
        line_packing.amount_verified += amount_track
        line_tracking.amount_verified += amount_track
        if packing_external not in line_tracking.packing["ids"]:
            line_tracking.packing["ids"].append(packing_external)
        if tracking.external_id not in line_packing.packing.tracking["tracking"]:
            line_packing.packing.tracking["tracking"].append(tracking.external_id)
        if packing_external not in tracking.packings["ids"]:
            tracking.packings["ids"].append(packing_external)
            packings.refresh_packing_progress(line_packing.packing)

    def lack_amount_packing(line_packing, line_tracking):
        line_tracking.status = 'INCOMPLETE'
        line_packing.status = 'VERIFIED'
        line_packing.amount_verified += amount_available
        line_tracking.amount_verified += amount_available
        if packing_external not in line_tracking.packing["ids"]:
            line_tracking.packing["ids"].append(packing_external)
        if tracking.external_id not in line_packing.packing.tracking["tracking"]:
            line_packing.packing.tracking["tracking"].append(tracking.external_id)
        if packing_external not in tracking.packings["ids"]:
            tracking.packings["ids"].append(packing_external)
            packings.refresh_packing_progress(line_packing.packing)

    for line_tracking in tracking_lines:
        sku_track = line_tracking.sku
        for line_packing in packing_lines:
            if line_tracking.status == 'INCOMPLETE':
                if sku_track == line_packing.sku:
                    amount_available = line_packing.amount - line_packing.amount_verified
                    amount_track = line_tracking.amount - line_tracking.amount_verified
                    packing_external = line_packing.packing.external_id
                    if amount_track == amount_available:
                        exact_amount_packing(line_packing, line_tracking)
                    elif amount_track < amount_available:
                        ample_amount_packing(line_packing, line_tracking)
                    elif amount_track > amount_available:
                        lack_amount_packing(line_packing, line_tracking)
                line_packing.save()
                line_tracking.save()
                tracking.save()


def refresh_tracking_progress(tracking):
    total_completed_lines = 0
    for line in tracking.lines.all():
        if line.status == 'COMPLETED':
            total_completed_lines += 1

    if total_completed_lines == len(tracking.lines.all()):
        tracking.status = 'COMPLETED'
    else:
        tracking.status = 'INCOMPLETE'

    tracking.save()


def refresh_tracking_validated(tracking):
    total_completed_lines = 0

    for line in tracking.lines.all():
        if line.verified:
            total_completed_lines += 1

    if total_completed_lines == len(tracking.lines.all()):
        tracking.verified = True
    else:
        tracking.verified = False

    tracking.save()


def refresh_advance_validated(tracking):
    items_total_tracking = []
    for line in tracking.lines.all():
        items = line.items.get("epcs", [])
        items_total_tracking.extend(items)

        line.verified = False
        line.items["epcs"] = []
        line.save()

    Item.objects.filter(pk__in=items_total_tracking).exclude(state='INACTIVE').update(state='PRESENT')

# -*- coding: utf-8 -*-

from __future__ import unicode_literals

from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework import viewsets, status, mixins
from rest_framework.decorators import action
from rest_framework.generics import CreateAPIView
from rest_framework.permissions import *
from rest_framework.response import Response

from core_config.utils import get_user_config
import logistics.tasks
from main.conf import base as common_config
import integrations.tasks as integration_tasks
import integrations.utils as integration_utils
from logistics import tasks as logistics_tasks
from logistics.business_logic import print_order_labels, shipping_orders, shipping_returns, trackings
from .filters import *
from .serializers import *


class TrackingViewSet(viewsets.ModelViewSet):
    """
    retrieve:
    Return the given tracking.

    list:
    Return a list of all the existing tracking.

    create:
    Create a new tracking instance.

    update:
    Update an existing tracking instance.

    partial_update:
    Update partial an existing tracking instance.

    delete:
    Delete  an existing tracking instance.
    """
    __basic_fields = ('external_id', 'status', 'verified', 'properties')
    queryset = Tracking.objects.all()
    serializer_class = TrackingSerializer
    filter_class = TrackingFilter
    search_fields = __basic_fields
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines')
        user_id = request.user.pk
        with transaction.atomic():
            tracking = serializer.save()
            if tracking.customer:
                tracking.save()
            else:
                user_config = utils.get_user_config(request.user.pk)
                tracking.customer = user_config.customer
                tracking.save()
            trackings.create_lines_tracking(user_id, tracking, lines)

        headers = self.get_success_headers(serializer.data)
        serializer = TrackingSerializer(tracking)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists Tracking History
        """
        tracking_object = self.get_object()
        trackings = Tracking.objects.get(pk=tracking_object.pk)
        query = trackings.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = TrackingSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = TrackingSerializer(query, many=True)
        return Response(serializer.data)

    @action(methods=['get'], detail=True)
    def lines(self, request, pk=None):
        """
        Lists lines production order
        """
        tracking_object = self.get_object()
        query = TrackingLine.objects.filter(tracking=tracking_object)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = TrackingLineSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = TrackingLineSerializer(query, many=True)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=TrackingVerifiedSerializer,
            url_path='actions/verified-items')
    def validation_items(self, request, pk=None):
        """
        Validation items of tracking lines.
        """

        tracking_object = self.get_object()
        serializer = TrackingVerifiedSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        items = serializer.validated_data['item_ids']
        tracking_id = tracking_object.external_id

        module_config = integration_utils.verified_integration_module(
            customer_id=tracking_object.customer_id, kong_module__name='SHIPPING_ORDER_DISPATCH',
            active_integration=True)
        if module_config:
            request_body = {
                "detail-type": "SHIPPING_ORDER_DISPATCH",
                "tracking_id": tracking_object.pk
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_config.id, body=request_body)

            # Run extra actions after the tracking is validated
            module_config = integration_utils.verified_integration_module(
                permission_name='INTEGRATIONS_VALIDATED_TRACKING', active_integration=True,
                customer_id=tracking_object.customer_id)

        if module_config:
            request_body = {
                "detail-type": "INTEGRATIONS_VALIDATED_TRACKING",
                "tracking_id": tracking_object.id,
                "verified": tracking_object.verified
            }
            integration_tasks.integration_module_request.delay(
                integration_module_id=module_config.id, body=request_body)

        logistics.tasks.validate_items.delay(list_items=items, tracking_id=tracking_id, reporter_id=request.user.pk)
        tracking_object.refresh_from_db()

        return Response(status=status.HTTP_202_ACCEPTED)


class TrackingLineViewSet(viewsets.ModelViewSet):
    """
    retrieve:
    Return the given tracking line.

    list:
    Return a list of all the existing tracking line.

    create:
    Create a new tracking line instance.

    update:
    Update an existing tracking line instance.

    partial_update:
    Update partial an existing tracking line instance.

    delete:
    Delete  an existing tracking line instance.
    """

    queryset = TrackingLine.objects.all()
    serializer_class = TrackingLineSerializer
    filter_class = TrackingLineFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'
    search_fields = ('sku__id', 'sku__external_id', 'sku__properties', 'sku__display_name', 'sku__name')

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists Tracking Lines History
        """
        tracking_line_object = self.get_object()
        tracking_lines = TrackingLine.objects.get(pk=tracking_line_object.pk)
        query = tracking_lines.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = TrackingLineSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = TrackingLineSerializer(query, many=True)
        return Response(serializer.data)


class TrackingFileView(CreateAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = TrackingFileSerializer

    def post(self, request, *args, **kwargs):
        serializer = TrackingFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        file_tracking = serializer.validated_data['tracking_file']
        customer = serializer.validated_data['customer_id']

        user = request.user.pk

        with transaction.atomic():
            tracking = TrackingFile.objects.create(
                tracking_file=file_tracking,
                customer=customer
            )
            tracking.save()
            trackings = logistics_tasks.apply_tracking(tracking, user)
        response_serializer = TrackingSerializer(trackings, many=True)
        return Response(response_serializer.data, status=status.HTTP_202_ACCEPTED)


class TrackingFromShippingOrderView(CreateAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = TrackingFromShippingOrderSerializer

    def post(self, request, *args, **kwargs):
        """

            Create Tracking by Shipping orders grouped.

        """
        serializer = TrackingFromShippingOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        orders_ids = serializer.validated_data['shipping_order_ids']
        location_id = serializer.validated_data['location_id']
        ship_date = serializer.validated_data['ship_date']
        conveyor_id = serializer.validated_data.pop('conveyor_id', None)
        tracking_type = serializer.validated_data['tracking_type']
        result = shipping_orders.validate_orders(orders_ids)
        if result:
            logistics_tasks.tracking_object_from_shipping_orders.delay(shipping_order_ids=orders_ids,
                                                                       location_id=location_id,
                                                                       conveyor_id=conveyor_id,
                                                                       ship_date=ship_date,
                                                                       user_id=request.user.pk,
                                                                       type=tracking_type)
            return Response(status=status.HTTP_201_CREATED)
        else:
            body = {'message': 'Shipping orders cant be grouped'}
            return Response(data=body, status=status.HTTP_406_NOT_ACCEPTABLE)


class ShippingOrderViewSet(viewsets.ModelViewSet):
    """
        retrieve:
        Return the given Shipping Order.

        list:
        Return a list of all Shipping Orders.

        create:
        Create a new Shipping Order instance.

        update:
        Update an existing Shipping Order instance.

        partial_update:
        Update partial an Shipping Order instance.

        delete:
        Delete an existing Shipping Order instance.
    """
    queryset = ShippingOrder.objects.all()
    serializer_class = ShippingOrderSerializer
    filter_class = ShippingOrderFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines')
        contact_information = serializer.validated_data.pop('contact_information', None)
        user_id = request.user.pk
        with transaction.atomic():
            shipping = serializer.save()
            user_config = get_user_config(user_id)
            shipping.customer = user_config.customer
            shipping.save()
            if not shipping.source:
                shipping.source = Location.objects.filter(name="CEDI").first()
                shipping.save()
            shipping_orders.create_lines_from_array(shipping, lines)
            auto_tracking = common_config.get_config('AUTO_TRACKING', default=False)
            if auto_tracking == True:
                shipping_orders.create_inf_to_trackings(shipping, lines)
        headers = self.get_success_headers(serializer.data)
        serializer = ShippingOrderSerializer(shipping)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(methods=['get'], detail=True)
    def lines(self, request, pk=None):
        """
        Lists lines production order
        """
        shipping_order_object = self.get_object()
        query = ShippingOrderLine.objects.filter(shipping_order=shipping_order_object)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = ShippingOrderLineSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ShippingOrderLineSerializer(query, many=True)
        return Response(serializer.data)


class ShippingOrderLineViewSet(viewsets.ModelViewSet):
    """
        retrieve:
        Return the given Shipping Order Line.

        list:
        Return a list of all Shipping Order Lines.

        create:
        Create a new Shipping Order Line instance.

        update:
        Update an existing Shipping Order Line instance.

        partial_update:
        Update partial an Shipping Order Line instance.

        delete:
        Delete an existing Shipping Order Line instance.
    """
    queryset = ShippingOrderLine.objects.all()
    serializer_class = ShippingOrderLineSerializer
    filter_class = ShippingOrderLineFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class ShippingReturnViewSet(viewsets.ModelViewSet):
    """
        retrieve:
        Return the given Shipping Return instance.

        list:
        Return a list of all Shipping Return instances.

        create:
        Create a new Shipping Return instance.

        update:
        Update an existing Shipping Return instance.

        partial_update:
        Update partial an Shipping Return instance.

        delete:
        Delete an existing Shipping Return instance.
    """
    queryset = ShippingReturn.objects.all()
    serializer_class = ShippingReturnSerializer
    filter_class = ShippingReturnFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines')
        with transaction.atomic():
            shipping_return = serializer.save()
            shipping_orders.create_return_lines_from_array(shipping_return, lines)

        module_config = integration_utils.verified_integration_module(
            customer_id=shipping_return.customer_id, kong_module__name='SHIPPING_RETURN_CREATE',
            active_integration=True)
        if module_config:
            request_body = {
                "detail-type": "SHIPPING_RETURN_CREATE",
                "shipping_return_id": shipping_return.id
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_config.id, body=request_body)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(methods=['get'], detail=True, filter_class=ShippingReturnLineFilter, queryset=ShippingReturnLine.objects.all())
    def lines(self, request, pk=None):
        """
        Lists lines production order
        """
        query = ShippingReturnLine.objects.filter(shipping_return_id=pk)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = ShippingReturnLineSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ShippingReturnLineSerializer(query, many=True)
        return Response(serializer.data)

    @action(methods=['post'], detail=False, serializer_class=CancelShippingReturnProgressSerializer)
    def cancel_progress(self, request):
        """
        Cancel progress and update status of items to DISPATCH on selected shipping return Line or on all
        shipping return
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        shipping_return_object = serializer.validated_data['shipping_return']
        return_line = serializer.validated_data.get('shipping_return_line')
        return_line_id = return_line.id if return_line else None
        all_lines = serializer.validated_data.get('all_lines', False)
        shipping_returns.cancel_progress(shipping_return=shipping_return_object, reporter=request.user,
                                         return_line_id=return_line_id, all_lines=all_lines)
        order_lines = ShippingReturnLine.objects.filter(shipping_return=shipping_return_object)
        page = self.paginate_queryset(order_lines)
        if page is not None:
            serializer = ShippingReturnLineSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ShippingReturnLineSerializer(order_lines, many=True)
        return Response(serializer.data)


class ShippingReturnLineViewSet(viewsets.ModelViewSet):
    """
        retrieve:
        Return the given Shipping Return Line instance.

        list:
        Return a list of all Shipping Return Line instances.

        create:
        Create a new Shipping Return Line instance.

        update:
        Update an existing Shipping Return Line instance.

        partial_update:
        Update partial an Shipping Return Line instance.

        delete:
        Delete an existing Shipping Return Line instance.
    """
    queryset = ShippingReturnLine.objects.all()
    serializer_class = ShippingReturnLineSerializer
    filter_class = ShippingReturnLineFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    @action(methods=['post'], detail=False, serializer_class=FillShippingReturnLineSerializer)
    def fill(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        shipping_return = serializer.validated_data['shipping_return']
        item_ids = serializer.validated_data['item_ids']
        entry_location = serializer.validated_data['entry_location']
        shipping_return.entry_location = entry_location
        shipping_return.save()
        shipping_returns.fill_order(shipping_return=shipping_return, item_ids=item_ids, reporter=request.user)
        queryset = ShippingReturnLine.objects.filter(shipping_return=shipping_return)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ShippingReturnLineSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ShippingReturnLineSerializer(queryset, many=True)
        return Response(serializer.data)


class PrintOrderLabelViewSet(viewsets.ModelViewSet):
    """
        retrieve:
        Return the given print order label.
        list:
        Return a list of all the existing print orders labels.
        create:
        Create a new print order label instance.

        update:
        Update an existing print order label instance.

        partial_update:
        Update partial an existing print order label instance.

        delete:
        Delete  an existing print order label instance.
    """
    queryset = PrintOrderLabel.objects.all()
    serializer_class = PrintOrderLabelSerializer
    filter_class = PrintOrderLabelFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    def get_serializer_context(self):
        return {'request': self.request}

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        print_order_lines = serializer.validated_data.pop('lines')

        with transaction.atomic():
            # TODO: refactor this lines in a common  method
            print_order_label = serializer.save()
            label_lines = print_order_lines.get('label')
            lines = print_order_labels.transform_lines_tracking(lines=label_lines)
            print_order_labels.create_label_lines_from_array(order=print_order_label, lines=lines)
        headers = self.get_success_headers(serializer.data)
        serializer = PrintOrderLabelSerializer(print_order_label)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(methods=['get'], detail=True)
    def label_lines(self, request, pk=None):
        """
            Lists print order LABEL Lines
        """
        print_order_label = self.get_object()
        query = LabelPrintOrderLine.objects.filter(
            print_order_label=print_order_label)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = LABELPrintOrderLineSerializer(
                page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = LABELPrintOrderLineSerializer(
            query, many=True)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=EmptySerializer,
            url_path='actions/init_print', url_name='init-print')
    def init_print(self, request, pk=None):
        print_order_label = self.get_object()
        serializer = EmptySerializer()
        print_order_label.init_print(request.user)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=PrintOrderLabelTotalSerializer,
            url_path='actions/finish_print', url_name='finish-print')
    def finish_print(self, request, pk=None):
        print_order_label = self.get_object()
        serializer = PrintOrderLabelTotalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        total_label_used = serializer.validated_data['total_label_used']
        print_order_label.finish_print(total_label_used)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=EmptySerializer,
            url_path='actions/aborted_print', url_name='aborted-print')
    def finish_aborted(self, request, pk=None):
        print_order_label = self.get_object()
        print_order_label.aborted_print()
        return Response(status=status.HTTP_202_ACCEPTED)


class PrintTemplateLabelViewSet(viewsets.ReadOnlyModelViewSet):
    """
    retrieve:-
    Return the given item group.
    list:
    Return a list of all the existing items.
    """
    queryset = PrintTemplateLabel.objects.all()
    serializer_class = PrintTemplateLabelSerializer
    filter_fields = ('label_reference',)
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class LABELPackingViewSet(mixins.CreateModelMixin, mixins.DestroyModelMixin, viewsets.GenericViewSet):
    """
    create:
    Create a new packing instance, (also generates Print Order).
    delete:
    Delete  an existing packing instance.

    """
    queryset = Packing.objects.all()
    serializer_class = LABELPackingSerializer
    permission_classes = (IsAuthenticated,)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        packing_ids = serializer.validated_data('packing_ids')
        location_id = serializer.validated_data('location_id')

        with transaction.atomic():
            print_order_labels.create_print_label_from_packing(packings_ids=packing_ids,
                                                               location_id=location_id)

        headers = self.get_success_headers(serializer.data)
        serializer = LABELPackingSerializer(Packing)
        final_data = serializer.data  # I don't really like this solution  to  add print_order_id
        final_data['print_order_id'] = print_order.pk  # to the response, find a better way to do so.

        return Response(final_data, status=status.HTTP_201_CREATED, headers=headers)

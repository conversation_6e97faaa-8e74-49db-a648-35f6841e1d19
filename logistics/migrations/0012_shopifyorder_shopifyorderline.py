# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-07-10 22:07
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0009_auto_20190710_1707'),
        ('inventory', '0014_auto_20190614_1534'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('operations', '0009_auto_20190527_1542'),
        ('logistics', '0011_auto_20190524_1612'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShopifyOrder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('billing_address', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('shipping_address', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('contact', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='operations.Contact')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('package', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='integrations.PackageOrder')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='ShopifyOrderLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('shopify_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='logistics.ShopifyOrder')),
                ('sku', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
    ]

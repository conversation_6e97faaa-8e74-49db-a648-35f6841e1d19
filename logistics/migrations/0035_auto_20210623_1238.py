# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2021-06-23 17:38
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0037_auto_20210204_1317'),
        ('logistics', '0034_auto_20210610_1435'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalshippingreturn',
            name='entry_location',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location'),
        ),
        migrations.AddField(
            model_name='shippingreturn',
            name='entry_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='inventory.Location'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalshippingreturnline',
            name='items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True),
        ),
        migrations.AlterField(
            model_name='shippingreturnline',
            name='items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True),
        ),
    ]

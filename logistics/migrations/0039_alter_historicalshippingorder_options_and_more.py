# Generated by Django 4.2.11 on 2025-04-20 01:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("inventory", "0102_delete_inventorysalesreport_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("operations", "0059_alter_historicalcontact_options_and_more"),
        ("customers", "0012_alter_historicalcustomer_options_and_more"),
        ("integrations", "0023_alter_historicalintegrationgroup_options_and_more"),
        ("logistics", "0038_auto_20240408_1159"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicalshippingorder",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical shipping order",
                "verbose_name_plural": "historical shipping orders",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalshippingorderline",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical shipping order line",
                "verbose_name_plural": "historical shipping order lines",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalshippingreturn",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical shipping return",
                "verbose_name_plural": "historical shipping returns",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalshippingreturnline",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical shipping return line",
                "verbose_name_plural": "historical shipping return lines",
            },
        ),
        migrations.AlterModelOptions(
            name="historicaltracking",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical tracking",
                "verbose_name_plural": "historical trackings",
            },
        ),
        migrations.AlterModelOptions(
            name="historicaltrackingfile",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical tracking file",
                "verbose_name_plural": "historical tracking files",
            },
        ),
        migrations.AlterModelOptions(
            name="historicaltrackingline",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical tracking line",
                "verbose_name_plural": "historical tracking lines",
            },
        ),
        migrations.AlterField(
            model_name="historicalshippingorder",
            name="billing_address",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingorder",
            name="costs",
            field=models.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingorder",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingorder",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalshippingorder",
            name="shipping_address",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingorderline",
            name="costs",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingorderline",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingorderline",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalshippingorderline",
            name="location_param",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                limit_choices_to={"group__name": "WAREHOUSE"},
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="integrations.integrationparam",
            ),
        ),
        migrations.AlterField(
            model_name="historicalshippingreturn",
            name="costs",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingreturn",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingreturn",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalshippingreturnline",
            name="costs",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingreturnline",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicalshippingreturnline",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicalshippingreturnline",
            name="items",
            field=models.JSONField(blank=True, default={"items": []}, null=True),
        ),
        migrations.AlterField(
            model_name="historicaltracking",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicaltracking",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicaltracking",
            name="properties",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="historicaltracking",
            name="verified",
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name="historicaltrackingfile",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicaltrackingfile",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicaltrackingline",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name="historicaltrackingline",
            name="id",
            field=models.BigIntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicaltrackingline",
            name="items",
            field=models.JSONField(blank=True, default={"epcs": []}, null=True),
        ),
        migrations.AlterField(
            model_name="historicaltrackingline",
            name="packing",
            field=models.JSONField(blank=True, default={"ids": []}, null=True),
        ),
        migrations.AlterField(
            model_name="labelprintorderline",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="labelprintorderline",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="labelprintorderline",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="labelprintorderline",
            name="print_order_label",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="label_lines",
                to="logistics.printorderlabel",
            ),
        ),
        migrations.AlterField(
            model_name="labelprintorderline",
            name="tracking",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to="logistics.tracking"
            ),
        ),
        migrations.AlterField(
            model_name="labelreference",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="labelreference",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="labelreference",
            name="margins",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="labelreference",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="labelreference",
            name="parameters",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="printorderlabel",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printorderlabel",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="printorderlabel",
            name="location",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="print_order_labels",
                to="inventory.location",
            ),
        ),
        migrations.AlterField(
            model_name="printorderlabel",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printorderlabel",
            name="printed_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printorderlabel",
            name="template_label",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="logistics.printtemplatelabel",
            ),
        ),
        migrations.AlterField(
            model_name="printtemplatelabel",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="printtemplatelabel",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="printtemplatelabel",
            name="label_reference",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="logistics.labelreference",
            ),
        ),
        migrations.AlterField(
            model_name="printtemplatelabel",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="billing_address",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="contact",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="contact",
                to="operations.contact",
            ),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="costs",
            field=models.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="customers.customer",
            ),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="seller",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="seller",
                to="operations.contact",
            ),
        ),
        migrations.AlterField(
            model_name="shippingorder",
            name="shipping_address",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="shippingorderline",
            name="costs",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="shippingorderline",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingorderline",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="shippingorderline",
            name="location_param",
            field=models.ForeignKey(
                blank=True,
                limit_choices_to={"group__name": "WAREHOUSE"},
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="integrations.integrationparam",
            ),
        ),
        migrations.AlterField(
            model_name="shippingorderline",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingorderline",
            name="shipping_order",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="lines",
                to="logistics.shippingorder",
            ),
        ),
        migrations.AlterField(
            model_name="shippingorderline",
            name="sku",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="inventory.sku",
            ),
        ),
        migrations.AlterField(
            model_name="shippingreturn",
            name="costs",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="shippingreturn",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingreturn",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="shippingreturn",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingreturnline",
            name="costs",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="shippingreturnline",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingreturnline",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="shippingreturnline",
            name="items",
            field=models.JSONField(blank=True, default={"items": []}, null=True),
        ),
        migrations.AlterField(
            model_name="shippingreturnline",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="shippingreturnline",
            name="shipping_return",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="lines",
                to="logistics.shippingreturn",
            ),
        ),
        migrations.AlterField(
            model_name="shippingreturnline",
            name="sku",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="inventory.sku",
            ),
        ),
        migrations.AlterField(
            model_name="tracking",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="tracking",
            name="current_location",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="inventory.location",
            ),
        ),
        migrations.AlterField(
            model_name="tracking",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="customers.customer",
            ),
        ),
        migrations.AlterField(
            model_name="tracking",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="tracking",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="tracking",
            name="properties",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="tracking",
            name="shipping_order",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="logistics.shippingorder",
            ),
        ),
        migrations.AlterField(
            model_name="tracking",
            name="verified",
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.AlterField(
            model_name="trackingfile",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="trackingfile",
            name="customer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="customers.customer",
            ),
        ),
        migrations.AlterField(
            model_name="trackingfile",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="trackingfile",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="trackingline",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="trackingline",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="trackingline",
            name="items",
            field=models.JSONField(blank=True, default={"epcs": []}, null=True),
        ),
        migrations.AlterField(
            model_name="trackingline",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="trackingline",
            name="packing",
            field=models.JSONField(blank=True, default={"ids": []}, null=True),
        ),
        migrations.AlterField(
            model_name="trackingline",
            name="sku",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="inventory.sku",
            ),
        ),
        migrations.AlterField(
            model_name="trackingline",
            name="tracking",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="lines",
                to="logistics.tracking",
            ),
        ),
    ]

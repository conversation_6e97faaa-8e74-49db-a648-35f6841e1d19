# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-04-02 15:35
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('logistics', '0031_auto_20200327_1015'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalshippingorder',
            name='costs',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='historicalshippingorderline',
            name='costs',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='shippingorder',
            name='costs',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='shippingorderline',
            name='costs',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
    ]

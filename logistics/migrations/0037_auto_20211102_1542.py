# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2021-11-02 20:42
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import logistics.models
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0039_auto_20211019_1233'),
        ('customers', '0007_auto_20190712_1606'),
        ('logistics', '0036_auto_20210719_1614'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalTracking',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=100, null=True)),
                ('shipping_date', models.CharField(blank=True, max_length=30, null=True)),
                ('order_number', models.CharField(blank=True, max_length=150, null=True)),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned'), ('PRINTED', 'Printed')], default='INCOMPLETE', max_length=20)),
                ('user_name', models.CharField(blank=True, max_length=100, null=True)),
                ('verified', models.BooleanField(default=False)),
                ('graphic_image', models.TextField(blank=True, max_length=100, null=True)),
                ('properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('current_location', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
                ('customer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('shipping_order', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='logistics.ShippingOrder')),
            ],
            options={
                'verbose_name': 'historical tracking',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.RenameField(
            model_name='tracking',
            old_name='location',
            new_name='current_location',
        ),
        migrations.RenameField(
            model_name='tracking',
            old_name='ship_date',
            new_name='shipping_date',
        ),
        migrations.RemoveField(
            model_name='tracking',
            name='conveyor',
        ),
        migrations.RemoveField(
            model_name='tracking',
            name='type',
        ),
        migrations.AlterField(
            model_name='historicaltrackingline',
            name='items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'epcs': []}, null=True),
        ),
        migrations.AlterField(
            model_name='historicaltrackingline',
            name='status',
            field=models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned'), ('PRINTED', 'Printed')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='tracking',
            name='graphic_image',
            field=models.FileField(blank=True, null=True, upload_to=logistics.models.get_upload_to_tracking),
        ),
        migrations.AlterField(
            model_name='tracking',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.RemoveField(
            model_name='tracking',
            name='shipping_order',
        ),
        migrations.AddField(
            model_name='tracking',
            name='shipping_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='logistics.ShippingOrder'),
        ),
        migrations.AlterField(
            model_name='tracking',
            name='status',
            field=models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned'), ('PRINTED', 'Printed')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='trackingline',
            name='items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'epcs': []}, null=True),
        ),
        migrations.AlterField(
            model_name='trackingline',
            name='status',
            field=models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned'), ('PRINTED', 'Printed')], default='INCOMPLETE', max_length=20),
        ),
    ]

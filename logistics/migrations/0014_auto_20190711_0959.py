# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-07-11 14:59
from __future__ import unicode_literals

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('logistics', '0013_auto_20190710_1708'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='HistoricalShopifyOrder',
            new_name='HistoricalShippingOrder',
        ),
        migrations.RenameModel(
            old_name='HistoricalShopifyOrderLine',
            new_name='HistoricalShippingOrderLine',
        ),
        migrations.RenameModel(
            old_name='ShopifyOrder',
            new_name='ShippingOrder',
        ),
        migrations.RenameModel(
            old_name='ShopifyOrderLine',
            new_name='ShippingOrderLine',
        ),
        migrations.AlterModelOptions(
            name='historicalshippingorder',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical shipping order'},
        ),
        migrations.AlterModelOptions(
            name='historicalshippingorderline',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical shipping order line'},
        ),
        migrations.RenameField(
            model_name='historicalshippingorderline',
            old_name='shopify_order',
            new_name='shipping_order',
        ),
        migrations.RenameField(
            model_name='shippingorderline',
            old_name='shopify_order',
            new_name='shipping_order',
        ),
    ]

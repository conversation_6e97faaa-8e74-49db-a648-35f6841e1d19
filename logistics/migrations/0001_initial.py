# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 17:05
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import logistics.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0002_auto_20190415_1203'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LabelPrintOrderLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='LabelReference',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=140)),
                ('dimensions', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), size=2)),
                ('parameters', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('photo_url', models.URLField(blank=True, null=True)),
                ('margins', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='PrintOrderLabel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=30, null=True, unique=True)),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('IN_PROCESS', 'In Process'), ('PRINTED', 'Printed'), ('ABORTED', 'Aborted')], default='ISSUED', max_length=20)),
                ('prefix', models.CharField(default='POL', max_length=3)),
                ('batch_number', models.CharField(blank=True, max_length=100, null=True)),
                ('batch_type', models.CharField(choices=[('PACKING', 'Packing'), ('STORE_ORDER', 'Store Order')], default='PACKING', max_length=20)),
                ('total_label_used', models.IntegerField(blank=True, default=0, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='print_order_labels', to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('printed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='PrintTemplateLabel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=140)),
                ('description', models.TextField(blank=True, null=True)),
                ('url_template', models.TextField()),
                ('print_offset', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), default=[0, 0], size=2)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('label_reference', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='logistics.LabelReference')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Shipping',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=30, null=True, unique=True)),
                ('name', models.CharField(blank=True, max_length=50, null=True)),
                ('email', models.CharField(blank=True, max_length=80, null=True)),
                ('fulfillment_status', models.CharField(blank=True, max_length=50, null=True)),
                ('fulfilled_at', models.CharField(blank=True, max_length=50, null=True)),
                ('currency', models.CharField(blank=True, max_length=5, null=True)),
                ('subtotal', models.IntegerField(blank=True, null=True)),
                ('total', models.IntegerField(blank=True, null=True)),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned')], default='INCOMPLETE', max_length=20)),
                ('shipping_method', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.CharField(blank=True, max_length=50, null=True)),
                ('properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='ShippingFile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('shipping_file', models.FileField(blank=True, null=True, upload_to=logistics.models.get_upload_to)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='ShippingLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned')], default='INCOMPLETE', max_length=20)),
                ('amount', models.IntegerField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('shipping', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='logistics.Shipping')),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Tracking',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=30, null=True, unique=True)),
                ('ship_date', models.CharField(blank=True, max_length=30, null=True)),
                ('order_number', models.CharField(blank=True, max_length=150, null=True)),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned')], default='INCOMPLETE', max_length=20)),
                ('type', models.CharField(choices=[('DAILY', 'Daily'), ('BULK', 'Bulk')], default='DAILY', max_length=20)),
                ('user_name', models.CharField(blank=True, max_length=100, null=True)),
                ('verified', models.BooleanField(default=False)),
                ('graphic_image', models.TextField(blank=True, null=True)),
                ('properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('packings', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'ids': []}, null=True)),
                ('total_expected_items', models.IntegerField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='TrackingFile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('tracking_file', models.FileField(blank=True, null=True, upload_to=logistics.models.get_upload_to)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='TrackingLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.IntegerField(blank=True, null=True)),
                ('amount_verified', models.IntegerField(blank=True, default=0, null=True)),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned')], default='INCOMPLETE', max_length=20)),
                ('verified', models.BooleanField(default=False)),
                ('packing', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'ids': []}, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('sku', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
                ('tracking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='logistics.Tracking')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.AddField(
            model_name='printorderlabel',
            name='template_label',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='logistics.PrintTemplateLabel'),
        ),
        migrations.AddField(
            model_name='labelprintorderline',
            name='print_order_label',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='label_lines', to='logistics.PrintOrderLabel'),
        ),
        migrations.AddField(
            model_name='labelprintorderline',
            name='tracking',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='logistics.Tracking'),
        ),
    ]

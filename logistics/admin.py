# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.apps import apps
from django.contrib import admin

from .models import *

# Register your models here.
app = apps.get_app_config('logistics')


class ShippingOrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'status')
    raw_id_fields = ('package_order', 'contact', 'seller')


class ShippingOrderLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'shipping_order_id', 'sku', 'amount')
    raw_id_fields = ('shipping_order', 'sku')


class ShippingReturnLineInLine(admin.TabularInline):
    fields = ('sku', 'amount', 'items', 'verified')
    model = ShippingReturnLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class ShippingReturnAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'shipping_order_id', 'status', 'created')
    inlines = [
        ShippingReturnLineInLine
    ]
    raw_id_fields = ('created_by', 'modified_by', 'shipping_order')


class ShippingReturnLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'shipping_return_id', 'sku', 'amount')
    raw_id_fields = ('created_by', 'modified_by', 'shipping_return', 'sku')


class TrackingAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'order_number', 'user_name', 'graphic_image', 'status')
    search_fields = ('id', 'external_id')
    raw_id_fields = ('current_location', 'shipping_order')


class TrackingLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'tracking_id', 'sku', 'amount', 'items', 'verified')
    raw_id_fields = ('tracking', 'sku')


class LabelReferenceAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'dimensions', 'margins')


class PrintTemplateLabelAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description', 'url_template', 'print_offset', 'label_reference')


class PrintOrderLineInLine(admin.TabularInline):
    fields = ('tracking',)
    model = LabelPrintOrderLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('tracking',)


class PrintOrderLabelAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'status', 'location', 'template_label', 'total_label_used',
                    'printed_by', 'batch_type', 'batch_number')
    raw_id_fields = ('location',)
    inlines = [
        PrintOrderLineInLine
    ]
    readonly_fields = ('created', 'modified', 'prefix')
    fieldsets = (
        ('Main Details', {
            'fields': (('created', 'modified'),
                       ('prefix', 'external_id', 'location', 'template_label'),
                       ('batch_number', 'batch_type'),
                       ('printed_by',),
                       ('total_label_used',)
                       )
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class GTINPrintOrderLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'print_order_label', 'tracking', 'created', 'modified')
    raw_id_fields = ('print_order_label', 'tracking')


admin.site.register(ShippingOrder, ShippingOrderAdmin)
admin.site.register(ShippingOrderLine, ShippingOrderLineAdmin)
admin.site.register(ShippingReturn, ShippingReturnAdmin)
admin.site.register(ShippingReturnLine, ShippingReturnLineAdmin)
admin.site.register(Tracking, TrackingAdmin)
admin.site.register(TrackingLine, TrackingLineAdmin)
admin.site.register(PrintOrderLabel, PrintOrderLabelAdmin)
admin.site.register(LabelPrintOrderLine, GTINPrintOrderLineAdmin)
admin.site.register(PrintTemplateLabel, PrintTemplateLabelAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

# -*- coding: utf-8 -*-

from __future__ import unicode_literals

import logging

from celery import shared_task
from common.models import User
from core_config.models import UserConfig
from inventory.models import Location
from logistics.business_logic import trackings, shipping_orders
from logistics.models import TrackingFile, Tracking, ShippingOrder
from integrations.models import IntegrationType


@shared_task(bind=True)
def apply_tracking(self, tracking, user_id):
    """
    Executes tracking business logic
    """
    location = Location.objects.get(pk=2)
    user = User.objects.get(pk=user_id)
    tracking_object = TrackingFile.objects.get(pk=tracking.pk)
    tracking_objects = trackings.file_object_tracking(tracking_object, location, user)
    return tracking_objects


@shared_task(bind=True)
def process_association(self, tracking_id):
    """
    Executes process of association tracking with packings.
    """
    tracking_object = Tracking.objects.get(pk=tracking_id)
    if tracking_object.status == 'INCOMPLETE' and tracking_object.verified == False:
        logging.warning('EXECUTE TRACKING {}'.format(tracking_object.external_id))
        trackings.process_associated_tracking(tracking=tracking_object)
        trackings.refresh_tracking_progress(tracking_object)
        logging.warning('FINISHED TRACKING {}'.format(tracking_object.external_id))


@shared_task(bind=True)
def tracking_object_from_shipping_orders(self, shipping_order_ids, user_id, location_id, ship_date, conveyor_id, type):
    location = Location.objects.get(pk=location_id)
    user_config = UserConfig.objects.get(user_id=user_id)
    orders = ShippingOrder.objects.filter(pk__in=shipping_order_ids)
    if conveyor_id:
        conveyor = IntegrationType.objects.get(pk=conveyor_id)
    else:
        conveyor = conveyor_id
    tracking = shipping_orders.create_tracking_from_shipping_orders(orders, user_config, location, ship_date, conveyor, type)
    return tracking


@shared_task(bind=True)
def validate_items(self, tracking_id, reporter_id, list_items=[]):
    trackings.validate_lines(items=list_items, tracking_id=tracking_id, reporter_id=reporter_id)
    return tracking_id


@shared_task(bind=True)
def tracking_object_from_shipping_orders(self, shipping_order_ids, user_id, location_id, ship_date, conveyor_id, type):
    location = Location.objects.get(pk=location_id)
    user_config = UserConfig.objects.get(user_id=user_id)
    orders = ShippingOrder.objects.filter(pk__in=shipping_order_ids)
    if conveyor_id:
        conveyor = IntegrationType.objects.get(pk=conveyor_id)
    else:
        conveyor = conveyor_id
    tracking = shipping_orders.create_tracking_from_shipping_orders(orders, user_config, location, ship_date, conveyor, type)
    return tracking
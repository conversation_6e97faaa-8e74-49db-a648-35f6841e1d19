# 🚀 Configuración de la base de datos PostgreSQL en Docker (Desarrollo local)

Este documento describe los pasos necesarios para configurar y conectar PostgreSQL en un entorno local usando Docker, junto con la integración en un proyecto **Django**.

---

## 📂 1. Crear archivo `.env`

Dentro de la carpeta `docker_postgres_local`, crea un archivo llamado **`.env`** con las siguientes variables de entorno:

```bash
# Variables de entorno para PostgreSQL
POSTGRES_USER=usuario
POSTGRES_PASSWORD=password
POSTGRES_DB=db_name
POSTGRES_PORT=5432
POSTGRES_VERSION=13.18
```

---

## 🐳 2. Levantar contenedor de PostgreSQL

En la terminal (Linux), muévete a la carpeta `docker_postgres_local` y ejecuta:

```bash
# Levantar el contenedor de PostgreSQL
sudo docker compose up -d

# Verificar que el contenedor esté corriendo
sudo docker ps
```

---

## ⚙️ 3. Configurar archivo `config.json`

Configura el archivo **`config.json`** con los mismos datos del archivo `.env`.  
⚠️ **Importante:** asegúrate de **no subir este archivo a Git**.

---

## 🌍 4. Variables de entorno para Django

Desde la carpeta raíz del proyecto (`kong-rfid-wms-layer-services`), crea las variables de entorno necesarias:

```bash
# Variables de entorno para Django
export DJANGO_SETTINGS_MODULE=main.conf.local
# Esta ruta varía según la ubicación de tu proyecto y archivo config.json
export KONG_SETTINGS_MODULE=/home/<USER>/repo/kong-rfid-wms-layer-services/config.json
export PYTHONUNBUFFERED=1
```

Verifica que se crearon correctamente:

```bash
echo $DJANGO_SETTINGS_MODULE
echo $KONG_SETTINGS_MODULE
echo $PYTHONUNBUFFERED
```

---

## 🗄️ 5. Crear extensión `hstore`

Conéctate al contenedor y crea la extensión `hstore`:

```bash
# Conexión al contenedor (ajusta los valores según tu .env)
sudo docker exec -it kong_postgres psql -U kong_user -d kong_rfid_db
```

En la consola `psql`, ejecuta:

```sql
CREATE EXTENSION IF NOT EXISTS hstore;
```

Salir de `psql`:

```
\q
```

---

## 📑 6. Migraciones de Django

- Asegúrate de tener el **entorno virtual** activado.  
- Ejecuta desde la carpeta raíz del proyecto:

```bash
python manage.py migrate
```

---

## 👤 7. Crear usuario administrador
Realiza el siguiente paso para crear un superusuario en Django o ejecuta el archivo:
```bash
#Ejecuta el script de configuración(incluído elsuperusuario)
bash setup_project.sh 
```
Ejecuta el script para crear un **superusuario**:

```bash
# Crear superusuario en Django
python create_superuser.py
```

🔑 Luego revisa las credenciales por defecto generadas en el script o guardalas si usaste el setup_project.sh

---

✅ ¡Listo! Tu proyecto **Django** ya está conectado a la base de datos PostgreSQL local con Docker.

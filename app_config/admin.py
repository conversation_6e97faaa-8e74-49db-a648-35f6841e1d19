# -*- coding: utf-8 -*-
from django.apps import apps
from django.contrib import admin

from .models import *

# auto-register all models
app = apps.get_app_config('app_config')


class AppNodeAdmin(admin.ModelAdmin):
    list_display = ('external_id', 'mac', 'name', 'profile', 'description',
                    'properties', 'recovery_action')


admin.site.register(AppNode, AppNodeAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from common.models import BaseModel
from core_config.models import KongModule
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>


# Create your models here.
class AppNode(BaseModel):
    APP_PROFILE = (
        ('RFID', 'Rfid'),
        ('LABEL', 'Label'),
        ('SHIPMENT', 'Shipment'),
        ('AUTOMATIC', 'Automatic')
    )
    APP_SOURCE = (
        ("SYSTEM", "System Source"),
        ("FIXED_PORTAL", "Fixed Portal"),
        ("HANDHELD", "Handheld Device"),
        ("WEB_APP", "Web Application"),
        ("PRINTER", "Printer Device"),
        ("DESKTOP_APP", "Desktop Application"),
        ("POS_DEVICE", "Point of Sale Device")
    )
    APP_MODE = (
        ("MULTI_CLIENT", "Multi Client"),
        ("MONO_CLIENT", "Mono Client")
    )
    external_id = models.CharField(max_length=17, null=True, blank=True)
    mac = models.CharField(max_length=17, null=True, blank=True)
    name = models.CharField(max_length=140)
    description = models.CharField(max_length=140, null=True, blank=True)
    action = models.ForeignKey(KongModule, null=True, blank=True, on_delete=models.PROTECT)
    profile = models.CharField(max_length=10, choices=APP_PROFILE, default='RFID')
    app_source = models.CharField(max_length=20, choices=APP_SOURCE)
    app_mode = models.CharField(max_length=20, choices=APP_MODE, default='MULTI_CLIENT')
    properties = JSONField(null=True, blank=True, default=dict)
    recovery_action = models.BooleanField(default=False)

    class Meta:
        ordering = ['mac']

    def __str__(self):
        return '{}-{}'.format(self.pk, self.action)

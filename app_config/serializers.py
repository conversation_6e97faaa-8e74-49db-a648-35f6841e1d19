# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from common.serializers import *
from rest_framework import serializers
from core_config.serializers import KongModuleSerializer
from .models import *


class AppNodeSerializer(BaseSerializer, serializers.ModelSerializer):
    action = KongModuleSerializer(read_only=True)
    action_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = AppNode
        fields = '__all__'


class AppNodeNestedSerializer(BaseSerializer):
    action = KongModuleSerializer(read_only=True)

    class Meta:
        model = AppNode
        fields = ('id', 'external_id', 'mac', 'action', 'profile', 'app_source', 'app_mode')

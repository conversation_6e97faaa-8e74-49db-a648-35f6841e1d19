# Generated by Django 4.2.11 on 2025-04-20 01:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("core_config", "0017_alter_historicaljsonmapconfig_options_and_more"),
        ("app_config", "0002_appnode_app_mode"),
    ]

    operations = [
        migrations.AlterField(
            model_name="appnode",
            name="action",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="core_config.kongmodule",
            ),
        ),
        migrations.AlterField(
            model_name="appnode",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="appnode",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="appnode",
            name="modified_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="appnode",
            name="properties",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
    ]

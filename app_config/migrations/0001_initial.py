# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-10-15 01:26
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core_config', '0007_auto_20200416_1604'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppNode',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=17, null=True)),
                ('mac', models.Char<PERSON>ield(blank=True, max_length=17, null=True)),
                ('name', models.CharField(max_length=140)),
                ('description', models.CharField(blank=True, max_length=140, null=True)),
                ('profile', models.CharField(choices=[('RFID', 'Rfid'), ('LABEL', 'Label'), ('SHIPMENT', 'Shipment'), ('AUTOMATIC', 'Automatic')], default='RFID', max_length=10)),
                ('app_source', models.CharField(choices=[('SYSTEM', 'System Source'), ('FIXED_PORTAL', 'Fixed Portal'), ('HANDHELD', 'Handheld Device'), ('WEB_APP', 'Web Application'), ('PRINTER', 'Printer Device'), ('DESKTOP_APP', 'Desktop Application'), ('POS_DEVICE', 'Point of Sale Device')], max_length=20)),
                ('properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('recovery_action', models.BooleanField(default=False)),
                ('action', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core_config.KongModule')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['mac'],
            },
        ),
    ]

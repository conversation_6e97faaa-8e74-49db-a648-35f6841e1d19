# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.urls import path
from rest_framework.routers import DefaultRouter

from .api_views import *

router = DefaultRouter()
router.register(r'countries', CountryViewSet, 'country')
router.register(r'state-countries', StateCountryViewSet, 'state-country')
router.register(r'cities', CityViewSet, 'city')
router.register(r'location-types', LocationTypeViewSet, 'location-type')
router.register(r'locations', LocationViewSet, 'location')
router.register(r'load-locations-file', MasterLOCATIONFileViewSet,  'load-location-file')
router.register(r'load-skus-file', MasterSKUFileViewSet,  'load-sku-file')
router.register(r'skus', SKUViewSet, 'sku')
router.register(r'sku-types', SKUTypeViewSet, 'sku-type')
router.register(r'sku-groups', SKUGroupViewSet, 'sku-group')
router.register(r'items', ItemViewSet, 'item')
router.register(r'item-types', ItemTypeViewSet, 'item-types')
router.register(r'traces', TraceViewSet, 'trace')
router.register(r'moves', MoveViewSet, 'move')
router.register(r'move-lines', MoveLineViewSet, 'move-line')
router.register(r'reserves', ReserveViewSet, 'reserve')
router.register(r'reserve-lines', ReserveLineViewSet, 'reserve-line')
router.register(r'return-types', ReturnTypeViewSet, 'return-type')
router.register(r'audits', AuditViewSet, 'audit')
router.register(r'audit-lines', AuditLineViewSet, 'audit-line')
router.register(r'audit-traces', AuditTraceViewSet, 'audit-trace')
router.register(r'total-location', LocationTotalInventoryViewSet, 'total-location')
router.register(r'summary-location', InventorySummaryToLocationViewSet, 'summary-location')
router.register(r'summary-sub-locations', SubLocationInventorySummaryViewSet, 'summary-sub-location')
router.register(r'summary-key-properties', SkuKeysViewSet, 'summary-key-property')
router.register(r'summary-key-value-properties', SkuPropertiesViewSet, 'summary-key-value-property')
router.register(r'properties', PropertiesViewSet, 'properties')
router.register(r'summary-unique-key-value-properties', UniqueSkuPropertiesViewSet, 'summary-unique-key-value-property')

urlpatterns = router.urls

urlpatterns += [
    path('items/utils/detail-sorter/', ItemDetailSorterView.as_view(), name='items-detail'),
    path('items/utils/detail/', ItemDetailView.as_view(), name='items-detail'),
    path('items/utils/sorter/', ItemSorterView.as_view(), name='items-sorter'),
    path('position-items/', PositionItemView.as_view(), name='position-items'),
    path('return-items/', ReturnItemView.as_view(), name='return-items'),
    path('unavailable-items/', ItemUnavailableView.as_view(), name='unavailable-items'),
    path('inactive-items/', InactiveInventoryView.as_view(), name='inactive-items'),
    path('type-position-items/', TypePositionItemView.as_view(), name='type-position-items'),
    path('execute-action-items/', ItemExecuteActionView.as_view(), name='execute-action-items'),
    path('sorter-items/', ItemResultSorterView.as_view(), name='sorter-post-items'),
    path('type-total-location/', TotalGroupLocationView.as_view(), name='type-total-location'),
    path('type-summary-location/', SummaryGroupLocationView.as_view(), name='type-summary-location')
]

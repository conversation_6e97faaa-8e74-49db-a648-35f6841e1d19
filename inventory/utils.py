# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from collections import defaultdict
from datetime import datetime
from itertools import chain
from django.db import connection
from rest_framework.generics import get_object_or_404
from rest_framework.utils import json

from . import business_logic
from .business_logic.positions import type_position_by_items
from .models import Item, Location
from customers.models import Customer

SKU_CRITERIA = "sku"
STATE_CRITERIA = "state"


def sort_items_by_sku_MHZ(items_ids):
    item_objects = Item.objects.select_related("sku").filter(pk__in=items_ids).exclude(state='INACTIVE')
    skus_dict = defaultdict(list)
    for item in item_objects:
        skus_dict[item.sku].append(item.pk)

    result = [{'sku': sku, 'items': items} for sku, items in skus_dict.items()]
    return result


def sort_items_by_sku_MHZ(items_ids):
    item_objects = Item.objects.select_related("sku").filter(pk__in=items_ids).exclude(state='INACTIVE')
    skus_dict = defaultdict(dict)
    for item in item_objects:
        skus_dict[item.sku].setdefault(item.type.name if item.type else "INVENTORY", []).append(item.pk)
    result = [{'sku': sku, 'items': items, 'type': type_name} for sku, item_type_dict in skus_dict.items()
                                                                    for type_name, items in item_type_dict.items()]
    return result


def sort_items_by_sku(items_ids, customer_id, filter_states: list = None, output_format="LIST"):
    item_objects = Item.objects.filter(pk__in=items_ids, sku__customer_id=customer_id) \
        .select_related("sku").exclude(state="INACTIVE")
    if filter_states:
        item_objects = item_objects.filter(state__in=filter_states)
    skus_dict = defaultdict(list)
    if output_format == "LIST":
        for item in item_objects:
            skus_dict[item.sku].append(item.pk)
        result = [{'sku': sku, 'items': items} for sku, items in skus_dict.items()]
        return result
    elif output_format == "DICT":
        for item in item_objects:
            skus_dict[item.sku_id].append(item.pk)
        return skus_dict


def sort_items_by_sku_empty(items_ids, customer_id):
    item_objects = Item.objects.filter(pk__in=items_ids, sku__customer_id=customer_id).exclude(state='INACTIVE')
    skus_dict = defaultdict(list)
    for item in item_objects:
        skus_dict[item.sku].append(item.pk)

    result = [{'sku': sku, 'items': []} for sku, items in skus_dict.items()]
    return result


def sort_items(criteria, items_ids, customer_id, location_types: list = None, position_type=None):
    type_position_by_items = None
    if location_types is None:
        location_types = []
    item_objects = Item.objects.select_related("current_location") \
        .filter(pk__in=items_ids, sku__customer_id=customer_id)
    sorter_dict = {}  # {"-- sku --": {"-- state --": {"-- location --": [-- items --]}}}
    sorted_items = []
    result_location_types = []
    for location_type in location_types:
        result_location_types.append(location_type)
        preview_location_type = location_type
        while preview_location_type.parent:
            result_location_types.append(preview_location_type.parent)
            preview_location_type = preview_location_type.parent
    for item in item_objects:
        properties = None
        item_type = None
        sku = item.sku if "sku" in criteria else None
        state = item.state if "state" in criteria else None
        location = item.current_location if "location" in criteria else None
        item_type = item.type if "type" in criteria else None
        if position_type:
            location = Location.objects.get(pk=position_type["entry_location_id"])
            type_position_by_items = business_logic.positions.type_position_by_items(items=[item], type_position=position_type["option"], reporter=None,
                                   entry_location=location)
        if item.properties:
            properties = item.properties["model"] if "properties" in criteria else None
        else:
            properties = item.properties if "properties" in criteria else None
        while location and result_location_types and location.type not in result_location_types:
            location = location.parent
        sorter_dict.setdefault(sku, {}).setdefault(state, {}).setdefault(properties, {}).setdefault(item_type, {})\
            .setdefault(location, []).append(item.pk)
    for sku, states_dict in sorter_dict.items():
        for state, item_type_dict in states_dict.items():
            for properties, properties_dict in item_type_dict.items():
                for item_type, location_dict in properties_dict.items():
                    for location, items in location_dict.items():
                        sorter_item = {
                            "sku": sku,
                            "state": state,
                            "location": location,
                            "items": items,
                            "properties": properties,
                            "type": item_type,
                            "suggested_positions": type_position_by_items
                        }
                        sorted_items.append(sorter_item)

    results = {
        "criteria": criteria,
        "results": sorted_items,
        "suggested_positions": type_position_by_items
    }
    return results


def sort_items_by_current_location(items_ids, customer_id):
    item_objects = Item.objects.filter(pk__in=items_ids, sku__customer_id=customer_id).exclude(state='INACTIVE')
    locations_dict = defaultdict(list)
    for item in item_objects:
        locations_dict[item.current_location].append(item.pk)
    result = [{'current_location': location, 'items': items} for location, items in locations_dict.items()]
    return result


def sort_items_objects(items_ids, customer):
    results = {}
    item_objects = Item.objects.filter(pk__in=items_ids, sku__customer=customer)
    item_dict = defaultdict(list)
    for item in item_objects:
        item_dict[item.pk, item.state, item.current_location, item.sku].append(item.pk)
    sorted_items = [
        {'id': property[0], 'state': property[1], 'location': property[2],
         'sku': property[3], 'item': items}
        for property, items in item_dict.items()]

    results['results'] = sorted_items
    return results

def sort_type_items_by_sku_MHZ(items_ids):
    item_objects = Item.objects.select_related("type").filter(pk__in=items_ids).exclude(state='INACTIVE')
    type_items_dict = defaultdict(list)
    for item in item_objects:
        type_items_dict[item.type].append(item.pk)

    result = [{'type': type, 'items': items} for type, items in type_items_dict.items()]
    return result



def get_items_prefixes(sku_id, customer_id):
    sql_statement = "SELECT DISTINCT(SUBSTRING(inventory_item.id,0,17)) AS item_id_prefix FROM inventory_item" \
                    " inner join inventory_sku on inventory_item.sku_id = inventory_sku.id " \
                    "WHERE sku_id=%s and inventory_sku.customer_id=%s"

    rows_list = []

    rows = ()

    with connection.cursor() as cursor:
        cursor.execute(sql_statement, [sku_id, customer_id])
        rows = cursor.fetchall()

    if not rows:
        return []
    else:
        for row_tuple in rows:
            for row in row_tuple:
                rows_list.append(row)
        return rows_list


def get_location_tree(location_id, only_childs: bool = False):
    value = []
    location = get_object_or_404(Location, pk=location_id)
    location_objects = not isinstance(location, (list, tuple)) and [location] or location

    def get_childs(location):
        for child in location.reply_set.all():
            value.append(child.id)
            get_childs(child)

    for obj in location_objects:
        if not only_childs:
            value.append(obj.id)
        get_childs(obj)
    return value


# TODO: Used in case 'get_location_tree' failed.
def get_locations_tree(location_id):
    location_objects = list(Location.objects.all().order_by('id'))
    location_tree_ids = get_location_tree_ids(location_objects=location_objects, location_id=int(location_id))
    return location_tree_ids


def get_location_tree_ids(location_objects, location_id):
    location_tree_list = []
    for location_object in location_objects:
        if location_object.parent_id == location_id:
            location_tree_list += get_location_tree_ids(
                location_objects=location_objects, location_id=location_object.id)
    if location_tree_list:
        location_tree_list.append(location_id)
        return location_tree_list
    else:
        return [location_id]


def join_up_repeated_sku(summary_list, reserved_lines, join_up_fields, customer):
    total_summary = []
    last_sku_id = 0
    last_summary_object = []
    if reserved_lines:
        reserved_line = reserved_lines.pop()
    else:
        reserved_line = {}
        reserved_line.setdefault('sku_id', 0)
    if customer.name == 'MARROQUINERA_COLOMBIA':
        for summary_object in summary_list:
            if summary_object.sku_id == last_sku_id:
                for field in join_up_fields:
                    exec('last_summary_object.{} += summary_object.{}'.format(field, field))
            else:
                total_summary, reserved_lines, reserved_line = join_up_reserved(
                    last_summary_object=last_summary_object,
                    last_sku_id=last_sku_id,
                    reserved_line=reserved_line,
                    reserved_lines=reserved_lines,
                    total_summary=total_summary)
                last_summary_object = summary_object
                last_sku_id = summary_object.sku_id
        if summary_list and summary_object.sku_id == last_sku_id:
            total_summary, reserved_lines, reserved_line = join_up_reserved(last_summary_object=last_summary_object,
                                                                            last_sku_id=last_sku_id,
                                                                            reserved_line=reserved_line,
                                                                            reserved_lines=reserved_lines,
                                                                            total_summary=total_summary)

        return total_summary


    else:
        for summary_object in summary_list:
            if summary_object.sku_id == last_sku_id:
                for field in join_up_fields:
                    exec('last_summary_object.{} += summary_object.{}'.format(field, field))

            else:
                total_summary, reserved_lines, reserved_line = join_up_reserved(last_summary_object=last_summary_object,
                                                                                last_sku_id=last_sku_id,
                                                                                reserved_line=reserved_line,
                                                                                reserved_lines=reserved_lines,
                                                                                total_summary=total_summary)
                last_summary_object = summary_object
                last_sku_id = summary_object.sku_id
        if summary_list and summary_object.sku_id == last_sku_id:
            total_summary, reserved_lines, reserved_line = join_up_reserved(last_summary_object=last_summary_object,
                                                                            last_sku_id=last_sku_id,
                                                                            reserved_line=reserved_line,
                                                                            reserved_lines=reserved_lines,
                                                                            total_summary=total_summary)
        return total_summary


def group_by_state(lines_object, customer_id, current_location_id):
    location = Location.objects.filter(pk=current_location_id)[0]
    customer = Customer.objects.filter(pk=customer_id)[0]

    grouped = {}
    for line in lines_object:
        for key, val in line.items():
            if grouped.get(key):
                grouped[key] += val
            else:
                grouped[key] = val

    custom = {'customer': customer, 'current_location': location}
    grouped.update(custom)
    return grouped


def group_by_parent_location(line_objects):
    grouped = {}
    for line in line_objects:
        for key, val in line.items():
            if grouped.get(key):
                grouped[key] += val
            else:
                grouped[key] = val
    return grouped


def join_up_reserved(last_summary_object, last_sku_id, reserved_line, reserved_lines, total_summary):
    if last_summary_object:
        total_reserved = 0
        if reserved_line['sku_id'] == last_sku_id:
            while True:
                total_reserved += reserved_line['amount']
                if reserved_lines:
                    reserved_line = reserved_lines.pop()
                    if not reserved_line['sku_id'] == last_sku_id:
                        break
                else:
                    break
            last_summary_object.total_reserved = total_reserved
            last_summary_object.total_difference = last_summary_object.total_available - total_reserved
        total_summary.append(last_summary_object)
    return total_summary, reserved_lines, reserved_line


def get_allowed_items(item_list, location_id, customer_id):
    new_items = ""
    if location_id:
        new_items = Item.objects.filter(pk__in=item_list, current_location_id=location_id,
                                        sku__customer_id=customer_id).values_list('id', flat=True)
    else:
        new_items = Item.objects.filter(pk__in=item_list, sku__customer_id=customer_id).values_list('id', flat=True)
    if len(new_items) == len(item_list):
        return True
    else:
        return False


def get_inactive_items(item_list, location_id, customer_id):
    new_items = None
    if location_id:
        new_items = Item.objects.filter(pk__in=item_list, current_location_id=location_id, sku__customer_id=customer_id,
                                        state='INACTIVE').values_list('id', flat=True)
    else:
        new_items = Item.objects.filter(pk__in=item_list, sku__customer_id=customer_id,
                                        state='INACTIVE').values_list('id', flat=True)
    if len(new_items) == len(item_list):
        return True
    else:
        return False


def sku_sub_locations(sku, location):
    print(print('time0:', datetime.now()))
    location_ids_tree = get_location_tree(location_id=location.id)
    print('time1:', datetime.now())
    item_objects = Item.objects.filter(sku_id=sku, state='PRESENT',
                                       current_location_id__in=location_ids_tree)
    print('time2:', datetime.now())
    locations_list = item_objects.values_list('current_location__display_name', flat=True)\
        .distinct().order_by('current_location_id')
    print('time3:', datetime.now())
    results = {'sub_locations': locations_list}
    return results


def group_items_by_sku_and_location(items_ids: list):
    item_objects = Item.objects.filter(pk__in=items_ids).exclude(state='INACTIVE')
    skus_dict = {}  # {"sku_id": {"location_id": ["item1", "item2", ...]}}
    for item in item_objects:
        skus_dict.setdefault(item.sku_id, {item.current_location_id: []})\
            .setdefault(item.current_location_id, []).append(item)
    return skus_dict

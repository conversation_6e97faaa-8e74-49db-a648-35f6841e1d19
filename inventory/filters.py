# -*- coding: utf-8 -*-
import django_filters

from common.filters import <PERSON><PERSON>ilter, ChoiceInFilter
from django_filters import rest_framework as filters

from .models import *
from .view_models import *


class CountryFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Country
        fields = ['external_id', 'name', 'iso3', 'iso2', 'phonecode', 'capital', 'currency']


class StateCountryFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = StateCountry
        fields = ['external_id', 'name', 'country_id']


class CityFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = City
        fields = ['external_id', 'name', 'state_id', 'country_id']


class TraceFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Trace
        fields = ['item_id', 'location_id', 'reporter_id', 'reporter_source', 'action', 'customer_id']


class MoveFilter(BaseFilter, filters.FilterSet):
    location_source__names = filters.BaseInFilter(field_name="source__name")

    class Meta:
        model = Move
        fields = ['external_id', 'status', 'source_id', 'destination_id', 'type', 'customer_id', 'type_return',
                  'reporter_source', 'location_source__names']


class MoveTotalsFilter(BaseFilter, filters.FilterSet):
    SALE_MOVE_TYPE = (
        ("SALE_OUT", "Sale Out"),
        ("RETURN", "Return")
    )
    type = ChoiceInFilter(field_name="move__type", choices=SALE_MOVE_TYPE)
    reporter_source = ChoiceInFilter(field_name='move__reporter_source', choices=Move.REPORTER_SOURCE)
    customer_id = filters.CharFilter(field_name="move__customer_id", required=True)
    timestamp_min = filters.NumberFilter(field_name="timestamp", lookup_expr="gte")
    timestamp_max = filters.NumberFilter(field_name="timestamp", lookup_expr="lte")

    class Meta:
        model = MoveLine
        fields = ['type', 'timestamp', 'timestamp_min', 'timestamp_max',
                  'customer_id', 'reporter_source']


class MoveSummaryFilter(BaseFilter, filters.FilterSet):
    type = ChoiceInFilter(field_name="move__type", choices=MoveTotalsFilter.SALE_MOVE_TYPE)
    reporter_source = ChoiceInFilter(field_name='move__reporter_source', choices=Move.REPORTER_SOURCE)
    customer_id = filters.CharFilter(field_name="move__customer_id", required=True)
    timestamp_min = filters.NumberFilter(field_name="timestamp", lookup_expr="gte")
    timestamp_max = filters.NumberFilter(field_name="timestamp", lookup_expr="lte")

    class Meta:
        model = MoveLine
        fields = ['type', 'timestamp', 'timestamp_min', 'timestamp_max',
                  'customer_id', 'reporter_source']


class MoveLineFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = MoveLine
        fields = ['move_id', 'sku_id', 'status']


class ReserveFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Reserve
        fields = ['external_id', 'status', 'customer_id', 'location_id']


class ReserveLineFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = ReserveLine
        fields = ['reserve_id', 'reserve__status', 'sku_id']


class LocationTypeFilter(filters.FilterSet):
    class Meta:
        model = LocationType
        fields = ('external_id', 'name', 'parent_id', 'parent__name')


class ReturnTypeFilter(filters.FilterSet):
    class Meta:
        model = ReturnType
        fields = ('external_id', 'name', 'parent_id')


class LocationFilter(BaseFilter, filters.FilterSet):
    names__location = django_filters.BaseInFilter(field_name="name")
    customer_id = filters.ModelMultipleChoiceFilter('customer', queryset=Customer.objects.all())

    class Meta:
        model = Location
        fields = ['id', 'external_id', 'type_id', 'type__name', 'name', 'display_name', 'parent_id', 'is_active',
                  'names__location', 'zone_type']


class MasterSKUFileFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = MasterSKUFile
        fields = ['customer_id', 'sku_group_id', 'sku_type_id']


class MasterLOCATIONFileFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = MasterLOCATIONFile
        fields = ['location_type_id']


class SKUFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = SKU
        fields = ['external_id', 'type_id', 'id', 'reference', 'customer_id', 'group_id']


class SKUTypeFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = SKUType
        fields = ['external_id', 'parent_id']


class SKUGroupFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = SKUGroup
        fields = ['external_id', 'customer_id', 'parent_id']


class ItemTypeFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = ItemType
        fields = ['external_id', 'name', 'description']


class ItemFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Item
        fields = ['id', 'sku_id', 'state', 'current_location_id', 'customer_id', 'type_return', 'type__name']


class AuditFilter(BaseFilter, filters.FilterSet):
    cut_date_min = filters.DateFilter(field_name="cut_date", lookup_expr='gte')
    cut_date_max = filters.DateFilter(field_name="cut_date", lookup_expr='lte')
    cut_date = filters.DateFilter(field_name="cut_date", lookup_expr='exact')
    class Meta:
        model = Audit
        fields = ['external_id', 'location_id', 'status', 'customer_id']


class AuditLineFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = AuditLine
        fields = ['audit_id', 'sku_id']


class SummaryFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = InventorySummaryToLocation
        fields = ['sku_id', 'sku__external_id']


# Additional report filters
class SubLocationInventorySummaryFilter(filters.FilterSet):
    current_sub_location_id = filters.CharFilter(field_name='current_location_id')

    class Meta:
        model = Item
        fields = ['sku_id', 'current_sub_location_id']


class AuditTraceFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = AuditTrace
        fields = ['audit_id']


class PropertiesFilter(filters.FilterSet):

    class Meta:
        model = Properties
        fields = ['name', 'type', 'customer_id']


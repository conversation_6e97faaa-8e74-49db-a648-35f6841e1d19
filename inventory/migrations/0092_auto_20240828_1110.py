# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2024-08-28 16:10
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0011_auto_20220330_1223'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0091_auto_20240827_0935'),
    ]

    operations = [
        migrations.CreateModel(
            name='Properties',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=100)),
                ('display_name', models.CharField(blank=True, max_length=100, null=True)),
                ('type', models.CharField(choices=[('PRODUCTS', 'Products')], max_length=20)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='client', to='customers.Customer')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1724861416.9543614),
        ),
        migrations.AlterField(
            model_name='moveline',
            name='timestamp',
            field=models.FloatField(default=1724861416.9543614),
        ),
    ]

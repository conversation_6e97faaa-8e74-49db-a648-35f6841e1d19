# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 17:03
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.hstore
import django.contrib.postgres.fields.jsonb
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import inventory.models
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customers', '__first__'),
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Audit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('prefix', models.CharField(default='AUD', max_length=3)),
                ('external_id', models.CharField(blank=True, max_length=45, null=True, unique=True)),
                ('cut_date', models.DateField(auto_now_add=True, null=True)),
                ('finish_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('PROCESSING', 'Processing'), ('IN_PROGRESS', 'In Progress'), ('FINISHED', 'Finished'), ('ADJUSTED', 'Adjusted'), ('CANCELED', 'Canceled')], default='CREATED', max_length=20)),
                ('allows_adjustment', models.BooleanField(default=False)),
                ('is_partial_count', models.BooleanField(default=False)),
                ('audit_items_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_upload_to)),
                ('expected_items_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_upload_to)),
                ('extra_items_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_upload_to)),
                ('missing_items_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_upload_to)),
                ('adjustment_items_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_upload_to)),
                ('summary_results_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_upload_to)),
                ('total_expected_items', models.IntegerField(blank=True, null=True)),
                ('total_audit_items', models.IntegerField(blank=True, default=0, null=True)),
                ('total_found_items', models.IntegerField(blank=True, default=0, null=True)),
                ('total_missing_items', models.IntegerField(blank=True, default=0, null=True)),
                ('total_extra_items', models.IntegerField(blank=True, default=0, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='AuditLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('total_expected_items', models.IntegerField(blank=True, null=True)),
                ('total_audit_items', models.IntegerField(blank=True, null=True)),
                ('total_found_items', models.IntegerField(blank=True, null=True)),
                ('total_missing_items', models.IntegerField(blank=True, null=True)),
                ('total_extra_items', models.IntegerField(blank=True, null=True)),
                ('audit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.Audit')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalLocationType',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=15, null=True)),
                ('name', models.CharField(max_length=150)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical location type',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalSKU',
            fields=[
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('id', models.IntegerField(blank=True, db_index=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=45, null=True)),
                ('name', models.CharField(max_length=500)),
                ('display_name', models.CharField(max_length=500)),
                ('filter', models.IntegerField(choices=[(0, '0 - All others'), (1, '1 - Point of Sale (POS) Trade Item'), (2, '2 - Full Case for Transport'), (3, '3 - Reserved'), (4, '4 - Inner Pack Trade Item Grouping for Handling'), (5, '5 - Reserved'), (6, '6 - Unit Load'), (7, '7 - Unit inside Trade Item or component inside a product not intended for individual sale')], default=1)),
                ('company_prefix', models.CharField(blank=True, max_length=20, null=True)),
                ('reference', models.CharField(blank=True, max_length=45, null=True)),
                ('gtin_code', models.CharField(max_length=20, null=True, validators=[django.core.validators.MinLengthValidator(14)])),
                ('gtin_data_structure', models.CharField(choices=[('GTIN_12', 'GTIN 12'), ('GTIN_13', 'GTIN 13')], default='GTIN_13', max_length=20, null=True)),
                ('ean', models.CharField(blank=True, max_length=13, null=True)),
                ('upc', models.CharField(blank=True, max_length=12, null=True)),
                ('isbn', models.CharField(blank=True, max_length=13, null=True)),
                ('serial_count', models.BigIntegerField(default=0)),
                ('properties', django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True)),
                ('search_data', models.TextField(blank=True, max_length=400, null=True)),
                ('photo', models.TextField(blank=True, max_length=500, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer')),
            ],
            options={
                'verbose_name': 'historical sku',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalSKUGroup',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=15, null=True)),
                ('name', models.CharField(max_length=50)),
                ('photo', models.TextField(blank=True, max_length=100, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical sku group',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalSKUType',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=15, null=True)),
                ('name', models.CharField(max_length=50)),
                ('photo', models.TextField(blank=True, max_length=100, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical sku type',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('id', models.CharField(max_length=150, primary_key=True, serialize=False)),
                ('serial', models.BigIntegerField()),
                ('state', models.CharField(choices=[('ISSUED', 'Issued'), ('PRESENT', 'Present'), ('IN_TRANSFER', 'In Transfer'), ('INACTIVE', 'Inactive'), ('LOST', 'Lost'), ('SOLD', 'Sold'), ('NOT_PRESENT', 'Not Present'), ('PACKED', 'Packed'), ('VERIFIED', 'Verified'), ('UNAVAILABLE', 'Unavailable'), ('DISPATCH', 'Dispatch')], default='PRESENT', max_length=20)),
                ('batch_type', models.CharField(choices=[('PRODUCTION_ORDER', 'Production Order'), ('PURCHASE_ORDER', 'Purchase Order'), ('INITIAL_INVENTORY', 'Initial Inventory')], default='PRODUCTION_ORDER', max_length=20)),
                ('batch_number', models.CharField(blank=True, max_length=200, null=True)),
                ('properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('locked', models.BooleanField(default=False)),
                ('verified', models.BooleanField(default=False)),
                ('reserved', models.BooleanField(default=False)),
                ('cut_date', models.DateField(blank=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=50)),
                ('display_name', models.CharField(max_length=100)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('description', models.CharField(blank=True, max_length=100, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True)),
                ('latitude', models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ManyToManyField(to='customers.Customer')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='LocationType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('name', models.CharField(max_length=150)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.LocationType')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Move',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=40, null=True, unique=True)),
                ('type', models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TAKEOUT', 'Take Out'), ('SALE_OUT', 'Sale Out'), ('RETURN', 'Return')], max_length=20)),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned')], default='INCOMPLETE', max_length=20)),
                ('prefix', models.CharField(default='MV', max_length=2)),
                ('items_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_move_files_path)),
                ('summary_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_move_files_path)),
                ('packing_list_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_move_files_path)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('destination', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incoming_moves', to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('source', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_moves', to='inventory.Location')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='MoveLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('amount', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned')], default='INCOMPLETE', max_length=20)),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('expected_items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('received_items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('move', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='inventory.Move')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='SKU',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('external_id', models.CharField(blank=True, max_length=45, null=True, unique=True)),
                ('name', models.CharField(max_length=500)),
                ('display_name', models.CharField(max_length=500)),
                ('filter', models.IntegerField(choices=[(0, '0 - All others'), (1, '1 - Point of Sale (POS) Trade Item'), (2, '2 - Full Case for Transport'), (3, '3 - Reserved'), (4, '4 - Inner Pack Trade Item Grouping for Handling'), (5, '5 - Reserved'), (6, '6 - Unit Load'), (7, '7 - Unit inside Trade Item or component inside a product not intended for individual sale')], default=1)),
                ('company_prefix', models.CharField(blank=True, max_length=20, null=True)),
                ('reference', models.CharField(blank=True, max_length=45, null=True)),
                ('gtin_code', models.CharField(max_length=20, null=True, validators=[django.core.validators.MinLengthValidator(14)])),
                ('gtin_data_structure', models.CharField(choices=[('GTIN_12', 'GTIN 12'), ('GTIN_13', 'GTIN 13')], default='GTIN_13', max_length=20, null=True)),
                ('ean', models.CharField(blank=True, max_length=13, null=True)),
                ('upc', models.CharField(blank=True, max_length=12, null=True)),
                ('isbn', models.CharField(blank=True, max_length=13, null=True)),
                ('serial_count', models.BigIntegerField(default=0)),
                ('properties', django.contrib.postgres.fields.hstore.HStoreField(blank=True, null=True)),
                ('search_data', models.TextField(blank=True, max_length=400, null=True)),
                ('photo', models.ImageField(blank=True, max_length=500, null=True, upload_to='skus')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='SKUGroup',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('name', models.CharField(max_length=50)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='sku_groups')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.SKUGroup')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='SKUType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('name', models.CharField(max_length=50)),
                ('photo', models.ImageField(blank=True, null=True, upload_to='sku_groups')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.SKUType')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Trace',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('reporter_source', models.CharField(choices=[('SYSTEM', 'System Source'), ('FIXED_PORTAL', 'Fixed Portal'), ('HANDHELD', 'Handheld Device'), ('WEB_APP', 'Web Application'), ('PRINTER', 'Printer Device'), ('DESKTOP_APP', 'Desktop Application'), ('POS_DEVICE', 'Point of Sale Device')], max_length=20)),
                ('action', models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return')], max_length=20)),
                ('description', models.CharField(blank=True, max_length=140, null=True)),
                ('additional_data', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='traces', to='inventory.Item')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('reporter', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='item_traces', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.AddField(
            model_name='sku',
            name='group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.SKUGroup'),
        ),
        migrations.AddField(
            model_name='sku',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='sku',
            name='type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKUType'),
        ),
        migrations.AddField(
            model_name='moveline',
            name='sku',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU'),
        ),
        migrations.AddField(
            model_name='location',
            name='type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.LocationType'),
        ),
        migrations.AddField(
            model_name='item',
            name='current_location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.Location'),
        ),
        migrations.AddField(
            model_name='item',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='item',
            name='sku',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU'),
        ),
        migrations.AddField(
            model_name='historicalskutype',
            name='parent',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKUType'),
        ),
        migrations.AddField(
            model_name='historicalskugroup',
            name='parent',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKUGroup'),
        ),
        migrations.AddField(
            model_name='historicalsku',
            name='group',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKUGroup'),
        ),
        migrations.AddField(
            model_name='historicalsku',
            name='history_user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalsku',
            name='modified_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalsku',
            name='type',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKUType'),
        ),
        migrations.AddField(
            model_name='historicallocationtype',
            name='parent',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.LocationType'),
        ),
        migrations.AddField(
            model_name='auditline',
            name='sku',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU'),
        ),
        migrations.AddField(
            model_name='audit',
            name='location',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.Location'),
        ),
        migrations.AddField(
            model_name='audit',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='item',
            unique_together=set([('sku', 'serial')]),
        ),
    ]

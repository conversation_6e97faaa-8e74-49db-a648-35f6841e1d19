# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-10-10 01:29
from __future__ import unicode_literals

import common.models
from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import functools
import inventory.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0048_auto_20221004_2001'),
    ]

    operations = [
        migrations.CreateModel(
            name='SkuKeys',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField()),
            ],
            options={
                'db_table': 'sku_keys',
                'ordering': ['-id'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
        migrations.CreateModel(
            name='SkuProperties',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField()),
                ('value', models.CharField()),
            ],
            options={
                'db_table': 'sku_key_value',
                'ordering': ['-id'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
        migrations.CreateModel(
            name='AuditTrace',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('audit_items_file', models.FileField(blank=True, null=True, upload_to=inventory.models.get_upload_audit_trace)),
                ('total_audit_items', models.IntegerField(blank=True, default=0, null=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('PROCESSING', 'Processing'), ('IN_PROGRESS', 'In Progress'), ('FINISHED', 'Finished'), ('ADJUSTED', 'Adjusted'), ('REGISTERED', 'registered'), ('CANCELED', 'Canceled')], default='IN_PROGRESS', max_length=20)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='audit',
            name='audit_summary_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'audit_summary_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='execute_image',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='audit',
            name='expected_summary_file',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='audit',
            name='extra_summary_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'extra_summary_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='found_items_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'found_items_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='found_summary_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'found_summary_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='missing_summary_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'missing_summary_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='not_items_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'not_items_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='omitted_items_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'omitted_items_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='omitted_summary_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'omitted_summary_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='audit',
            name='result_items_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'result_items_file'})),
        ),
        migrations.AddField(
            model_name='audit',
            name='total_audit_not_items',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='audit',
            name='total_omitted_items',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='audit',
            name='type',
            field=models.CharField(choices=[('GENERAL', 'General'), ('PROPERTY', 'Property')], default='GENERAL', max_length=20),
        ),
        migrations.AddField(
            model_name='auditline',
            name='items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True),
        ),
        migrations.AddField(
            model_name='auditline',
            name='total_audit_not_items',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='audit_summary_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='execute_image',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='expected_summary_file',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='extra_summary_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='found_items_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='found_summary_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='missing_summary_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='not_items_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='omitted_items_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='omitted_summary_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='properties',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='result_items_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='total_audit_not_items',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='total_omitted_items',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='type',
            field=models.CharField(choices=[('GENERAL', 'General'), ('PROPERTY', 'Property')], default='GENERAL', max_length=20),
        ),
        migrations.AddField(
            model_name='historicalauditline',
            name='items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True),
        ),
        migrations.AddField(
            model_name='historicalauditline',
            name='total_audit_not_items',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='audit',
            name='audit_items_file',
            field=models.FileField(blank=True, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'audit_items_file'})),
        ),
        migrations.AlterField(
            model_name='audit',
            name='expected_items_file',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='audit',
            name='extra_items_file',
            field=models.FileField(blank=True, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'extra_items_file'})),
        ),
        migrations.AlterField(
            model_name='audit',
            name='location',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location'),
        ),
        migrations.AlterField(
            model_name='audit',
            name='missing_items_file',
            field=models.FileField(blank=True, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'missing_items_file'})),
        ),
        migrations.AlterField(
            model_name='auditline',
            name='sku',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU'),
        ),
        migrations.AlterField(
            model_name='historicalaudit',
            name='expected_items_file',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='audittrace',
            name='audit',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Audit'),
        ),
        migrations.AddField(
            model_name='audittrace',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='audittrace',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
    ]

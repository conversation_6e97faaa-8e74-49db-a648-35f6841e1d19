# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-03-03 14:52
from __future__ import unicode_literals

import django.contrib.postgres.fields.hstore
import django.contrib.postgres.fields.jsonb
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0039_auto_20211019_1233'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicallocation',
            name='params',
            field=django.contrib.postgres.fields.jsonb.J<PERSON><PERSON>ield(default={}),
        ),
        migrations.AddField(
            model_name='location',
            name='params',
            field=django.contrib.postgres.fields.jsonb.JSONField(default={}),
        ),
        migrations.AlterField(
            model_name='historicallocation',
            name='properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(default={}),
        ),
        migrations.Alter<PERSON>ield(
            model_name='location',
            name='properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(default={}),
        ),
    ]

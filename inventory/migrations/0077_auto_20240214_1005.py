# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2024-02-14 15:05
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0076_auto_20231116_1021'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicallocation',
            name='zone_type',
            field=models.CharField(blank=True, choices=[('RECEPTION', 'Reception')], max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='location',
            name='zone_type',
            field=models.CharField(blank=True, choices=[('RECEPTION', 'Reception')], max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1707923137.241427),
        ),
        migrations.AlterField(
            model_name='historicaltrace',
            name='action',
            field=models.Char<PERSON><PERSON>(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return'), ('UNAVAILABLE', 'Unavailable'), ('UPDATE_ITEM', 'Update item')], max_length=20),
        ),
        migrations.AlterField(
            model_name='moveline',
            name='timestamp',
            field=models.FloatField(default=1707923137.241427),
        ),
        migrations.AlterField(
            model_name='trace',
            name='action',
            field=models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return'), ('UNAVAILABLE', 'Unavailable'), ('UPDATE_ITEM', 'Update item')], max_length=20),
        ),
    ]

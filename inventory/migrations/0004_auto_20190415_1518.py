# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 20:18
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0001_initial'),
        ('inventory', '0003_historicalaudit_historicalauditline_historicalitem_historicalmove_historicalmoveline_historicaltrace'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalitem',
            name='customer',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='historicalmove',
            name='customer',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='item',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='move',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer'),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2024-04-17 20:21
from __future__ import unicode_literals

from django.db import migrations, models
import functools
import inventory.models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0083_auto_20240417_1145'),
    ]

    operations = [
        migrations.AddField(
            model_name='audit',
            name='items_type_summary_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'items_type_summary_file'})),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='items_type_summary_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1713385264.9269476),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='moveline',
            name='timestamp',
            field=models.Float<PERSON>ield(default=1713385264.9269476),
        ),
    ]

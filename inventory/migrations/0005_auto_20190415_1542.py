# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 20:42
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0001_initial'),
        ('inventory', '0004_auto_20190415_1518'),
    ]

    operations = [
        migrations.AddField(
            model_name='audit',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='customer',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='historicalskugroup',
            name='customer',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='historicaltrace',
            name='customer',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='skugroup',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer'),
        ),
        migrations.AddField(
            model_name='trace',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer'),
        ),
    ]

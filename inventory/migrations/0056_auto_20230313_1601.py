# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2023-03-13 21:01
from __future__ import unicode_literals

from django.db import migrations, models
import functools
import inventory.models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0055_auto_20230313_1134'),
    ]

    operations = [
        migrations.AlterField(
            model_name='audit',
            name='missing_items_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'missing_items_file'})),
        ),
        migrations.AlterField(
            model_name='historicalaudit',
            name='missing_items_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
    ]

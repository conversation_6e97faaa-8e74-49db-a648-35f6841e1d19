# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 19:59
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0002_auto_20190415_1203'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalAudit',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('prefix', models.CharField(default='AUD', max_length=3)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=45, null=True)),
                ('cut_date', models.DateField(blank=True, editable=False, null=True)),
                ('finish_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('PROCESSING', 'Processing'), ('IN_PROGRESS', 'In Progress'), ('FINISHED', 'Finished'), ('ADJUSTED', 'Adjusted'), ('CANCELED', 'Canceled')], default='CREATED', max_length=20)),
                ('allows_adjustment', models.BooleanField(default=False)),
                ('is_partial_count', models.BooleanField(default=False)),
                ('audit_items_file', models.TextField(blank=True, max_length=100, null=True)),
                ('expected_items_file', models.TextField(blank=True, max_length=100, null=True)),
                ('extra_items_file', models.TextField(blank=True, max_length=100, null=True)),
                ('missing_items_file', models.TextField(blank=True, max_length=100, null=True)),
                ('adjustment_items_file', models.TextField(blank=True, max_length=100, null=True)),
                ('summary_results_file', models.TextField(blank=True, max_length=100, null=True)),
                ('total_expected_items', models.IntegerField(blank=True, null=True)),
                ('total_audit_items', models.IntegerField(blank=True, default=0, null=True)),
                ('total_found_items', models.IntegerField(blank=True, default=0, null=True)),
                ('total_missing_items', models.IntegerField(blank=True, default=0, null=True)),
                ('total_extra_items', models.IntegerField(blank=True, default=0, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical audit',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalAuditLine',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('total_expected_items', models.IntegerField(blank=True, null=True)),
                ('total_audit_items', models.IntegerField(blank=True, null=True)),
                ('total_found_items', models.IntegerField(blank=True, null=True)),
                ('total_missing_items', models.IntegerField(blank=True, null=True)),
                ('total_extra_items', models.IntegerField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('audit', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Audit')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('sku', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKU')),
            ],
            options={
                'verbose_name': 'historical audit line',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalItem',
            fields=[
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('id', models.CharField(db_index=True, max_length=150)),
                ('serial', models.BigIntegerField()),
                ('state', models.CharField(choices=[('ISSUED', 'Issued'), ('PRESENT', 'Present'), ('IN_TRANSFER', 'In Transfer'), ('INACTIVE', 'Inactive'), ('LOST', 'Lost'), ('SOLD', 'Sold'), ('NOT_PRESENT', 'Not Present'), ('PACKED', 'Packed'), ('VERIFIED', 'Verified'), ('UNAVAILABLE', 'Unavailable'), ('DISPATCH', 'Dispatch')], default='PRESENT', max_length=20)),
                ('batch_type', models.CharField(choices=[('PRODUCTION_ORDER', 'Production Order'), ('PURCHASE_ORDER', 'Purchase Order'), ('INITIAL_INVENTORY', 'Initial Inventory')], default='PRODUCTION_ORDER', max_length=20)),
                ('batch_number', models.CharField(blank=True, max_length=200, null=True)),
                ('properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('locked', models.BooleanField(default=False)),
                ('verified', models.BooleanField(default=False)),
                ('reserved', models.BooleanField(default=False)),
                ('cut_date', models.DateField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('current_location', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('sku', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKU')),
            ],
            options={
                'verbose_name': 'historical item',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalMove',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=40, null=True)),
                ('type', models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TAKEOUT', 'Take Out'), ('SALE_OUT', 'Sale Out'), ('RETURN', 'Return')], max_length=20)),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned')], default='INCOMPLETE', max_length=20)),
                ('prefix', models.CharField(default='MV', max_length=2)),
                ('items_file', models.TextField(blank=True, max_length=100, null=True)),
                ('summary_file', models.TextField(blank=True, max_length=100, null=True)),
                ('packing_list_file', models.TextField(blank=True, max_length=100, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('destination', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('source', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
            ],
            options={
                'verbose_name': 'historical move',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalMoveLine',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('amount', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned')], default='INCOMPLETE', max_length=20)),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('expected_items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('received_items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'items': []}, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('move', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Move')),
                ('sku', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKU')),
            ],
            options={
                'verbose_name': 'historical move line',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalTrace',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('reporter_source', models.CharField(choices=[('SYSTEM', 'System Source'), ('FIXED_PORTAL', 'Fixed Portal'), ('HANDHELD', 'Handheld Device'), ('WEB_APP', 'Web Application'), ('PRINTER', 'Printer Device'), ('DESKTOP_APP', 'Desktop Application'), ('POS_DEVICE', 'Point of Sale Device')], max_length=20)),
                ('action', models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return')], max_length=20)),
                ('description', models.CharField(blank=True, max_length=140, null=True)),
                ('additional_data', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('item', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Item')),
                ('location', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('reporter', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical trace',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]

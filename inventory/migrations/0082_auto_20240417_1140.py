# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2024-04-17 16:40
from __future__ import unicode_literals

from django.db import migrations, models
import functools
import inventory.models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0081_auto_20240415_1621'),
    ]

    operations = [
        migrations.AddField(
            model_name='audit',
            name='items_type_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'items_type_file'})),
        ),
        migrations.AddField(
            model_name='historicalaudit',
            name='items_type_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1713372017.3696747),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='moveline',
            name='timestamp',
            field=models.Float<PERSON>ield(default=1713372017.3696747),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 17:03
from __future__ import unicode_literals

import common.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LocationInventorySummary',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location_type', models.CharField(max_length=20)),
                ('total_issued', models.IntegerField()),
                ('total_present', models.IntegerField()),
                ('total_in_transfer', models.IntegerField()),
                ('total_lost', models.IntegerField()),
                ('total_not_present', models.IntegerField()),
                ('total_packed', models.IntegerField()),
                ('total_dispatch', models.IntegerField()),
                ('total_reserved', models.IntegerField()),
                ('total_difference', models.IntegerField()),
                ('total', models.IntegerField()),
            ],
            options={
                'db_table': 'inventory_summary_to_location',
                'ordering': ['-current_location_id', '-total_present'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
        migrations.CreateModel(
            name='LocationItemsTotal',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_present', models.IntegerField()),
                ('total_in_transfer', models.IntegerField()),
                ('total_packed', models.IntegerField()),
                ('total_lost', models.IntegerField()),
                ('total_dispatch', models.IntegerField()),
                ('total_reserved', models.IntegerField()),
                ('total_difference', models.IntegerField()),
                ('total', models.IntegerField()),
            ],
            options={
                'db_table': 'total_items_by_location',
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
        migrations.CreateModel(
            name='LocationTypeInventorySummary',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location_type', models.CharField(max_length=20)),
                ('total_issued', models.IntegerField()),
                ('total_present', models.IntegerField()),
                ('total_in_transfer', models.IntegerField()),
                ('total_lost', models.IntegerField()),
                ('total_not_present', models.IntegerField()),
                ('total_packed', models.IntegerField()),
                ('total_dispatch', models.IntegerField()),
                ('total_reserved', models.IntegerField()),
                ('total_difference', models.IntegerField()),
                ('total', models.IntegerField()),
            ],
            options={
                'db_table': 'inventory_summary_to_location_type',
                'ordering': ['-total_present'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
    ]

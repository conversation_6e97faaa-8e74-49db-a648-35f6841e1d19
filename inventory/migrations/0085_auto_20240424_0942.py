# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2024-04-24 14:42
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0084_auto_20240417_1521'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalitemtype',
            name='name',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='historicalmove',
            name='status',
            field=models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='historicalmoveline',
            name='status',
            field=models.Char<PERSON>ield(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1713969735.1890802),
        ),
        migrations.AlterField(
            model_name='historicaltrace',
            name='action',
            field=models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return'), ('UNAVAILABLE', 'Unavailable'), ('UPDATE_ITEM', 'Update item'), ('CLOSED', 'Closed')], max_length=20),
        ),
        migrations.AlterField(
            model_name='itemtype',
            name='name',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='move',
            name='status',
            field=models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='moveline',
            name='status',
            field=models.CharField(choices=[('COMPLETED', 'Completed'), ('INCOMPLETE', 'Incomplete'), ('DRAFT', 'Draft'), ('PROCESSING', 'Processing'), ('INVALID', 'Invalid'), ('UNASSIGNED', 'Unassigned'), ('CLOSED', 'Closed')], default='INCOMPLETE', max_length=20),
        ),
        migrations.AlterField(
            model_name='moveline',
            name='timestamp',
            field=models.FloatField(default=1713969735.1890802),
        ),
        migrations.AlterField(
            model_name='trace',
            name='action',
            field=models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return'), ('UNAVAILABLE', 'Unavailable'), ('UPDATE_ITEM', 'Update item'), ('CLOSED', 'Closed')], max_length=20),
        ),
    ]

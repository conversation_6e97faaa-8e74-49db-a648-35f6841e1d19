# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2023-09-20 16:36
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0070_auto_20230911_1042'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalItemType',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=15, null=True)),
                ('name', models.CharField(blank=True, choices=[('CORPORATE SALES', 'CORPORATE SALES')], max_length=20, null=True)),
                ('display_name', models.CharField(max_length=100)),
                ('description', models.CharField(max_length=100)),
                ('params', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical item type',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='ItemType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('name', models.CharField(blank=True, choices=[('CORPORATE SALES', 'CORPORATE SALES')], max_length=20, null=True)),
                ('display_name', models.CharField(max_length=100)),
                ('description', models.CharField(max_length=100)),
                ('params', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1695227810.3178225),
        ),
        migrations.AlterField(
            model_name='moveline',
            name='timestamp',
            field=models.FloatField(default=1695227810.3178225),
        ),
        migrations.AddField(
            model_name='historicalitem',
            name='type',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.ItemType'),
        ),
        migrations.AddField(
            model_name='item',
            name='type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.ItemType'),
        ),
    ]

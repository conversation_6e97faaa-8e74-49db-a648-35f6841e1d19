# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-09-10 04:25
from __future__ import unicode_literals

import django.contrib.postgres.fields.hstore
import django.contrib.postgres.fields.jsonb
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0042_auto_20220829_1003'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicallocation',
            name='params',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name='historicallocation',
            name='properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name='location',
            name='params',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name='location',
            name='properties',
            field=django.contrib.postgres.fields.hstore.HStoreField(blank=True, default={}, null=True),
        ),
    ]

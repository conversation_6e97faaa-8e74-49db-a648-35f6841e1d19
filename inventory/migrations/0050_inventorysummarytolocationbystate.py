# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-10-10 05:26
from __future__ import unicode_literals

import common.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0049_auto_20221009_2029'),
    ]

    operations = [
        migrations.CreateModel(
            name='InventorySummaryToLocationByState',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_present', models.IntegerField(null=True)),
            ],
            options={
                'db_table': 'inventory_summary_to_location_by_state',
                'ordering': ['-current_location_id', '-total_present'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
    ]

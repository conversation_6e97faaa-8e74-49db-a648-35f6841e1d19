# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2024-07-05 21:29
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0088_auto_20240607_0917'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalitem',
            name='total_amount_cloned',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='item',
            name='total_amount_cloned',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1720214961.6963634),
        ),
        migrations.AlterField(
            model_name='historicaltrace',
            name='action',
            field=models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return'), ('UNAVAILABLE', 'Unavailable'), ('UPDATE_ITEM', 'Update item'), ('CLOSED', 'Closed'), ('CLONED', 'Cloned')], max_length=20),
        ),
        migrations.AlterField(
            model_name='moveline',
            name='timestamp',
            field=models.FloatField(default=1720214961.6963634),
        ),
        migrations.AlterField(
            model_name='trace',
            name='action',
            field=models.CharField(choices=[('SIMPLE_ENTRY', 'Simple Entry'), ('SIMPLE_OUTPUT', 'Simple Output'), ('TRANSFER', 'Transfer'), ('TRANSFER_OUT', 'Transfer Out'), ('TRANSFER_IN', 'Transfer In'), ('PRINT', 'Print'), ('AUDIT_ENTRY', 'Audit Entry'), ('TAKEOUT', 'Take Out'), ('SALE', 'Sale'), ('PACK', 'Pack'), ('UNPACK', 'Unpack'), ('READING', 'Reading'), ('VERIFIED', 'Verified'), ('OTHER', 'Other'), ('RETURN', 'Return'), ('UNAVAILABLE', 'Unavailable'), ('UPDATE_ITEM', 'Update item'), ('CLOSED', 'Closed'), ('CLONED', 'Cloned')], max_length=20),
        ),
    ]

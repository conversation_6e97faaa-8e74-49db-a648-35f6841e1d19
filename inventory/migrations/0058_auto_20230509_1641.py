# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2023-05-09 21:41
from __future__ import unicode_literals

import common.models
from django.db import migrations, models
import django.db.models.deletion
import inventory.models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0057_auto_20230418_0918'),
    ]

    operations = [
        migrations.CreateModel(
            name='SKURecursiveCode',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recursive_code', models.CharField(max_length=500)),
            ],
            options={
                'db_table': 'sku_recursive_code',
                'ordering': ['-id'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
        migrations.AddField(
            model_name='historicalmove',
            name='master_file',
            field=models.TextField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='historicalmove',
            name='reporter_source',
            field=models.CharField(choices=[('SYSTEM', 'System Source'), ('FIXED_PORTAL', 'Fixed Portal'), ('HANDHELD', 'Handheld Device'), ('WEB_APP', 'Web Application'), ('PRINTER', 'Printer Device'), ('DESKTOP_APP', 'Desktop Application'), ('POS_DEVICE', 'Point of Sale Device')], default='HANDHELD', max_length=20),
        ),
        migrations.AddField(
            model_name='historicalmoveline',
            name='price',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='historicalmoveline',
            name='sub_location',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location'),
        ),
        migrations.AddField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1683668482.507641),
        ),
        migrations.AddField(
            model_name='move',
            name='master_file',
            field=models.FileField(blank=True, null=True, upload_to=inventory.models.get_move_files_path),
        ),
        migrations.AddField(
            model_name='move',
            name='reporter_source',
            field=models.CharField(choices=[('SYSTEM', 'System Source'), ('FIXED_PORTAL', 'Fixed Portal'), ('HANDHELD', 'Handheld Device'), ('WEB_APP', 'Web Application'), ('PRINTER', 'Printer Device'), ('DESKTOP_APP', 'Desktop Application'), ('POS_DEVICE', 'Point of Sale Device')], default='HANDHELD', max_length=20),
        ),
        migrations.AddField(
            model_name='moveline',
            name='price',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='moveline',
            name='sub_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location'),
        ),
        migrations.AddField(
            model_name='moveline',
            name='timestamp',
            field=models.FloatField(default=1683668482.507641),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2023-08-29 17:01
from __future__ import unicode_literals

import common.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0062_auto_20230710_2141'),
    ]

    operations = [
        migrations.CreateModel(
            name='InventorySalesReport',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.Char<PERSON><PERSON>(max_length=20)),
                ('total_amount', models.IntegerField(null=True)),
                ('lines_count', models.IntegerField(null=True)),
                ('timestamp', models.FloatField()),
            ],
            options={
                'db_table': 'inventory_reports_sales',
                'ordering': ['-timestamp'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1693328504.1435797),
        ),
        migrations.AlterField(
            model_name='moveline',
            name='timestamp',
            field=models.FloatField(default=1693328504.1435797),
        ),
    ]

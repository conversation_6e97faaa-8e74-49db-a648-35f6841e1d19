# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2023-07-11 02:41
from __future__ import unicode_literals

from django.db import migrations, models
import functools
import inventory.models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0061_auto_20230710_1159'),
    ]

    operations = [
        migrations.AlterField(
            model_name='audit',
            name='adjustment_items_file',
            field=models.FileField(blank=True, max_length=200, null=True, upload_to=functools.partial(inventory.models.get_upload_to, *(), **{'file_type': 'adjustment_items_file'})),
        ),
        migrations.AlterField(
            model_name='historicalaudit',
            name='adjustment_items_file',
            field=models.TextField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1689043270.2723575),
        ),
        migrations.Alter<PERSON>ield(
            model_name='moveline',
            name='timestamp',
            field=models.Float<PERSON>ield(default=1689043270.2723575),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-06-20 16:41
from __future__ import unicode_literals

import common.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0016_auto_20190618_1605'),
    ]

    operations = [
        migrations.CreateModel(
            name='InventorySummaryReserved',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_present', models.IntegerField()),
                ('total_reserved', models.IntegerField()),
                ('total_difference', models.IntegerField()),
            ],
            options={
                'db_table': 'inventory_summary_reserved',
                'ordering': ['-current_location_id', '-total_present'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
        migrations.CreateModel(
            name='InventorySummaryToLocation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_issued', models.IntegerField()),
                ('total_present', models.IntegerField()),
                ('total_in_transfer', models.IntegerField()),
                ('total_lost', models.IntegerField()),
                ('total_sold', models.IntegerField()),
                ('total_not_present', models.IntegerField()),
                ('total_packed', models.IntegerField()),
                ('total_dispatch', models.IntegerField()),
                ('total_reserved', models.IntegerField()),
                ('total_difference', models.IntegerField()),
                ('total', models.IntegerField()),
            ],
            options={
                'db_table': 'inventory_summary_to_location',
                'ordering': ['-current_location_id', '-total_present'],
                'managed': False,
            },
            bases=(models.Model, common.models.ViewModel),
        ),
    ]

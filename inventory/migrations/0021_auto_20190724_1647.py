# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-07-24 21:47
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0020_auto_20190724_1643'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='city',
            name='created',
        ),
        migrations.RemoveField(
            model_name='city',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='city',
            name='external_id',
        ),
        migrations.RemoveField(
            model_name='city',
            name='iso2',
        ),
        migrations.RemoveField(
            model_name='city',
            name='modified',
        ),
        migrations.RemoveField(
            model_name='city',
            name='modified_by',
        ),
        migrations.RemoveField(
            model_name='historicalcity',
            name='created',
        ),
        migrations.RemoveField(
            model_name='historicalcity',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='historicalcity',
            name='external_id',
        ),
        migrations.RemoveField(
            model_name='historicalcity',
            name='iso2',
        ),
        migrations.RemoveField(
            model_name='historicalcity',
            name='modified',
        ),
        migrations.RemoveField(
            model_name='historicalcity',
            name='modified_by',
        ),
        migrations.RemoveField(
            model_name='historicalstatecountry',
            name='created',
        ),
        migrations.RemoveField(
            model_name='historicalstatecountry',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='historicalstatecountry',
            name='external_id',
        ),
        migrations.RemoveField(
            model_name='historicalstatecountry',
            name='iso2',
        ),
        migrations.RemoveField(
            model_name='historicalstatecountry',
            name='modified',
        ),
        migrations.RemoveField(
            model_name='historicalstatecountry',
            name='modified_by',
        ),
        migrations.RemoveField(
            model_name='statecountry',
            name='created',
        ),
        migrations.RemoveField(
            model_name='statecountry',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='statecountry',
            name='external_id',
        ),
        migrations.RemoveField(
            model_name='statecountry',
            name='iso2',
        ),
        migrations.RemoveField(
            model_name='statecountry',
            name='modified',
        ),
        migrations.RemoveField(
            model_name='statecountry',
            name='modified_by',
        ),
        migrations.AddField(
            model_name='city',
            name='country',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='inventory.Country'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='city',
            name='created_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='city',
            name='flag',
            field=models.IntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='city',
            name='updated_on',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcity',
            name='country',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Country'),
        ),
        migrations.AddField(
            model_name='historicalcity',
            name='created_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcity',
            name='flag',
            field=models.IntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='historicalcity',
            name='updated_on',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalstatecountry',
            name='created_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalstatecountry',
            name='flag',
            field=models.IntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='historicalstatecountry',
            name='updated_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='statecountry',
            name='created_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='statecountry',
            name='flag',
            field=models.IntegerField(default=1),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='statecountry',
            name='updated_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]

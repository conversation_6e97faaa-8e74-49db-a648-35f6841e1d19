# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2023-09-05 15:00
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0063_auto_20230829_1201'),
    ]

    operations = [
        migrations.CreateModel(
            name='SalesSKU',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('reporter_source', models.CharField(choices=[('HANDHELD', 'Handheld Device'), ('WEB_APP', 'Web Application'), ('POS_DEVICE', 'Point of Sale Device')], default='HANDHELD', max_length=20)),
                ('total_amount', models.IntegerField()),
                ('total_lines', models.IntegerField()),
                ('timestamp', models.FloatField(default=1693926016.0165424)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('sku', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU')),
                ('sub_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AlterField(
            model_name='historicalmoveline',
            name='timestamp',
            field=models.FloatField(default=1693926015.988757),
        ),
        migrations.AlterField(
            model_name='moveline',
            name='timestamp',
            field=models.FloatField(default=1693926015.988757),
        ),
    ]

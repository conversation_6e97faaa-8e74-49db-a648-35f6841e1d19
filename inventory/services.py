import json
import boto3
from main.conf import common
from inventory.models import MoveLine
from inventory.serializers import SKUSerializer, MoveSerializer, MoveLineServiceSerializer
from core_config.models import CustomerMetadata
from core_config.serializers import CustomerMetadataSerializer


def audit_img_lambda(audit):
    session = boto3.Session(region_name=common.AWS_REGION, aws_access_key_id=common.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=common.AWS_SECRET_ACCESS_KEY)
    client = session.client('lambda')

    response = client.invoke(
        FunctionName=common.AWS_LAMBDA_NAME,
        InvocationType='RequestResponse',
        Payload=json.dumps({"audit_id": str(audit.id), "current_location_id": str(audit.location.id), "type_audit": str(audit.type),
                            "cut_date": str(audit.cut_date), "properties": audit.properties})
    )
    result = response['Payload'].read()
    results = json.loads(result)
    return results


def replicate_create_sku(sku_object):
    if common.AWS_SQS_INTEGRATION_URL:
        session = boto3.Session(region_name=common.AWS_REGION, aws_access_key_id=common.AWS_ACCESS_KEY_ID,
                                aws_secret_access_key=common.AWS_SECRET_ACCESS_KEY)
        client = session.client('sqs')
        customer_metadata = CustomerMetadata.objects.all().first()
        data = {
            "sku": SKUSerializer(sku_object).data,
            "customer_metadata": CustomerMetadataSerializer(customer_metadata).data
        }
        client.send_message(
            QueueUrl=common.AWS_SQS_INTEGRATION_URL,
            MessageBody=json.dumps(data)
        )


def replicate_move_create(move_object):
    if common.AWS_SQS_INTEGRATION_URL:
        session = boto3.Session(region_name=common.AWS_REGION, aws_access_key_id=common.AWS_ACCESS_KEY_ID,
                                aws_secret_access_key=common.AWS_SECRET_ACCESS_KEY)
        client = session.client('sqs')
        customer_metadata = CustomerMetadata.objects.all().first()
        lines = MoveLine.objects.filter(move_id=move_object.id)
        data = {
            "move": MoveSerializer(move_object).data,
            "lines": MoveLineServiceSerializer(lines, many=True).data,
            "customer_metadata": CustomerMetadataSerializer(customer_metadata).data
        }
        client.send_message(
            QueueUrl=common.AWS_SQS_INTEGRATION_URL,
            MessageBody=json.dumps(data)
        )




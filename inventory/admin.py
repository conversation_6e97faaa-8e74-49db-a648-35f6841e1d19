# -*- coding: utf-8 -*-
import bulk_admin
from django.apps import apps
from django.contrib import admin

from .models import *

# auto-register all models
app = apps.get_app_config('inventory')


class CountryAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'iso3', 'iso2', 'created')
    search_fields = ('id', 'name')


class StateCountryAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'country', 'created', 'modified')
    search_fields = ('id', 'name')


class CityAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'state', 'country', 'created')
    search_fields = ('id', 'name')
    raw_id_fields = ('state',)


class LocationAdmin(bulk_admin.BulkModelAdmin):
    list_display = ("id", "external_id", "name", "display_name", "type", "get_customers")
    search_fields = ("id", "name", "display_name", "type__name")
    raw_id_fields = ("parent",)


class LocationTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'parent')


class SKUTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'parent', 'coding_type')


class SKUGroupAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'parent')


class SKUAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'display_name', 'type')
    readonly_fields = ('serial_count',)
    search_fields = ('id', 'external_id')


class ItemTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description', 'external_id', 'params')


class ItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'sku', 'state', 'current_location', 'modified')
    raw_id_fields = ('sku', 'current_location')


class MoveLineInline(admin.TabularInline):
    fields = ('sku', 'amount', 'status', 'items', 'expected_items', 'received_items')
    model = MoveLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class MoveAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'source', 'destination', 'type', 'type_return', 'status', 'customer',
                    'created')
    raw_id_fields = ('source', 'destination')
    inlines = [
        MoveLineInline
    ]
    readonly_fields = ('created', 'modified', 'prefix')
    fieldsets = (
        ('Main Details', {
            'fields': (('created', 'modified',),
                       ('prefix', 'external_id', 'type', 'status'),
                       ('source', 'destination', 'customer', 'type_return'))
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class MoveLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'move', 'sku', 'amount', 'status')
    raw_id_fields = ('move', 'sku')


class SkuSalesAdmin(admin.ModelAdmin):
    list_display = ('id', 'sku', 'reporter_source', 'total_amount')


class TraceAdmin(admin.ModelAdmin):
    list_display = ('id', 'item', 'location', 'reporter', 'reporter_source', 'action', 'modified')
    raw_id_fields = ('item', 'location')


class AuditLineInline(admin.TabularInline):
    fields = ('sku', 'total_expected_items', 'total_audit_items', 'total_found_items', 'total_missing_items',
              'total_extra_items')
    model = AuditLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class AuditAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'location', 'status', 'allows_adjustment', 'total_expected_items',
                    'total_audit_items', 'total_found_items', 'total_missing_items', 'total_extra_items',
                    'finish_date')
    readonly_fields = ('created', 'modified', 'prefix', 'cut_date')
    raw_id_fields = ('location',)
    fieldsets = (
        ('Main Details', {
            'fields': (('created', 'modified', 'external_id'),
                       ('allows_adjustment', 'finish_date'),
                       ('prefix', 'status', 'cut_date'),
                       ('location',),
                       ('total_extra_items', 'total_expected_items'),
                       ('total_found_items', 'total_missing_items')),
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class ReserveLineInline(admin.TabularInline):
    fields = ('sku', 'amount')
    model = ReserveLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class ReserveAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'customer', 'status', 'cut_date', 'location')
    raw_id_fields = ('location',)
    inlines = [
        ReserveLineInline
    ]
    readonly_fields = ('created', 'modified')
    fieldsets = (
        ('Main Details', {
            'fields': (('created', 'modified'),
                       ('external_id', 'status'),
                       ('customer', 'cut_date'),
                       ('location',),)
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class ReserveLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'reserve', 'sku', 'amount')
    raw_id_fields = ('reserve', 'sku')


admin.site.register(Country, CountryAdmin)
admin.site.register(StateCountry, StateCountryAdmin)
admin.site.register(City, CityAdmin)
admin.site.register(Location, LocationAdmin)
admin.site.register(LocationType, LocationTypeAdmin)
admin.site.register(SKUGroup, SKUGroupAdmin)
admin.site.register(SKUType, SKUTypeAdmin)
admin.site.register(SKU, SKUAdmin)
admin.site.register(ItemType, ItemTypeAdmin)
admin.site.register(Item, ItemAdmin)
admin.site.register(Move, MoveAdmin)
admin.site.register(MoveLine, MoveLineAdmin)
admin.site.register(Trace, TraceAdmin)
admin.site.register(Audit, AuditAdmin)
admin.site.register(Reserve, ReserveAdmin)
admin.site.register(ReserveLine, ReserveLineAdmin)
admin.site.register(SalesSKU, SkuSalesAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

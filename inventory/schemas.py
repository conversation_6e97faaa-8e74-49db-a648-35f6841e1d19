import coreapi
import coreschema
from rest_framework import schemas


class AuditFileViewSchema(schemas.AutoSchema):

    def get_manual_fields(self, path, method):
        extra_fields = [
            coreapi.Field(
                name="finished",
                description="Define audit finished (true) or audit in progress (false).",
                location="query",
                schema=coreschema.String(),
                required=False
            )
        ]

        manual_fields = super().get_manual_fields(path, method)
        return manual_fields + extra_fields


class SubLocationInventorySummaryViewSchema(schemas.AutoSchema):

    def get_manual_fields(self, path, method):
        extra_fields = [
            coreapi.Field(
                name="current_location_id",
                description="Location tree by related location",
                location="query",
                schema=coreschema.String(),
                required=True
            )
        ]
        manual_fields = super().get_manual_fields(path, method)
        return manual_fields + extra_fields

    def _allows_filters(self, path, method):
        """
        Determine whether to include filter Fields in schema.

        Default implementation looks for ModelViewSet or GenericAPIView
        actions/methods that cause filtering on the default implementation.

        Override to adjust behaviour for your view.

        Note: Introduced in v3.7: Initially "private" (i.e. with leading underscore)
            to allow changes based on user experience.
        """
        return True

    def get_pagination_fields(self, path, method):
        view = self.view

        if view.action not in ['list']:
            return []

        pagination = getattr(view, 'pagination_class', None)
        if not pagination:
            return []

        paginator = view.pagination_class()
        return paginator.get_schema_fields(view)


class LocationSummaryViewSchema(schemas.AutoSchema):

    def get_manual_fields(self, path, method):
        extra_fields = []
        if path == '/inventory/locations/':
            extra_fields = [
                coreapi.Field(
                    name="reception_locations_filter",
                    description="Activate filter in locations receptions.",
                    location="query",
                    schema=coreschema.Boolean(description="List Location True/False", default=False),
                    required=False
                )
            ]
            manual_fields = super().get_manual_fields(path, method)
            return manual_fields + extra_fields
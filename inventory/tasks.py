# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import datetime
from datetime import datetime, date


#from apscheduler.scheduler import Scheduler
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from . import services as aws_lambda


from inventory.business_logic import audits, skus, locations, reports
from inventory.business_logic.audits import *
from inventory.models import *
import integrations.utils as integration_utils
import integrations.tasks as integration_tasks


sched = BackgroundScheduler()


@shared_task(bind=True)
def apply_move(self, move_id, auto_receive='false'):
    """
    Executes move business logic
    """
    logging.debug("Starting to process apply_move with move {}".format(move_id))
    move_object = Move.objects.get(pk=move_id)
    if move_object.status == "UNASSIGNED":
        return move_object
    if move_object.type == "SIMPLE_ENTRY":
        moves.execute_possible_item_move(move=move_object)
        return moves.execute_simple_entry(move_object)
    elif move_object.type == "TRANSFER":
        moves.execute_transfer(move_object)
        items_list = move_object.items_list
        if auto_receive == "true":
            receive_items(list_items=items_list, move_id=move_object.pk, user_id=move_object.created_by_id)
        return items_list
    elif move_object.type == "RETURN":
        return moves.execute_return(move_object)
    else:
        logging.error("Invalid move %s to process", move_object)


@shared_task(bind=True)
def receive_items(self, move_id, user_id, list_items=[]):
    """
        Executes receive items of business logic
    """
    logging.debug("Starting to process receive_items with move {}".format(move_id))
    move_object = Move.objects.get(pk=move_id)
    user_object = User.objects.get(pk=user_id)
    moves.execute_action_receive(move=move_object, user=user_object, item_ids=list_items)
    logging.debug("Finished to process receive_items with move {}".format(move_id))


@shared_task(bind=True)
def receive_move(self, move_id, user_id, list_items=[]):
    """
        Executes receive items of business logic
    """
    logging.debug("Starting to process receive_items with move {}".format(move_id))
    move_object = Move.objects.get(pk=move_id)
    user_object = User.objects.get(pk=user_id)
    moves.execute_action_close(move=move_object, user=user_object)
    logging.debug("Finished to process receive_items with move {}".format(move_id))


@shared_task(bind=True)
def calculate_sales(self, type_reporters, reporter, created_min, created_max):
    """
        Executes receive items of business logic
    """
    logging.debug("Starting to process calculate sales with products")
    skus.group_by_sales(type_reporters=type_reporters, reporter=reporter, created_min=created_min,
                        created_max=created_max)
    logging.debug("Finished to process calculate sales ")


@shared_task(bind=True)
def move_files(self, move_id):
    move_object = Move.objects.get(pk=move_id)
    """
        Executes generate files of moves
    """
    moves.generate_move_items_file(move=move_object)
    moves.generate_move_summary_file(move=move_object)


@shared_task(bind=True)
def process_move_master_file(self, move_id):
    logging.info('Starting to process move {} master file'.format(move_id))
    move_object = Move.objects.get(pk=move_id)
    move_object.status = "PROCESSING"
    move_object.save()

    moves_list = moves.process_move_master_file(move=move_object)
    for move in moves_list:
        move.status = "COMPLETED"
        move.save()
    logging.warning('Move {} master file Successfully processed'.format(move_id))



def refresh_reserved():
    date = datetime.now().date()
    logging.warning('date and hour: {} '.format(datetime.now().date()))

    logging.warning('date: {} '.format(date))

    reserves = Reserve.objects.filter(status='CREATED')
    logging.warning('Value to validated: {} '.format(reserves))

    reserve_ids = [reserve.id for reserve in reserves]

    for reserve in reserves:
        if reserve.cut_date <= date:
            reserve.cut_date = None
            reserve.status = 'EXPIRED'
            reserve.save()
            logging.warning('Valid: {}-{}-{}'.format(reserve, reserve.cut_date, reserve.status))
            logging.info('Valid: {}-{}-{}'.format(reserve, reserve.cut_date, reserve.status))

    reserve_lines = ReserveLine.objects.filter(reserve_id__in=reserve_ids)
    for line in reserve_lines:
        if line.reserve.status == 'EXPIRED':
            line.amount = 0
            line.save()

sched.add_job(refresh_reserved, trigger=CronTrigger(minute=10, hour=23))
sched.start()


@shared_task(bind=True)
def count_items_file(self, audit_trace_id):
    audit_trace_object = AuditTrace.objects.get(pk=audit_trace_id)
    items = audits.count_trace_items_file(audit_trace_object=audit_trace_object)
    audit_trace_object.total_audit_items = len(items)
    audit_trace_object.save()


@shared_task(bind=True)
def create_trace(self, audit_id, user_id, update_status: bool):
    audit_object = Audit.objects.get(pk=audit_id)
    user_object = User.objects.get(pk=user_id)
    audits.report_audit_trace(audit_object, user_object)
    if update_status:
        audit_object.status = "IN_PROGRESS"
        audit_object.save()


@shared_task(bind=True)
def process_audit(self, audit_id, finished_audit):
    logging.info("Starting to process Audit: {order}".format(order=audit_id))
    audit_object = Audit.objects.get(pk=audit_id)
    audit_object.status = "PROCESSING"
    audit_object.save()
    logging.info('Process audit')
    audit_results = audits.AuditResults(audit_object)
    if audit_object.customer.name != 'MARROQUINERA_COLOMBIA' and audit_object.customer.name != 'ARISTAS':
        audit_results.generate_values()
        audit_results.generate_summary()
    if audit_object.customer.name == 'MARROQUINERA_COLOMBIA' or audit_object.customer.name == 'ARISTAS':
        audit_results.generate_values_MHZ()
        audit_results.generate_summary_MHZ()

    if finished_audit == "true":
        audit_object.status = "FINISHED"
        audit_object.allows_adjustment = True
        audit_object.finish_date = date.today()
        logging.info('finished audit')

    if finished_audit == "false":
        audit_object.status = "IN_PROGRESS"
        logging.info('in process audit')

    audit_object.save()

@shared_task(bind=True)
def adjustment_audit_MHZ(self, audit_id, action_missing, action_extra, reporter_id):
    logging.info("Starting to process Adjustment audit : {order}".format(order=audit_id))
    reporter_object = User.objects.get(pk=reporter_id)

    audit_object = Audit.objects.get(pk=audit_id)
    audit_object.status = "PROCESSING"
    audit_object.save()

    audits.adjustment_audit(audit_object=audit_object, action_missing=action_missing, action_extra=action_extra,
                            reporter_object=reporter_object)

    audit_object.allows_adjustment = False
    audit_object.status = "ADJUSTED"
    audit_object.save()

    logging.info("{order} audits are not allowed for adjustment. ".format(order=audit_object))


@shared_task(bind=True)
def adjustment_audit(self, audit_id, action_missing, action_extra, reporter_id):
    logging.info("Starting to process Adjustment audit : {order}".format(order=audit_id))
    audit_object = Audit.objects.get(pk=audit_id)
    reporter_object = User.objects.get(pk=reporter_id)

    audits.adjust_audit(audit_object=audit_object, action_missing=action_missing,
                        action_extra=action_extra, reporter_object=reporter_object)
    audit_finished = Audit.objects.filter(location=audit_object.location, status="FINISHED",
                                          finish_date__lt=audit_object.finish_date).update(allows_adjustment=False)
    logging.info("{order} audits are not allowed for adjustment. ".format(order=audit_finished))

    audits.generate_file_audit_adjust(audit=audit_object, action_missing=action_missing, action_extra=action_extra)

    module_config = integration_utils.verified_integration_module(
        customer_id=audit_object.customer_id, kong_module__name='INTEGRATIONS_INVENTORY_ADJUST',
        active_integration=True)
    if module_config:
        request_body = {
            "detail-type": "INTEGRATIONS_INVENTORY_ADJUST",
            "audit_id": audit_id
        }
        integration_tasks.integration_module_request(
            integration_module_id=module_config.id, body=request_body)

    logging.info("Generate file of audit adjust {order} ".format(order=audit_object.pk))


@shared_task(bind=True)
def apply_action_items(self, items, source_id, action, description, customer_id, user_id):
    user_object = User.objects.get(pk=user_id)
    customer_object = Customer.objects.get(pk=customer_id)
    if source_id:
        source_object = Location.objects.get(pk=source_id)
    else:
        source_object =  None
    reporter_source: str
    state: str
    if action == 'VERIFIED':
        reporter_source = "HANDHELD"
        state = "VERIFIED"
    elif action == "TAKEOUT":
        reporter_source = "SYSTEM"
        state = "NOT_PRESENT"
    elif action == "OTHER":
        reporter_source = "SYSTEM"
        state = "INACTIVE"

    moves.execute_action_items(items=items, source_object=source_object, user_object=user_object,
                               description=description, action=action, reporter_source=reporter_source,
                               customer_object=customer_object)
    moves.update_items_state(items=items, state=state, reporter=user_object)
    logging.info("Execute in item this {action} ".format(action=action))


@shared_task(bind=True)
def process_take_img_inventory(self, audit_id):
    """
        Execute take picture inventory this moment.
    """
    audit_object = Audit.objects.get(pk=audit_id)
    audit_object.status = "PROCESSING"
    audit_object.save()
    results = aws_lambda.audit_img_lambda(audit=audit_object)

    audit_object_updated = Audit.objects.get(pk=audit_id)

    if results['statusCode'] == 200:
        audit_object_updated.execute_image = True
        audit_object_updated.status = "IN_PROGRESS"

    audit_object_updated.save()
    logging.info("Finished to take img Audit: {order}".format(order=audit_id))

@shared_task(bind=True)
def apply_action_activation_items(self, items, source_id, action, description, customer_id, user_id):
    user_object = User.objects.get(pk=user_id)
    customer_object = Customer.objects.get(pk=customer_id)
    source_object = ""
    if source_id:
        source_object = Location.objects.get(pk=source_id)
    else:
        source_object = None

    reporter_source: str
    state: str

    if action == "ACTIVATE":
        reporter_source = "SYSTEM"
        state = "PRESENT"

    moves.execute_action_activation_items(items=items, source_object=source_object, user_object=user_object,
                               description=description, action=action, reporter_source=reporter_source,
                               customer_object=customer_object)
    moves.update_items_active_state(items=items, state=state, reporter=user_object)
    logging.info("Execute in item this {action} ".format(action=action))


@shared_task(bind=True)
def load_skus_file(self, master_file_id):
    master_file_object = MasterSKUFile.objects.get(pk=master_file_id)
    skus.create_product_master(master_file_object=master_file_object)


@shared_task(bind=True)
def get_move_totals(self, sub_location__type_id, move_type,
                    amount_type, parent_id, sub_location_id, queryset, created_min=None,
                    created_max=None, customer_id=None):
    """
        Executes refresh progress work order.
    """
    move_totals = reports.get_move_total_amount(
        queryset=queryset, move_type=move_type, sub_location_id=sub_location_id,
        parent_id=parent_id, sub_location_type_id=sub_location__type_id,
        customer_id=customer_id, amount_type=amount_type, created_min=created_min, created_max=created_max)
    totals = reports.get_move_location_properties_total_amount(queryset=queryset, move_totals=move_totals,
                                                               amount_type=amount_type, created_min=created_min,
                                                               created_max=created_max)
    return totals


@shared_task(bind=True)
def load_locations_file(self, master_file_id):
    master_file_object = MasterLOCATIONFile.objects.get(pk=master_file_id)
    locations.create_product_master(master_file_object=master_file_object)
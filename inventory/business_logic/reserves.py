# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from ..models import Item, SKU, Location, ReserveLine, Reserve, Trace


def create_lines_from_array(reserve, lines):
    new_lines = []
    for line in lines:
        new_lines.append(
            ReserveLine(
                reserve=reserve,
                sku_id=line['sku_id'],
                amount=line['amount'],
                created_by=reserve.created_by,
                modified_by=reserve.modified_by
            )
        )
    ReserveLine.objects.bulk_create(new_lines)

    return new_lines


def destroy_reserve_lines(reserve):
    lines = ReserveLine.objects.filter(reserve=reserve)
    lines.delete()

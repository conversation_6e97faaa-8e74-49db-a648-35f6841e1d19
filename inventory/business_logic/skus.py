# -*- coding: utf-8 -*-

import csv
from datetime import datetime, date
from django.db.models import Count, Sum
from django.shortcuts import get_object_or_404
import pandas as pd
import logging

from ..models import SKU, MasterSKUFile, Location, Move, MoveLine, SalesSK<PERSON>


def validate_reserved_columns_master_file(master_file_object: MasterSKUFile):
    eans = []
    external_ids = []
    duplicated_eans = None
    reserved_columns = MasterSKUFile.MASTER_FILE_PARAMS["reserved_columns"]
    required_columns = []
    for column_params in reserved_columns.values():
        if column_params.get("required") == "true":
            required_columns.append(column_params["external_column_name"])

    master_file = master_file_object.master_file_sku
    dialect = csv.Sniffer().sniff(str(master_file.read(1024)))
    master_file.seek(0)
    df_master_file = pd.read_csv(master_file, dtype='str', sep=dialect.delimiter)
    duplicated_external_id_df = df_master_file[df_master_file.duplicated(['external_id'], keep=False)]
    duplicated_external_ids = pd.DataFrame(duplicated_external_id_df["external_id"])["external_id"].tolist()
    if len(list(df_master_file.get('ean', []))) > 0:
        duplicated_ean_df = df_master_file[df_master_file.duplicated(['ean'], keep=False)]
        duplicated_eans = pd.DataFrame(duplicated_ean_df["ean"])["ean"].tolist()
    columns = list(df_master_file.columns)
    for required_column in required_columns:
        if required_column not in columns:
            error_message = "Invalid csv format, required columns are {}".format(required_columns)
            raise AttributeError(error_message)
    if duplicated_eans:
        for ean in duplicated_eans:
            if not ean in eans:
                eans.append(ean)
    if duplicated_external_ids:
        for external_id in duplicated_external_ids:
            if not external_id in external_ids:
                external_ids.append(duplicated_external_ids)

    if duplicated_eans or duplicated_external_ids:
        error_message = "Invalid csv format, rows duplicated {} {}".format(external_ids, eans)
        raise AttributeError(error_message)


def create_product_master(master_file_object: MasterSKUFile):
    reserved_columns = MasterSKUFile.MASTER_FILE_PARAMS["reserved_columns"]
    reserved_column_dict = {}  # {"-- file column name --": "-- kong column name --}
    for column_name, column_params in reserved_columns.items():
        reserved_column_dict[column_params["external_column_name"]] = column_name

    # TODO: Option for different codification
    sgtin_filter_value = 1
    data = pd.read_csv(master_file_object.master_file_sku, dtype='str')
    data.fillna('', inplace=True)
    file_keys_list = list(data.columns)
    properties = {}

    # TODO: Validate params for shopify.
    params = {}
    sku_group = master_file_object.sku_group
    reserved_column_list = reserved_column_dict.keys()
    properties_columns = set(file_keys_list) - set(reserved_column_list)
    reserved_file_columns = set(file_keys_list) & set(reserved_column_list)

    for index, row in data.iterrows():
        external_id = row[reserved_columns['external_id']["external_column_name"]]
        if not external_id:
            sku_group.refresh_from_db()
            sku_prefix = sku_group.properties.setdefault("sku_prefix", sku_group.external_id)
            sku_prefix_count = sku_group.properties.setdefault("sku_prefix_count", "1")
            sku_group.properties["sku_prefix_count"] = str(int(sku_prefix_count) + 1)
            sku_group.save()
            external_id = "{}-{}".format(sku_prefix, sku_prefix_count.zfill(7))
        sku = SKU.objects.filter(external_id=external_id).first()
        sku_dict = {}
        if sku:
            properties = sku.properties
            for feature in properties_columns:
                properties[feature] = row[feature]
            for reserved_column in reserved_file_columns:
                if row[reserved_column]:
                    sku_dict[reserved_column_dict[reserved_column]] = row[reserved_column]

            sku_dict["properties"] = properties
            sku.__dict__.update(sku_dict)
            sku.save()
            logging.warning("SKU {} UPDATED".format(sku.external_id))
        else:
            for reserved_column in reserved_file_columns:
                sku_dict[reserved_column_dict[reserved_column]] = row[reserved_column]
            for feature in properties_columns:
                properties[feature] = row[feature]

            new_sku = SKU(
                external_id=external_id,
                name=sku_dict.get("name"),
                display_name=sku_dict.get("display_name"),
                ean=sku_dict.get("ean"),
                type=master_file_object.sku_type,
                group=master_file_object.sku_group,
                customer=master_file_object.customer,
                filter=sgtin_filter_value,
                params=params,
                properties=properties,
                created_by=master_file_object.created_by,
                modified_by=master_file_object.modified_by,
            )
            new_sku.save()
            if new_sku.reference == "000None":
                new_sku.reference = ""
                new_sku.save()
            logging.info("Created Successfully: {}".format(new_sku.external_id))


def validation_master(customer_id):
    master_object = get_object_or_404(MasterSKUFile, customer_id=customer_id)
    return master_object


def group_by_sales(type_reporters, reporter, created_min, created_max):
    move = None
    store_ids = Location.objects.filter(type__name='SHOWROOM').values_list('id')
    sub_locations_stores_ids = Location.objects.filter(parent__id__in=store_ids).values_list('id').values_list('id')
    move_ids = Move.objects.filter(reporter_source__in=type_reporters, type__in=['SALE_OUT', 'RETURN'],
                                   created__gt=created_min, created__lt=created_max).values_list('id')
    detail_sales_pos_device = MoveLine.objects.filter(move_id__in=move_ids, sub_location_id__in=sub_locations_stores_ids)\
        .values('sku_id', 'sub_location', 'move__reporter_source', 'move__type', 'move__customer_id', 'move__created',
                'move__id').annotate(lines_count=Count('id'), total_amount=Sum('amount')).order_by()
    if detail_sales_pos_device:
        for detail in detail_sales_pos_device:
            move = Move.objects.filter(pk=detail["move__id"]).first()
            if not move.params.get("verified_sale"):
                move_created_date = datetime.strftime(detail["move__created"], '%Y-%m-%d')
                type_sales_by_sku = SalesSKU.objects.filter(reporter_source=detail["move__reporter_source"], sale_date=datetime.strptime(move_created_date, '%Y-%m-%d'),
                                                            sku_id=detail["sku_id"], sub_location_id=detail["sub_location"],
                                                            type=detail["move__type"], customer_id=detail["move__customer_id"]).first()

                if type_sales_by_sku:
                    logging.info("Update total amount of sku {}".format(detail["sku_id"]))
                    type_sales_by_sku.total_amount = type_sales_by_sku.total_amount + detail["total_amount"]
                    type_sales_by_sku.last_update_date = datetime.now()
                    type_sales_by_sku.save(update_fields=['total_amount', 'last_update_date'])
                else:
                    SalesSKU.objects.create(
                        created_by_id=reporter,
                        modified_by_id=reporter,
                        sku_id=detail["sku_id"],
                        reporter_source=detail["move__reporter_source"],
                        sub_location_id=detail["sub_location"],
                        total_amount=detail["total_amount"],
                        total_lines=detail["lines_count"],
                        type=detail["move__type"],
                        customer_id=detail["move__customer_id"],
                        last_update_date=datetime.now(),
                        sale_date=datetime.strptime(move_created_date, '%Y-%m-%d')
                    )
    move.params = {"verified_sale": True}
    move.save()


from common.models import User
from inventory.models import Item, Location
from inventory.view_models import InventorySummaryToLocation

from django.db.models import Case, Count, IntegerField, Value, When, Sum


def type_position_by_items(items: list, type_position, reporter: User, entry_location=Location):
    results = []
    inventory_summary_by_sku = {}
    location_position = None
    if type_position == "PRODUCT":
        inventory_summary_by_sku.update({"type_position": type_position, "entry_location": entry_location})
        results.append(inventory_summary_by_sku)
        item_ids = Item.objects.filter(pk__in=items)
        item_sublocations = Item.objects.filter(current_location__properties__ware_house_id=entry_location.pk,
                                                current_location__type_id=14).values_list('current_location')
        for item in item_ids:
            inventory_summary = InventorySummaryToLocation.objects.filter(sku_id=item.sku_id,
                                current_location_id__in=item_sublocations).values('current_location_id', 'sku_id')\
                                .annotate(total_presents=Sum('total_present'))
            for inventory in inventory_summary:
                location_position = inventory["current_location_id"]
            inventory_summary_by_sku.update({"type_position": type_position, "items": [item], "inventory_summary": inventory_summary,
                                             "location_position": location_position, "item": item})
            results.append(inventory_summary_by_sku)

    return results
# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import csv
import logging
import operator
import os
from functools import reduce

import pandas as pd
from celery import shared_task
from django.core.files.base import File
from django.db.models import Q
from ..models import *
from inventory.business_logic import moves, audits
import inventory.utils as inventory_utils
from ..services import audit_img_lambda


class AuditResults:

    def __init__(self, audit_object):
        self.audit_object = audit_object
        self.audit_items_file = None
        self.audit_items = None
        self.expected_items = None
        self.found_items = None
        self.missing_items = None
        self.extra_items = None
        self.not_items = None
        self.omitted_items = None
        self.type_items = None
        self.values_calculated = False
        self.sku_dict = dict()
        self.sku_id_list = None
        self.result_items_list = None

    def _generate_found_files(self):
        audit_object = self.audit_object

        def create_items_file(items, name):
            df_items = pd.DataFrame(items, columns=['id'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-items-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_items.to_csv(path, encoding='utf-8')
            file = open(path, 'rb')
            audit_object.found_items_file.save(file_name, File(file))

        def create_summary_files(items, name):
            sorted_list_dict = inventory_utils.sort_items_by_sku_MHZ(items_ids=items)
            sorted_items = []
            for result in sorted_list_dict:
                sku = result['sku']
                items = result['items']
                type = result.get('type')
                new = [sku.id, sku.external_id, sku.name, type, len(items)]
                sorted_items.append(new)

            df_summary = pd.DataFrame(sorted_items, columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-summary-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_summary.to_csv(path, encoding='utf-8', sep=';')
            file = open(path, 'rb')
            audit_object.found_summary_file.save(file_name, File(file))

        if self.found_items:
            create_items_file(items=self.found_items, name='found')
            create_summary_files(items=self.found_items, name='found')

    def _generate_extra_files(self):
        audit_object = self.audit_object

        def create_items_file(items, name):
            df_items = pd.DataFrame(items, columns=['id'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-items-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_items.to_csv(path, encoding='utf-8')
            file = open(path, 'rb')
            audit_object.extra_items_file.save(file_name, File(file))

        def create_summary_files(items, name):
            sorted_list_dict = inventory_utils.sort_items_by_sku(items_ids=items)
            sorted_items = []
            for result in sorted_list_dict:
                new = []
                sku = result['sku']
                items = result['items']
                type = result.get("type")
                new = [sku.id, sku.external_id, sku.name, len(items)]
                sorted_items.append(new)

            df_summary = pd.DataFrame(sorted_items, columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-summary-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_summary.to_csv(path, encoding='utf-8', sep=';')
            file = open(path, 'rb')
            audit_object.extra_summary_file.save(file_name, File(file))

        if self.extra_items:
            create_items_file(items=self.extra_items, name='extra')
            create_summary_files(items=self.extra_items, name='extra')

    def get_audit_items_MHZ(self, result_items):
        audit = self.audit_object
        if audit.type == 'GENERAL':
            items_id_list = Item.objects.filter(pk__in=result_items).exclude(state='INACTIVE'). \
                values_list('id', flat=True)
            return items_id_list
        elif audit.type == 'PROPERTY':
            values = list(audit.properties.values())
            flattened_values = [item for sublist in values for item in sublist]
            properties_filter = reduce(
                operator.and_,
                [
                    reduce(
                        operator.or_,
                        [Q(sku__properties__contains={key: value}) for value in values]
                    )
                    for key, values in audit.properties.items()
                ]
            )
            items_id_list = list(
                Item.objects.filter(pk__in=result_items)
                .filter(properties_filter)
                .exclude(state="INACTIVE")
                .values_list("id", flat=True)
            )
            return items_id_list

    def get_audit_items(self):
        """
        Returns audit items from given audit as list
        """
        audit = self.audit_object
        audit.audit_items_file.seek(0)
        content = audit.audit_items_file.read()
        content = content.decode(encoding="ascii", errors="ignore")
        item_list = [line for line in content.split('\n')]
        item_list = set(map(str.strip, item_list))
        item_list = [str(line)[:24] for line in item_list if line]  # TODO change 24 digits no a variable
        list_items = Item.objects.filter(id__in=item_list, customer=audit.customer).exclude(state='INACTIVE'). \
            values_list('id', flat=True)
        return list(list_items)

    def get_expected_items_MHZ(self):
        audit = self.audit_object
        dataset = pd.read_csv(audit.expected_items_file)
        df = pd.DataFrame(dataset)
        item_id_list = df['id'].tolist()
        return item_id_list


    def get_expected_items(self):
        """
        Returns expected items from given audit as list
        """
        audit = self.audit_object
        location_ids = inventory_utils.get_location_tree(audit.location.id)
        if audit.type == 'GENERAL':
            items_id_list = (Item.objects.filter(current_location_id__in=location_ids, state="PRESENT",
                                                 customer=audit.customer, modified__date__lt=self.audit_object.cut_date)
                             .values_list('id', flat=True))
            return list(items_id_list)
        elif audit.type == 'PROPERTY':
            values = list(audit.properties.values())
            flattened_values = [item for sublist in values for item in sublist]
            properties_filter = reduce(
                operator.and_,
                [
                    reduce(
                        operator.or_,
                        [Q(sku__properties__contains={key: value}) for value in values]
                    )
                    for key, values in audit.properties.items()
                ]
            )
            items_id_list = list(
                Item.objects.filter(
                    state="PRESENT",
                    current_location_id__in=location_ids,
                    modified__lte=self.audit_object.cut_date,
                    customer_id=self.audit_object.customer_id,

                ).filter(properties_filter)
                .values_list("id", flat=True)
            )
            return list(items_id_list)
        else:
            items_id_list = Item.objects.filter(
                current_location_id__in=location_ids, state="PRESENT", customer=audit.customer,
                modified__date__lt=self.audit_object.cut_date).values_list('id', flat=True)
            return list(items_id_list)

    def calculate_not_items(self, audit_items, result_items):
        not_items = list(set(result_items) - (set(audit_items)))
        return not_items

    def calculate_omitted_items(self, result_items, audit_items):
        valid_omitted_items = Item.objects.filter(pk__in=result_items, state__in=['PACKED'])\
                                                    .exclude(state="UNAVAILABLE").values_list("id", flat=True)
        return list(valid_omitted_items)

    def calculate_type_items(self, result_items, audit_items):
        valid_type_items = Item.objects.filter(pk__in=result_items, type__name__in=['CORPORATE_SALES'])\
                                                    .exclude(state="UNAVAILABLE").values_list("id", flat=True)
        return list(valid_type_items)

    def calculate_found_items(self, expected_items, audit_items):
        """
        Returns found items from given expected items and counted items
        """
        found_items = set(expected_items).intersection(set(audit_items))
        return list(found_items)

    def calculate_missing_items(self, expected_items, audit_items):
        """
        Returns found items from given expected items and counted items
        """
        not_found_items = list(set(expected_items) - set(audit_items))
        return not_found_items

    def calculate_extra_items(self, audit_items, expected_items):
        """
        Returns extra items from given expected items and counted items
        """
        valid_audit_items = Item.objects.filter(pk__in=audit_items, modified__date__lt=self.audit_object.cut_date) \
            .exclude(state="PACKED").exclude(state="UNAVAILABLE").values_list("id", flat=True)
        extra_items = list(set(valid_audit_items) - set(expected_items))
        return extra_items

    def refresh_result_items_file(self):
        audit_img_lambda(audit=self.audit_object)
        self.audit_object.refresh_from_db()

    def get_result_items_list(self):
        audit = self.audit_object
        dataset = pd.read_csv(audit.result_items_file)
        df = pd.DataFrame(dataset)
        item_list = df['id'].tolist()
        return item_list

    def combined_csv_audit_files(self):
        file_url = None
        audit = self.audit_object
        all_files = AuditTrace.objects.filter(audit=audit).exclude(status='CREATED')
        list_files = []
        list_df = []
        for file in all_files:
            list_files.append(file.audit_items_file)
            file_url = file.audit_items_file.url
        import requests
        response = requests.get(file_url)

        if response.status_code == 200:
            from io import StringIO
            df = pd.read_csv(StringIO(response.text), delimiter="\t", header=None)
            list_df.append(df)
        else:
            print(f"Error al descargar {file_url}: {response.status_code}")
        combined_csv = pd.concat(list_df, ignore_index=True)
        combined_out = combined_csv.drop_duplicates()
        combined_out.columns = ['id']
        abs_path = os.path.abspath(os.path.dirname(__file__))
        file_name = "result_items_file_{}.csv".format(self.audit_object.id)
        path = os.path.join(abs_path, "../tmp/" + file_name)
        combined_out.to_csv(path, sep=',')
        file = open(path, 'rb')
        audit.result_items_file.save(file_name, File(file))
        return self.audit_object

    def get_result_items_list(self):
        audit = self.audit_object
        dataset = pd.read_csv(audit.result_items_file)
        df = pd.DataFrame(dataset)
        item_list = df['id'].tolist()
        return item_list


    def calculate_values_MHZ(self, override_values=True):

        if override_values is False and self.values_calculated:
            return self.audit_object

        self.refresh_result_items_file()
        self.audit_items_file = self.combined_csv_audit_files()
        self.result_items_list = self.get_result_items_list()
        self.audit_items = self.get_audit_items_MHZ(result_items=self.result_items_list)
        self.expected_items = self.get_expected_items()
        self.found_items = self.calculate_found_items(expected_items=self.expected_items,
                                                      audit_items=self.audit_items)
        self.missing_items = self.calculate_missing_items(expected_items=self.expected_items,
                                                          audit_items=self.audit_items)
        self.extra_items = self.calculate_extra_items(audit_items=self.audit_items, expected_items=self.expected_items)
        self.not_items = self.calculate_not_items(result_items=self.result_items_list, audit_items=self.audit_items)
        self.omitted_items = self.calculate_omitted_items(result_items=self.result_items_list,
                                                          audit_items=self.audit_items)
        self.type_items = self.calculate_type_items(result_items=self.result_items_list,
                                                          audit_items=self.audit_items)
        self.values_calculated = True
        return self.audit_object



    def calculate_values(self, override_values=True):
        """
        Calculates audit values, such as expected items, found items, not found
        """
        if override_values is False and self.values_calculated:
            return self.audit_object

        self.audit_items = self.get_audit_items()
        self.expected_items = self.get_expected_items()
        self.found_items = self.calculate_found_items(expected_items=self.expected_items,
                                                      audit_items=self.audit_items)
        self.missing_items = self.calculate_missing_items(expected_items=self.expected_items,
                                                          audit_items=self.audit_items)
        self.extra_items = self.calculate_extra_items(expected_items=self.expected_items,
                                                      audit_items=self.audit_items)
        self.type_items = self.calculate_type_items(result_items=self.audit_items,
                                                    audit_items=self.audit_items)
        self.values_calculated = True

        # generate sku_dict
        # TODO: Refactor this code ASAP

        sku_dict = dict()
        # 1. Sort Items:
        sorted_audit_items = inventory_utils.sort_items_by_sku(self.audit_items, self.audit_object.customer.pk)
        sorted_expected_items = inventory_utils.sort_items_by_sku(self.expected_items, self.audit_object.customer.pk)
        sorted_found_items = inventory_utils.sort_items_by_sku(self.found_items, self.audit_object.customer.pk)
        sorted_missing_items = inventory_utils.sort_items_by_sku(self.missing_items, self.audit_object.customer.pk)
        sorted_extra_items = inventory_utils.sort_items_by_sku(self.extra_items, self.audit_object.customer.pk)

        # 2. Generate dict
        # Each Dict is like {"sku_id":{[audit_items],[expected_items],[found_items],[missing_items],[extra_items]}

        for result in sorted_audit_items:
            sku = result['sku']
            items = result['items'] or []
            sku_dict[sku.pk] = {}
            sku_dict[sku.pk]['audit_items'] = items

        for result in sorted_expected_items:
            sku = result['sku']
            items = result['items'] or []
            if sku.pk in sku_dict:
                sku_dict[sku.pk]['expected_items'] = items
            else:
                sku_dict[sku.pk] = {}
                sku_dict[sku.pk]['expected_items'] = items

        for result in sorted_found_items:
            sku = result['sku']
            items = result['items'] or []
            if sku.pk in sku_dict:
                sku_dict[sku.pk]['found_items'] = items
            else:
                sku_dict[sku.pk] = {}
                sku_dict[sku.pk]['found_items'] = items

        for result in sorted_missing_items:
            sku = result['sku']
            items = result['items'] or []
            if sku.pk in sku_dict:
                sku_dict[sku.pk]['missing_items'] = items
            else:
                sku_dict[sku.pk] = {}
                sku_dict[sku.pk]['missing_items'] = items

        for result in sorted_extra_items:
            sku = result['sku']
            items = result['items'] or []
            if sku.pk in sku_dict:
                sku_dict[sku.pk]['extra_items'] = items
            else:
                sku_dict[sku.pk] = {}
                sku_dict[sku.pk]['extra_items'] = items

        self.sku_dict = sku_dict
        return self.audit_object


    def generate_values_MHZ(self, override_values=True):
        """
        Generates and assign  audit values, such as expected items, found items, not found
        """
        self.calculate_values_MHZ(override_values=override_values)
        if override_values is False:
            return self.audit_object

        self.audit_object.total_expected_items = len(self.expected_items)
        self.audit_object.total_audit_items = len(self.audit_items)
        self.audit_object.total_found_items = len(self.found_items)
        self.audit_object.total_missing_items = len(self.missing_items)
        self.audit_object.total_extra_items = len(self.extra_items)
        self.audit_object.total_audit_not_items = len(self.not_items)
        self.audit_object.total_omitted_items = len(self.omitted_items)
        self.audit_object.type_items = len(self.type_items)
        return self.audit_object

    def generate_values(self, override_values=True):
        """
        Generates and assign  audit values, such as expected items, found items, not found
        """
        self.calculate_values(override_values=override_values)
        if override_values is False:
            return self.audit_object

        self.audit_object.total_expected_items = len(self.expected_items)
        self.audit_object.total_audit_items = len(self.audit_items)
        self.audit_object.total_found_items = len(self.found_items)
        self.audit_object.total_missing_items = len(self.missing_items)
        self.audit_object.total_extra_items = len(self.extra_items)
        self.audit_object.type_items = len(self.type_items)
        self.audit_object.save()

        return self.audit_object

    def _generate_summary_lines(self):
        """
        Generates Audit summary Lines
        """
        if not self.sku_dict:
            self.generate_values()
        AuditLine.objects.filter(audit=self.audit_object).delete()
        sku_dict = self.sku_dict
        # Each Dict is like {"sku_id":[[audit_items],[expected_items],[found_items],[missing_items],[extra_items]]
        lines = []
        for sku_id, items in sku_dict.items():
            lines.append(
                AuditLine(
                    audit=self.audit_object,
                    sku_id=sku_id,
                    total_audit_items=len(items.get('audit_items', [])),
                    total_expected_items=len(items.get('expected_items', [])),
                    total_found_items=len(items.get('found_items', [])),
                    total_missing_items=len(items.get('missing_items', [])),
                    total_extra_items=len(items.get('extra_items', [])),
                )
            )
        AuditLine.objects.bulk_create(lines)

    def _generate_summary_file(self):
        """
        Generates Audit summary File
        """
        audit = self.audit_object
        audit_items = self.get_audit_items()
        # Create lines for audit items lines (counted)
        sorted_audit_items = inventory_utils.sort_items_by_sku(items_ids=audit_items, customer_id=audit.customer.pk)
        abs_path = os.path.abspath(os.path.dirname(__file__))
        file_name = "audit_{}_summary_results.csv".format(audit.pk)
        path = os.path.join(abs_path, "../tmp/" + file_name)
        audit_items_file_tmp = open(path, 'w')
        writer = csv.writer(audit_items_file_tmp)

        for result in sorted_audit_items:
            sku = result['sku']
            items = result['items']

            writer.writerow([
                sku.pk,
                sku.external_id.encode('utf_8'),
                sku.name.encode('utf_8'),
                len(items)]
            )

        audit_items_file_tmp.close()
        summary_file = open(path, "rb")
        file_name = "audit_{}_summary_results.csv".format(audit.id)
        audit.summary_results_file.save(file_name, File(summary_file))
        os.remove(path)

    def _generate_missing_file(self):
        """
        Generates Audit missing File
        """
        audit = self.audit_object
        missing_items = self.missing_items
        # Create lines for audit items lines (counted)
        sorted_missing_items = inventory_utils.sort_items_by_sku(items_ids=missing_items, customer_id=audit.customer.pk)
        abs_path = os.path.abspath(os.path.dirname(__file__))
        file_name = "audit_{}_missing_results.csv".format(audit.pk)
        path = os.path.join(abs_path, "../tmp/" + file_name)
        missing_items_file_tmp = open(path, 'w')
        writer = csv.writer(missing_items_file_tmp)

        for result in sorted_missing_items:
            sku = result['sku']
            items = result['items']

            writer.writerow([
                sku.pk,
                sku.external_id.encode('utf-8'),
                sku.name.encode('utf-8'),
                " ".join(str(item) for item in items),
                len(items)]
            )

        missing_items_file_tmp.close()
        summary_file = open(path, "rb")
        file_name = "audit_{}_missing_results.csv".format(audit.id)
        audit.missing_items_file.save(file_name, File(summary_file))
        os.remove(path)

    def _generate_extra_files(self):
        audit_object = self.audit_object

        def create_items_file(items, name):
            df_items = pd.DataFrame(items, columns=['id'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-items-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_items.to_csv(path, encoding='utf-8')
            file = open(path, 'rb')
            audit_object.extra_items_file.save(file_name, File(file))

        def create_summary_files(items, name):
            sorted_list_dict = inventory_utils.sort_items_by_sku_MHZ(items_ids=items)
            sorted_items = []
            for result in sorted_list_dict:
                new = []
                sku = result['sku']
                items = result['items']
                type = result["type"]
                new = [sku.id, sku.external_id, sku.name, type, len(items)]
                sorted_items.append(new)

            df_summary = pd.DataFrame(sorted_items, columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-summary-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_summary.to_csv(path, encoding='utf-8', sep=';')
            file = open(path, 'rb')
            audit_object.extra_summary_file.save(file_name, File(file))

        if self.extra_items:
            create_items_file(items=self.extra_items, name='extra')
            create_summary_files(items=self.extra_items, name='extra')

    def _generate_extras_file(self):
        """
        Generates Audit missing File
        """
        audit = self.audit_object
        extra_items = self.extra_items
        # Create lines for audit items lines (counted)
        sorted_missing_items = inventory_utils.sort_items_by_sku(items_ids=extra_items, customer_id=audit.customer.pk)
        abs_path = os.path.abspath(os.path.dirname(__file__))
        file_name = "audit_{}_extras_results.csv".format(audit.pk)
        path = os.path.join(abs_path, "../tmp/" + file_name)
        extra_items_file_tmp = open(path, 'w')
        writer = csv.writer(extra_items_file_tmp)

        for result in sorted_missing_items:
            sku = result['sku']
            items = result['items']

            writer.writerow([
                sku.pk,
                sku.external_id.encode('utf-8'),
                sku.name.encode('utf-8'),
                " ".join(str(item) for item in items),
                len(items)]
            )

        extra_items_file_tmp.close()
        summary_file = open(path, "rb")
        file_name = "audit_{}_extra_results.csv".format(audit.id)
        audit.extra_items_file.save(file_name, File(summary_file))
        os.remove(path)

    def _generate_missing_files(self):
        audit_object = self.audit_object

        def create_items_file(items, name):
            df_items = pd.DataFrame(items, columns=['id'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-items-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_items.to_csv(path, encoding='utf-8')
            file = open(path, 'rb')
            audit_object.missing_items_file.save(file_name, File(file))

        def create_summary_files(items, name):
            sorted_list_dict = inventory_utils.sort_items_by_sku_MHZ(items_ids=items)
            sorted_items = []
            for result in sorted_list_dict:
                new = []
                sku = result['sku']
                items = result['items']
                type = result["type"]
                new = [sku.id, sku.external_id, sku.name, type, len(items)]
                sorted_items.append(new)

            df_summary = pd.DataFrame(sorted_items, columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-summary-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_summary.to_csv(path, encoding='utf-8', sep=';')
            file = open(path, 'rb')
            audit_object.missing_summary_file.save(file_name, File(file))

        if self.missing_items:
            create_items_file(items=self.missing_items, name='missing')
            create_summary_files(items=self.missing_items, name='missing')
        elif len(self.missing_items) < 1:
            create_items_file(items=self.missing_items, name='missing')
            create_summary_files(items=self.missing_items, name='missing')

    def _generate_omitted_files(self):
        audit_object = self.audit_object

        def create_items_file(items, name):
            df_items = pd.DataFrame(items, columns=['id'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-items-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_items.to_csv(path, encoding='utf-8')
            file = open(path, 'rb')
            audit_object.omitted_items_file.save(file_name, File(file))

    def _generate_type_item_files(self):
        audit_object = self.audit_object
        def create_items_file(items, name):
            sorted_list_dict = inventory_utils.sort_type_items_by_sku_MHZ(items_ids=items)
            sorted_items = []
            for item in items:
                for result in sorted_list_dict:
                    new = []
                    type = result['type']
                    items = result['items']
                    new = [item, type.name, ]
                    sorted_items.append(new)
            df_items = pd.DataFrame(sorted_items, columns=['id', 'type_name'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-items-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_items.to_csv(path, encoding='utf-8')
            file = open(path, 'rb')
            audit_object.items_type_file.save(file_name, File(file))

        def create_summary_files(items, name):
            sorted_list_dict = inventory_utils.sort_items_by_sku_MHZ(items_ids=items)
            sorted_items = []
            for result in sorted_list_dict:
                new = []
                sku = result['sku']
                items = result['items']
                new = [sku.id, sku.external_id, sku.name, len(items)]
                sorted_items.append(new)

            df_summary = pd.DataFrame(sorted_items, columns=['sku_id', 'sku_external_id', 'sku_name', 'sku_amount'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-summary-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_summary.to_csv(path, encoding='utf-8', sep=';')
            file = open(path, 'rb')
            audit_object.items_type_summary_file.save(file_name, File(file))

        if self.type_items:
            create_items_file(items=self.type_items, name='type_items')
            create_summary_files(items=self.type_items, name='type_items')

    def _generate_not_files(self):
        audit_object = self.audit_object

        def create_items_file(items, name):
            df_items = pd.DataFrame(items, columns=['id'])
            abs_path = os.path.abspath(os.path.dirname(__file__))
            file_name = "{}-items-{}.csv".format(name, audit_object.id)
            path = os.path.join(abs_path, "../tmp/" + file_name)
            df_items.to_csv(path, encoding='utf-8')
            file = open(path, 'rb')
            audit_object.not_items_file.save(file_name, File(file))

        if self.not_items:
            create_items_file(items=self.not_items, name='not_items')

    def _generate_file_summary(self):
        audit = self.audit_object
        if audit.found_summary_file:
            dataset_found = pd.read_csv(audit.found_summary_file, sep=';')
            dataset_found_pd = pd.DataFrame(dataset_found,
                                            columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])
        else:
            dataset_found_pd = pd.DataFrame(columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])
        if audit.extra_summary_file:
            dataset_extra = pd.read_csv(audit.extra_summary_file, sep=';')
            dataset_extra_pd = pd.DataFrame(dataset_extra,
                                            columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])
        else:
            dataset_extra_pd = pd.DataFrame(columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])

        if audit.omitted_summary_file:
            dataset_omitted = pd.read_csv(audit.omitted_summary_file, sep=';')
            dataset_omitted_pd = pd.DataFrame(dataset_omitted,
                                              columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])
        else:
            dataset_omitted_pd = pd.DataFrame(columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount'])

        sub_total = pd.merge(dataset_extra_pd, dataset_found_pd, on=['sku_id', 'sku_external_id', 'type', 'sku_name'],
                             how='outer')
        total = pd.merge(sub_total, dataset_omitted_pd, on=['sku_id', 'sku_external_id', 'sku_name', 'type'], how='outer')
        result = pd.DataFrame(total, columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount_x', 'sku_amount_y',
                                              'sku_amount'])
        result['total_sum'] = result[["sku_amount_x", "sku_amount_y", 'sku_amount']].sum(axis=1)

        new_data_frame = pd.DataFrame(result,
                                      columns=['sku_id', 'sku_external_id', 'sku_name', 'type', 'sku_amount_x', 'sku_amount_y',
                                               'sku_amount', 'total_sum'])

        new_data_frame.rename(columns={'sku_amount_x': 'sku_amount_extra', 'sku_amount_y': 'sku_amount_found',
                                       'sku_amount': 'sku_amount_omitted'},
                              inplace=True)
        abs_path = os.path.abspath(os.path.dirname(__file__))
        file_name = "result_summary_file_{}.csv".format(audit.id)
        path = os.path.join(abs_path, "../tmp/" + file_name)
        new_data_frame.to_csv(path, encoding='utf-8', sep=';')
        file = open(path, 'rb')
        audit.audit_summary_file.save(file_name, File(file))
        return self.audit_object


    def generate_summary_MHZ(self):
        self._generate_found_files()
        self._generate_extra_files()
        self._generate_missing_files()
        self._generate_omitted_files()
        self._generate_type_item_files()
        self._generate_not_files()
        self._generate_file_summary()

    def generate_summary(self):
        self._generate_summary_file()
        self._generate_summary_lines()
        self._generate_missing_file()
        self._generate_extras_file()
        self._generate_type_item_files()


def adjust_audit(audit_object, action_missing, action_extra, reporter_object):
    logging.info("EXECUTE ADJUST AUDIT_ID: {}, ACTION FOR ITEMS MISSING: {},ACTION FOR ITEMS EXTRA: {} AND REPORTER_"
                 "ID: {}".format(audit_object.pk, action_missing, action_extra, reporter_object.pk))

    audit_results = AuditResults(audit_object)
    audit_results.calculate_values()
    missing_items = audit_results.missing_items
    extra_items = audit_results.extra_items

    def process_load(items):
        items_objects = Item.objects.filter(pk__in=items).exclude(state='INACTIVE')
        trace = []

        move_ids_to_update = []
        for item in items_objects:
            trace_dict = {
                "created_by": reporter_object,
                "modified_by": reporter_object,
                "item": item,
                "location": item.current_location,
                "reporter": reporter_object,
                "reporter_source": "SYSTEM",
                "action": "TAKEOUT",
                "customer": item.customer
            }
            if item.state == "PRESENT" and item.current_location != audit_object.location:
                in_trace = Trace(**trace_dict)
                trace.append(in_trace)
            elif item.state == "IN_TRANSFER":
                transfer_line = MoveLine.objects.filter(
                    expected_items__items__contains=item.id, move__type="TRANSFER") \
                    .order_by('-id').first()
                transfer_line.add_received_items(items=[item.id], reporter=reporter_object, calculate_progress=True)
                move_ids_to_update.append(transfer_line.move_id)
            item.current_location = audit_object.location
            item.state = "PRESENT"
            item.modified_by = reporter_object
            item.save()
            trace_dict["location"] = audit_object.location
            trace_dict["action"] = "AUDIT_ENTRY"
            trace_dict["reporter_source"] = "WEB_APP"
            in_trace = Trace(**trace_dict)
            trace.append(in_trace)
        moves_to_update = Move.objects.filter(id__in=move_ids_to_update)
        for move in moves_to_update:
            moves.update_move_progress(move=move, reporter=reporter_object)
        Trace.objects.bulk_create(trace)

    def process_download(items):
        items_objects = Item.objects.filter(pk__in=items).exclude(state='INACTIVE')
        trace = []

        for item in items_objects:
            item.state = "NOT_PRESENT"
            item.modified_by = reporter_object
            item.save()
            in_trace = Trace(
                created_by=reporter_object,
                modified_by=reporter_object,
                item=item,
                location=audit_object.location,
                reporter=reporter_object,
                reporter_source="WEB_APP",
                action="TAKEOUT",
                customer=item.sku.customer,
            )
            trace.append(in_trace)
        Trace.objects.bulk_create(trace)

    if action_missing == Audit.DOWNLOAD:
        process_download(missing_items)
    else:
        logging.error("{} Is not a valid action for adjust missing items ".format(action_missing))

    if action_extra == Audit.LOAD:
        process_load(extra_items)
    else:
        logging.error("{} Is not a valid action for adjust extra items ".format(action_extra))

    audit_object.status = "ADJUSTED"
    audit_object.save()


def generate_file_audit_adjust(audit, action_missing, action_extra):
    logging.info(
        "EXECUTE ADJUST AUDIT_ID: {}, ACTION FOR ITEMS MISSING: {},ACTION FOR ITEMS EXTRA: {} ".format(audit.pk,
                                                                                                       action_missing,
                                                                                                       action_extra))

    audit_results = AuditResults(audit)
    audit_results.calculate_values()
    missing_items = audit_results.missing_items
    extra_items = audit_results.extra_items

    item_extra = Item.objects.filter(pk__in=extra_items).exclude(state='INACTIVE')
    item_missing = Item.objects.filter(pk__in=missing_items).exclude(state='INACTIVE')

    abs_path = os.path.abspath(os.path.dirname(__file__))
    file_name = "audit_{}_adjust_items_file.csv".format(audit.pk)
    path = os.path.join(abs_path, "../tmp/" + file_name)
    audit_items_file_tmp = open(path, 'w')
    writer = csv.writer(audit_items_file_tmp)

    for result in item_missing:
        item = result.pk
        sku = result.sku
        logging.info("Lines of action missing for generate file of: item:{}, sku:{}, sku_external_id {}".format(
            item, sku, sku.external_id))
        writer.writerow([
            item,
            sku,
            sku.external_id,
            action_missing.encode('utf-8'),
        ]
        )
    for result in item_extra:
        item = result.pk
        sku = result.sku
        logging.info("Lines of action extra for generate file of: item:{}, sku:{}, sku_external_id {}".format(
            item, sku, sku.external_id))
        writer.writerow([
            item,
            sku,
            sku.external_id,
            action_extra.encode('utf-8'),
        ])

    audit_items_file_tmp.close()
    summary_file = open(path, "rb")
    file_name = "audit_{}_adjust_items_file.csv".format(audit.pk)
    audit.adjustment_items_file.save(file_name, File(summary_file))
    os.remove(path)


def count_items_file(audit_object):
    audit_object.audit_items_file.seek(0)
    content = audit_object.audit_items_file.read()
    content = content.decode("utf-8")
    item_list = [line for line in content.split('\n')]
    item_list = set(map(str.strip, item_list))
    items = Item.objects.filter(pk__in=list(item_list)).exclude(state='INACTIVE')
    return items


def count_trace_items_file(audit_trace_object):
    dataset = pd.read_csv(audit_trace_object.audit_items_file, names=['id'])
    df = pd.DataFrame(dataset)
    item_list = df['id'].tolist()
    items = Item.objects.filter(pk__in=list(item_list)).exclude(state='INACTIVE')
    return items


@shared_task(bind=True)
def count_items_file_MHZ(self, audit_trace_id):
    audit_trace_object = AuditTrace.objects.get(pk=audit_trace_id)
    items = audits.count_trace_items_file(audit_trace_object=audit_trace_object)
    audit_trace_object.total_audit_items = len(items)
    audit_trace_object.save()


def report_audit_trace(audit_object, user_object):
    total_audit_items = count_items_file(audit_object)
    trace_audit = AuditTrace.objects.create(
        audit=audit_object,
        audit_items_file=audit_object.audit_items_file,
        total_audit_items=len(total_audit_items),
        created_by=user_object,
        modified_by=user_object
    )
    trace_audit.save()
    return trace_audit


def adjustment_audit(audit_object, action_missing, action_extra, reporter_object):
    logging.info("EXECUTE ADJUST AUDIT_ID: {}, ACTION FOR ITEMS MISSING: {},ACTION FOR ITEMS EXTRA: {} AND REPORTER_"
                 "ID: {}".format(audit_object.pk, action_missing, action_extra, reporter_object.pk))

    audit_results = AuditResults(audit_object)
    if audit_object.type:
        audit_results.calculate_values_MHZ()
    else:
        audit_results.calculate_values()
    missing_items = audit_results.missing_items
    extra_items = audit_results.extra_items

    def process_load(items):
        items_to_update = Item.objects.filter(id__in=items)
        trace = []
        move_ids_to_update = []
        for item in items_to_update:
            trace_dict = {
                "created_by": reporter_object,
                "modified_by": reporter_object,
                "item": item,
                "location": item.current_location,
                "reporter": reporter_object,
                "reporter_source": "SYSTEM",
                "action": "TAKEOUT"
            }
            if item.state == "PRESENT" and item.current_location != audit_object.location:
                in_trace = Trace(**trace_dict)
                trace.append(in_trace)
            elif item.state == "IN_TRANSFER":
                transfer_line = MoveLine.objects.filter(
                    expected_items__items__contains=item.id, move__type="TRANSFER")\
                    .order_by('-id').first()
                if transfer_line:
                    transfer_line.add_received_items(
                        items=[item.id], reporter=reporter_object, calculate_progress=True)
                    move_ids_to_update.append(transfer_line.move_id)
                else:
                    logging.warning("item {} with status IN_TRANSFER not found on any transfer line".format(item.id))
            item.current_location = audit_object.location

            item.state = "PRESENT"
            item.modified_by = reporter_object
            item.save()
            trace_dict["location"] = audit_object.location
            trace_dict["action"] = "AUDIT_ENTRY"
            trace_dict["reporter_source"] = "WEB_APP"
            in_trace = Trace(**trace_dict)
            trace.append(in_trace)
        moves_to_update = Move.objects.filter(id__in=move_ids_to_update)
        for move in moves_to_update:
            moves.update_move_progress(move=move, reporter=reporter_object)
        Trace.objects.bulk_create(trace)

    def process_unload(items):
        items_to_update = Item.objects.filter(id__in=items)
        trace = []
        for item in items_to_update:
            item.state = "NOT_PRESENT"
            item.modified_by = reporter_object
            item.save()
            in_trace = Trace(
                created_by=reporter_object,
                modified_by=reporter_object,
                item=item,
                location=audit_object.location,
                reporter=reporter_object,
                reporter_source="WEB_APP",
                action="TAKEOUT",
            )
            trace.append(in_trace)
        Trace.objects.bulk_create(trace)

    if action_missing == Audit.DOWNLOAD:
        process_unload(missing_items)
    else:
        logging.error("{} Is not a valid action for adjust missing items ".format(action_missing))

    if action_extra == Audit.LOAD:
        process_load(extra_items)
    else:
        logging.error("{} Is not a valid action for adjust extra items ".format(action_extra))
    return audit_object

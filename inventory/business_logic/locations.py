# -*- coding: utf-8 -*-

import csv

from django.http import JsonResponse
from django.shortcuts import get_object_or_404
import pandas as pd
import logging


from requests import Response
from rest_framework import status

from common.models import User
from core_config.models import UserConfig
from ..models import Location, MasterLOCATIONFile


def validate_reserved_columns_master_file(master_file_object: MasterLOCATIONFile):
    reserved_columns = MasterLOCATIONFile.MASTER_FILE_PARAMS["reserved_columns"]
    required_columns = []
    for column_params in reserved_columns.values():
        if column_params.get("required") == "true":
            required_columns.append(column_params["external_column_name"])

    master_file = master_file_object.master_file_location
    dialect = csv.Sniffer().sniff(str(master_file.read(1024)))
    master_file.seek(0)
    df_master_file = pd.read_csv(master_file, dtype='str', sep=dialect.delimiter)
    columns = list(df_master_file.columns)
    for required_column in required_columns:
        if required_column not in columns:
            error_message = "Invalid csv format, required columns are {}".format(required_columns)
            raise AttributeError(error_message)


def create_product_master(master_file_object: MasterLOCATIONFile):
    reserved_columns = MasterLOCATIONFile.MASTER_FILE_PARAMS["reserved_columns"]
    reserved_column_dict = {}  # {"-- file column name --": "-- kong column name --}
    for column_name, column_params in reserved_columns.items():
        reserved_column_dict[column_params["external_column_name"]] = column_name

    # TODO: Option for different codification
    sgtin_filter_value = 1
    data = pd.read_csv(master_file_object.master_file_location, dtype='str')
    data.fillna('', inplace=True)
    file_keys_list = list(data.columns)
    properties = {}

    # TODO: Validate params for shopify.
    params = {}
    reserved_column_list = reserved_column_dict.keys()
    properties_columns = set(file_keys_list) - set(reserved_column_list)
    reserved_file_columns = set(file_keys_list) & set(reserved_column_list)

    for index, row in data.iterrows():
        external_id = row[reserved_columns['external_id']["external_column_name"]]
        location = Location.objects.filter(external_id=external_id).first()
        location_dict = {}
        if location:
            properties = location.properties
            for feature in properties_columns:
                properties[feature] = row[feature]
            for reserved_column in reserved_file_columns:
                if row[reserved_column]:
                    location_dict[reserved_column_dict[reserved_column]] = row[reserved_column]

            location_dict["properties"] = properties
            location.__dict__.update(location_dict)
            location.save()
            logging.warning("SKU {} UPDATED".format(location.external_id))
        else:
            for reserved_column in reserved_file_columns:
                location_dict[reserved_column_dict[reserved_column]] = row[reserved_column]
            for feature in properties_columns:
                properties[feature] = row[feature]

            new_location = Location(
                name=location_dict.get("name"),
                display_name=location_dict.get("display_name"),
                external_id=external_id,
                type=master_file_object.location_type,
                parent=master_file_object.parent,
                params=params,
                created_by=master_file_object.created_by,
                modified_by=master_file_object.modified_by,
                created=master_file_object.created,
                modified=master_file_object.modified,
            )
            new_location.save()
            new_location.customer.add(2)
            logging.info("Created Successfully: {}".format(new_location.external_id))


def validation_master(customer_id):
    master_object = get_object_or_404(MasterLOCATIONFile, customer_id=customer_id)
    return master_object


def validate_locations(source_id, destination_id, user: User):
    locations = [source_id, destination_id]
    locations_updates = Location.objects.filter(external_id__in=locations).values('external_id', 'pk')
    location_source_id = None
    location_destination_id = None
    for location in locations_updates:
        if location["external_id"] == str(source_id):
            location_source_id = location["pk"]
        else:
            location_destination_id = location["pk"]

    customer_properties = UserConfig.objects.get(user_id=user.pk).customer.properties.get("integration_users")
    if customer_properties:
        user_ids = customer_properties.get("user_ids")
        if locations_updates:
            if str(user.pk) in user_ids:
                return JsonResponse({"current_location_source": location_source_id,
                                     "current_location_destination": location_destination_id}, status=status.HTTP_200_OK)
            else:
                JsonResponse({"message": "The user is not authorized to perform this operation"},
                             status=status.HTTP_401_UNAUTHORIZED)
        return JsonResponse({"message": "The location with this id does not exist in the system"},
                                status=status.HTTP_406_NOT_ACCEPTABLE)
    else:
        pass

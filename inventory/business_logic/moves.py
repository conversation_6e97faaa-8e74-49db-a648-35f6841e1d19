# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import csv
import json
import logging
import os
from collections import defaultdict
from copy import deepcopy
from datetime import datetime

import pandas as pd
from django.core.files.base import File
from django.db.models.expressions import RawSQL
from django.db.models import F, Sum, Count
from common.models import User
import inventory.utils as inventory_utils
from core_config.models import UserConfig
from ..models import MoveLine, Item, Trace, Move, SKU, Location

# Creation logic
from ..services import replicate_move_create


def items_by_customer(items_ids, customer):
    items_ids = Item.objects.filter(pk__in=items_ids, sku__customer=customer).exclude(state='INACTIVE').values('id')
    return items_ids


def arrange_items_by_sku(items_ids, include_states=()):
    item_objects = Item.objects.filter(pk__in=items_ids).exclude(state='INACTIVE')
    if include_states:
        item_objects = item_objects.filter(state__in=include_states)
    skus_dict = defaultdict(list)
    for item in item_objects:
        skus_dict[item.sku.pk].append(item)
    return skus_dict


def arrange_items_by_location_sku(items_ids, default_location_id: int = 0, force_default_location=True):
    item_objects = Item.objects.filter(pk__in=items_ids).exclude(state='INACTIVE')
    skus_dict = {}  # {-- current location id --: {-- sku id --: [-- items --]}}
    for item in item_objects:
        if force_default_location:
            current_location_id = default_location_id
        else:
            current_location_id = item.current_location_id
        skus_dict.setdefault(current_location_id, {}).setdefault(item.sku_id, []).append(item)
    return skus_dict


def create_lines_from_items(move, items, include_states=(), modified_as_timestamp=False):
    new_lines = []
    sub_location_id = None
    force_default_location = True
    if move.type in ("SIMPLE_ENTRY", "TRANSFER"):
        sub_location_id = move.destination_id
    elif move.type in ("SIMPLE_OUTPUT", "TAKEOUT", "SALE_OUT"):
        sub_location_id = move.source_id
    elif move.type == "RETURN":
        sub_location_id = move.destination_id
        force_default_location = False

    location_skus_dict = arrange_items_by_location_sku(items_ids=items, default_location_id=sub_location_id,
                                                       force_default_location=force_default_location)

    for location_id, skus_dict in location_skus_dict.items():
        for sku_id, item_list in skus_dict.items():
            if modified_as_timestamp:
                timestamp_item_list = [([item.pk], item.modified) for item in item_list]
            else:
                timestamp_item_list = [([item.pk for item in item_list], datetime.now())]
            for timestamp_item_line in timestamp_item_list:
                new_lines.append(
                    MoveLine(
                        move=move,
                        sku_id=sku_id,
                        amount=len(timestamp_item_line[0]),
                        status="COMPLETED",
                        items={"items": timestamp_item_line[0]},
                        #received_items={"items": timestamp_item_line[0]}, #Todo Its neccesary for adidas?
                        created_by=move.created_by,
                        modified_by=move.modified_by,
                        sub_location_id=location_id,
                        timestamp=datetime.timestamp(timestamp_item_line[1])
                    )
                )
    MoveLine.objects.bulk_create(new_lines)
    return new_lines


def move_items(reporter: User, items=[], destination=None):
    """
    Moves items to given location
    """
    assert len(items), 'EPC list cannot be  empty'
    assert destination, 'Destination cannot be None'
    items = Item.objects.exclude(current_location=destination, state='PRESENT')\
        .filter(pk__in=items).exclude(state='INACTIVE')
    items_to_update = [item.pk for item in items]
    logging.info("Moving  %s items to %s", len(items), destination)
    items.update(current_location=destination, state='PRESENT', modified=datetime.now(),
                 modified_by=reporter)
    # Return updated items ids
    return items_to_update


def move_items_return(reporter: User, related_model_instance=None, items=[], destination=None):
    """
    Moves items to given location
    """
    assert len(items), 'EPC list cannot be  empty'
    assert destination, 'Destination cannot be None'
    items = Item.objects.exclude(current_location=destination, state='PRESENT')\
        .filter(pk__in=items).exclude(state='INACTIVE')

    properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) - 'model' - 'model_pk' - 'model_external_id'"
    properties_query_params = []
    if related_model_instance:
        model_name = type(related_model_instance).__name__
        model_pk = related_model_instance.pk
        model_external_id = related_model_instance.external_id
        item_properties_str = json.dumps({
            "properties": {
                "model": model_name,
                "model_pk": model_pk,
                "model_external_id": model_external_id
                }

        })
        properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) || %s::jsonb"
        properties_query_params = [item_properties_str]

    item_ids = [item.pk for item in items]
    logging.info("Changing  %s items to %s state", len(item_ids),'PRESENT')
    items.update(current_location=destination, state='PRESENT', modified=datetime.now().astimezone(), modified_by=reporter,
                 properties=RawSQL(properties_query_str, properties_query_params))
    # Return updated items ids
    return Item.objects.filter(pk__in=item_ids)


def update_items_state(reporter: User, items=[], state=None, exclude_states=[], include_states=[],
                       related_model_instance=None, return_objects=False):
    """
    Updates Items to given state
    """
    # if you provide exclude_states, items in those states wont be updated.
    # if you provide include_states, ONLY items in those states will be updated.
    assert len(items), 'Item list cannot be  empty'
    assert state, 'New State cannot be None'
    assert not (bool(exclude_states) and bool(include_states)), \
        'You should specify either included, excluded states or None, but not both'

    properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) - 'model' - 'model_pk' - 'model_external_id'"
    properties_query_params = []
    if related_model_instance:
        model_name = type(related_model_instance).__name__
        customer__name = UserConfig.objects.filter(user=reporter).first().customer.name
        if customer__name == "LATAM":
            model_name = 'STOCK_PACKAGE'
        if model_name == 'Package':  # latam
            model_name = 'Packing'
        model_pk = related_model_instance.pk
        model_external_id = related_model_instance.external_id
        item_properties_str = json.dumps({
            "model": model_name,
            "model_pk": model_pk,
            "model_external_id": model_external_id
        })
        properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) || %s::jsonb"
        properties_query_params = [item_properties_str]
        items = Item.objects.filter(pk__in=items)

    items = Item.objects.filter(pk__in=items).exclude(state='INACTIVE')

    if exclude_states:
        items = items.exclude(state__in=exclude_states)
    if include_states:
        items = items.filter(state__in=include_states)

    item_ids = [item.pk for item in items]
    logging.info("Changing  %s items to %s state", len(item_ids), state)
    items.update(state=state, modified=datetime.now().astimezone(), modified_by=reporter,
                 properties=RawSQL(properties_query_str, properties_query_params))
    if return_objects:
        return Item.objects.filter(pk__in=item_ids)
    else:
        return item_ids



def update_items_active_state(reporter: User, items=[], state=None, exclude_states=[], include_states=[],
                       related_model_instance=None, return_objects=False):
    """
    Updates Items to given state
    """
    # if you provide exclude_states, items in those states wont be updated.
    # if you provide include_states, ONLY items in those states will be updated.
    assert len(items), 'Item list cannot be  empty'
    assert state, 'New State cannot be None'
    assert not (bool(exclude_states) and bool(include_states)), \
        'You should specify either included, excluded states or None, but not both'

    properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) - 'model' - 'model_pk' - 'model_external_id'"
    properties_query_params = []
    if related_model_instance:
        model_name = type(related_model_instance).__name__
        if model_name == 'Package':  # latam
            model_name = 'STOCK_PACKAGE'
        model_pk = related_model_instance.pk
        model_external_id = related_model_instance.external_id
        item_properties_str = json.dumps({
            "model": model_name,
            "model_pk": model_pk,
            "model_external_id": model_external_id
        })
        properties_query_str = "COALESCE(properties::jsonb, '{}'::jsonb) || %s::jsonb"
        properties_query_params = [item_properties_str]
    items = Item.objects.filter(pk__in=items)

    if exclude_states:
        items = items.exclude(state__in=exclude_states)
    if include_states:
        items = items.filter(state__in=include_states)

    item_ids = [item.pk for item in items]
    logging.info("Changing  %s items to %s state", len(item_ids), state)
    items.update(state=state, modified=datetime.now().astimezone(), modified_by=reporter,
                 properties=RawSQL(properties_query_str, properties_query_params))
    if return_objects:
        return Item.objects.filter(pk__in=item_ids)
    else:
        return item_ids


def update_move_progress(move, reporter: User):
    """
    Updates move status according to its lines, if all lines are completed,move is completed
    """
    assert move, 'Move cannot be None'
    # I know this algorithm could be more efficient, improve it when you can.
    total_completed_lines = 0
    for line in move.lines.all():
        if line.status == "COMPLETED":
            total_completed_lines += 1

    if total_completed_lines == len(move.lines.all()):
        move.status = "COMPLETED"
    else:
        move.status = "INCOMPLETE"
    move.modified_by = reporter
    move.save()


def update_items_move(items, reporter: User):
    items = Item.objects.filter(pk=items).first()
    items.state = "NOT_PRESENT"
    items.save()


def update_items_return(items=[], exclude_states=[], include_states=[], type_return=None):
    """
    Updates Items to given verified
    """
    # if you provide exclude_states, items in those states wont be updated.
    # if you provide include_states, ONLY items in those states will be updated.
    assert len(items), 'Item list cannot be  empty'
    assert not (bool(exclude_states) and bool(include_states)), \
        'You should specify either included, excluded states or None, but not both'

    items = Item.objects.filter(pk__in=items).exclude(state='INACTIVE')
    items_to_update = [item.pk for item in items]

    if exclude_states:
        items = items.exclude(state__in=exclude_states)
    if include_states:
        items = items.filter(state__in=include_states)

    logging.info("Changing  %s items to verified false", len(items_to_update))
    items.update(modified=datetime.now(), type_return=type_return)

    return items_to_update


def execute_simple_entry(move):
    # TODO: Add flag to update pending moves to  location
    assert move, 'Move cannot be None'
    assert move.type == "SIMPLE_ENTRY", 'Move type must  be Simple Entry, found: {} '.format(move.type)
    assert move.destination, 'Move destination cannot be None'
    logging.info("Executing simple entry  %s with %s lines", move, move.lines.count())

    def process_line(line_object):
        items_in_line = line_object.items_list
        updated_items_ids = move_items(items=items_in_line, destination=move.destination,
                                       reporter=move.modified_by)  # 1. Actually Move Items
        line.status = "COMPLETED"  # 2. Update Line to completed, verify si es completed or invalid
        line.add_received_items(items=updated_items_ids, replace=True, commit=False)  # 3. Update received Items
        line.save()
        return updated_items_ids

    # Process Line by line
    items = []
    for line in move.lines.all():
        updated_item_ids = process_line(line)
        items.extend(updated_item_ids)
    # Set move status to completed
    move.status = "COMPLETED"
    move.save()
    # Create traces
    traces = []
    for item in items:
        in_trace = Trace(
            item_id=item,
            location=move.destination,
            reporter=move.created_by,
            reporter_source="HANDHELD",
            action="SIMPLE_ENTRY",
            created_by=move.modified_by,
            modified_by=move.modified_by,
            customer=move.customer
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)

    return items


def execute_return(move):
    # TODO: Add flag to update pending moves to  location
    assert move, "Move cannot be None"
    assert move.type == "RETURN", "Move type must  be Return, found: {} ".format(move.type)
    assert move.destination, "Move destination cannot be None"
    logging.info("Executing return  %s with %s lines", move, move.lines.count())

    def process_line(line_object):
        items_in_line = line_object.items_list
        items_available = Item.objects.filter(pk__in=items_in_line)
        updated_items_ids = move_items(items=items_available, destination=move.destination,
                                       reporter=move.modified_by)  # 1. Actually Move Items
        line.status = "COMPLETED"  # 2. Update Line to completed, verify si es completed or invalid
        line.add_received_items(items=updated_items_ids, replace=True, commit=False)  # 3. Update received Items
        line.save()
        return updated_items_ids

    # Process Line by line
    items = []
    for line in move.lines.all():
        updated_item_ids = process_line(line)
        items.extend(updated_item_ids)
    # Set move status to completed
    move.status = "COMPLETED"
    move.save()
    # Create traces
    traces = []
    for item in items:
        in_trace = Trace(
            item_id=item,
            location=move.destination,
            reporter=move.modified_by,
            reporter_source="HANDHELD",
            action="RETURN",
            created_by=move.modified_by,
            modified_by=move.modified_by,
            customer=move.customer,
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)
    update_items_return(items=items, type_return=move.type_return)  # update items return.
    return items


def execute_transfer(move):
    assert move, 'Move cannot be None'
    assert move.type == "TRANSFER", 'Move type must  be Transfer, found: {} '.format(move.type)
    assert move.destination, 'Move destination cannot be None'
    logging.info("Executing transfer  %s with %s lines", move, move.lines.count())

    def process_line(line_object):
        items_in_line = line_object.items_list
        updated_items_ids = update_items_state(items=items_in_line, state="IN_TRANSFER",
                                               reporter=move.modified_by)  # 1.Update items to in transfer
        line.status = "INCOMPLETE"  # 2. Update Line status to incomplete
        line.add_expected_items(items=updated_items_ids, replace=True, commit=False)  # 3. Update received Items
        line.save()
        return updated_items_ids

    # Process Line by line
    items = []
    for line in move.lines.all():
        updated_item_ids = process_line(line)
        items.extend(updated_item_ids)
    # Set move status to completed
    move.status = "INCOMPLETE"
    move.save()
    # Create traces
    traces = []
    for item in items:
        in_trace = Trace(
            item_id=item,
            location=move.source,
            reporter=move.created_by,
            reporter_source="WEB_APP",
            action="TRANSFER_OUT",
            created_by=move.modified_by,
            modified_by=move.modified_by,
            customer=move.customer,
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)

    return items


def execute_action_receive(move, user, item_ids=[]):
    assert move, 'Move cannot be None'
    assert move.type == "TRANSFER", 'Move type must  be Transfer, found: {} '.format(move.type)
    assert move.status == "INCOMPLETE", 'Move status must be INCOMPLETE, found: {}'.format(move.status)
    assert move.destination, 'Move destination cannot be None'
    logging.info("Executing receive action in move %s with %s lines", move.pk, move.lines.count())

    def process_line(line_object, incoming_item_ids):
        expected_items = line_object.expected_items_list  # 1. items expected of the move.
        received_items = line_object.received_items_list  # 2. items already received of the move.
        pending_items = list(set(expected_items) - set(received_items))  # 3. items pending to receive

        items_found_to_receive = list(set(pending_items).intersection(incoming_item_ids))  # 4. items found to receive

        if not items_found_to_receive:
            return []

        updated_items_ids = move_items(items=items_found_to_receive, destination=move.destination,
                                       reporter=user)  # 5. Actually Receive Items
        logging.debug("Evaluating line %s . Expected  %s items", line.pk, expected_items)
        logging.debug("Evaluating line %s . Item found to receive  %s items", line.pk, items_found_to_receive)
        line.add_received_items(items=updated_items_ids, replace=False,
                                commit=False)  # 6.update received_items in the line.

        if set(line.expected_items_list) == set(line.received_items_list):  # 7. validation if complete line of items.
            line.status = "COMPLETED"
        else:  # 8. update state of lines and move.
            line.status = "INCOMPLETE"
        line.modified_by = user
        line.save()
        return updated_items_ids

    # Process Line by line
    items = []
    for line in move.lines.all():
        updated_items = process_line(line, item_ids)
        items.extend(updated_items)
    # Update move progress
    update_move_progress(move=move, reporter=user)
    trace = []
    for item in items:  # 9. create trace for all items_moves.
        in_trace = Trace(
            item_id=item,
            location=move.destination,
            reporter=user,
            reporter_source="HANDHELD",
            action="TRANSFER_IN",
            created_by=user,
            modified_by=user,
            customer=move.customer,
        )
        trace.append(in_trace)
    Trace.objects.bulk_create(trace)
    return items


def get_move_items(move):
    items = move.items_list
    return items


def execute_action_close(move, user):
    def process_items(line_object):
        expected_items = line_object.expected_items_list
        received_items = line_object.received_items_list
        pending_items = list(set(expected_items) - set(received_items))
        return pending_items

    for line in move.lines.all():
        items = []
        trace = []

        items_move = process_items(line)
        items.extend(items_move)

        if items:
            line.status = "CLOSED"
            line.save()
            for item in items:
                update_items_move(items=item, reporter=user)
                in_trace = Trace(
                    item_id=item,
                    location=move.destination,
                    reporter=user,
                    reporter_source="SYSTEM",
                    action="CLOSED",
                    created_by=user,
                    modified_by=user,
                    customer=move.customer,
                    additional_data={
                        "model_name": type(move).__name__,
                        "model_external_id": move.external_id,
                        "model_pk": move.pk
                    }
                )
                trace.append(in_trace)
            Trace.objects.bulk_create(trace)

    move.status = "CLOSED"
    move.save()


def execute_action_items(items, source_object, user_object, description, action, reporter_source, customer_object):
    traces = []
    item_objects = Item.objects.filter(pk__in=items).exclude(state='INACTIVE')
    for item in item_objects:
        if not source_object:
            source_object = item.current_location
        in_trace = Trace(
            item_id=item,
            location=source_object,
            reporter=user_object,
            reporter_source=reporter_source,
            action=action,
            created_by=user_object,
            modified_by=user_object,
            customer=customer_object,
            description=description
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)
    return items


def execute_action_activation_items(items, source_object, user_object, description, action, reporter_source, customer_object):
    traces = []
    item_objects = Item.objects.filter(pk__in=items)
    for item in item_objects:
        source_object = None
        if not source_object:
            source_object = item.current_location
        in_trace = Trace(
            item_id=item,
            location=source_object,
            reporter=user_object,
            reporter_source=reporter_source,
            action=action,
            created_by=user_object,
            modified_by=user_object,
            customer=customer_object,
            description=description
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)
    return items


def generate_move_items_file(move):
    """
    Generates Move Items File
    """

    items_move = get_move_items(move=move)
    # Create lines for items file lines (counted)
    sorted_summary_sku = inventory_utils.sort_items_by_sku(items_ids=items_move, customer_id=move.customer.pk)
    abs_path = os.path.abspath(os.path.dirname(__file__))

    file_name = "move_{}_items_file.csv".format(move.pk)
    path = os.path.join(abs_path, "../tmp/" + file_name)
    move_items_file_tmp = open(path, 'w')
    writer = csv.writer(move_items_file_tmp)

    for result in sorted_summary_sku:
        sku = result['sku']
        items = result['items']

        for item in items:
            writer.writerow([
                item,
                sku.id,
                sku.external_id.encode('utf-8'),
                sku.display_name
            ]
            )
    move_items_file_tmp.close()
    """
    summary_file = open(path, "rb")
    file_name = "move_{}_items_file.csv".format(move.pk)
    move.items_file.save(file_name, File(summary_file))
    """
    file_name = "move_{}_items_file.csv".format(move.pk)

    with open(path, mode="rb") as f:
        move.items_file = File(f, name=file_name)
        move.save(update_fields=["items_file"])
    os.remove(path)


def generate_move_summary_file(move):
    """
    Generates Move Summary File
    """
    items_move = get_move_items(move=move)
    # Create lines for summary file sku lines (counted)
    sorted_summary_sku = inventory_utils.sort_items_by_sku(items_ids=items_move, customer_id=move.customer.pk)
    abs_path = os.path.abspath(os.path.dirname(__file__))

    file_name = "move_{}_summary_file.csv".format(move.pk)
    path = os.path.join(abs_path, "../tmp/" + file_name)
    move_summary_file_tmp = open(path, 'w')
    writer = csv.writer(move_summary_file_tmp)
    for result in sorted_summary_sku:
        sku = result['sku']
        items = result['items']

        writer.writerow([
            sku.id,
            sku.external_id.encode('utf-8'),
            sku.display_name,
            len(items)]
        )
    move_summary_file_tmp.close()
    """
    summary_file = open(path, "rb")
    file_name = "move_{}_summary_file.csv".format(move.pk)
    move.summary_file.save(file_name, File(summary_file))
    """
    file_name = "move_{}_summary_file.csv".format(move.pk)
    with open(path, mode="rb") as f:
        move.summary_file = File(f, name=file_name)
        move.save(update_fields=["summary_file"])
    os.remove(path)


def move_to_services(move):
    """
    config_module = ModuleLocationConfig.objects.filter(current_location=move.destination,
                                                        kong_module__name='INVENTORY_MOVE_TRANSFER',
    """
    replicate_move_create(move_object=move)
    logging.info("Send  %s move to sqs.", move)


def unavailable_items(items_ids, reporter: User):
    items = Item.objects.filter(pk__in=items_ids)
    for item in items:
        item.state = 'INACTIVE'
        item.modified_by = reporter
        item.modified = datetime.now().astimezone()
        item.save()

    # Create traces
    traces = []
    for item in items:
        in_trace = Trace(
            created_by=reporter,
            modified_by=reporter,
            item_id=item.pk,
            location=item.current_location,
            reporter=reporter,
            reporter_source="SYSTEM",
            action="TAKEOUT",
            description="Inactivation of items at the request of the client.",
            modified=datetime.now().astimezone(),
            created=datetime.now().astimezone()
        )
        traces.append(in_trace)
    Trace.objects.bulk_create(traces)


def execute_possible_item_move(move: Move.objects):
    # item_actions_map = {-- actual_move_type --: {"last_states": [...], "action_trace": "", "reporter_source": ""}}
    item_actions_map = {
        "SIMPLE_ENTRY": {
            "last_states": ["SOLD"],
            "action_type": "RETURN",
            "reporter_source": "FIXED_PORTAL"
        }
    }
    action_map = item_actions_map.get(move.type)
    if action_map:
        item_ids = []
        for line in move.lines.all():
            item_ids.extend(line.items_list)
        traces = []
        item_list = Item.objects.filter(id__in=item_ids, state__in=action_map["last_states"])
        if item_list:
            possible_move = Move.objects.create(
                created_by=move.modified_by,
                modified_by=move.modified_by,
                source_id=move.source_id,
                destination_id=move.destination_id,
                type=action_map["action_type"],
                reporter_source=action_map["reporter_source"],
                status="COMPLETED",
                customer_id=move.customer_id,
            )
            create_lines_from_items(move=possible_move, items=item_list, include_states=None, modified_as_timestamp=True)
        for item in item_list:
            in_trace = Trace(
                created_by=move.modified_by,
                modified_by=move.modified_by,
                item=item,
                location=move.destination,
                reporter=move.created_by,
                reporter_source=action_map["reporter_source"],
                action=action_map["action_type"],
                customer=move.customer
            )
            traces.append(in_trace)
        Trace.objects.bulk_create(traces)


def process_move_master_file(move: Move):
    extra_action_commands = {
        "CHANGE_SIGN": "{str_value_command} * (-1)"
    }
    sub_location_id = None
    # Get column names
    master_file_columns = move.MASTER_FILE_PARAMS["master_file_columns"]
    product_code_column = master_file_columns["product_code"]["external_column_name"]
    recursive_property_column = master_file_columns["recursive_property"].get("external_column_name")
    amount_column = master_file_columns["amount"]["external_column_name"]
    amount_extra_action = master_file_columns["amount"].get("extra_action")
    price_column = master_file_columns["price"]["external_column_name"]
    date_column = master_file_columns["date"]["external_column_name"]

    # Read file and pass to dataframe
    expected_min_header_length = 0
    for column_params in master_file_columns.values():
        if column_params.get("required") == "true":
            expected_min_header_length = expected_min_header_length + len(column_params["external_column_name"])

    expected_min_header_length = expected_min_header_length if expected_min_header_length else 256
    master_file = move.master_file
    dialect = csv.Sniffer().sniff(str(master_file.read(expected_min_header_length)))
    master_file.seek(0)
    df_master_file = pd.read_csv(master_file, sep=dialect.delimiter, dtype='str')

    # Ignore sales already charged by datetime
    df_master_file[date_column] = pd.to_datetime(df_master_file[date_column], dayfirst=True)
    max_timestamp = datetime.timestamp(max(df_master_file[date_column])) + 86400
    min_timestamp = datetime.timestamp(min(df_master_file[date_column]))
    datetime_in_kong = MoveLine.objects.filter(
        move__reporter_source='WEB_APP', timestamp__lte=max_timestamp, timestamp__gte=min_timestamp) \
        .extra(select={"datetime": "to_timestamp(timestamp)::date"}) \
        .values_list('datetime', flat=True).order_by('datetime').distinct()
    df_master_file = df_master_file[~df_master_file[date_column].isin(datetime_in_kong)]

    # Form recursive code param
    #recursive_code = df_master_file[product_code_column] + df_master_file[recursive_property_column]
    recursive_code = df_master_file[product_code_column]
    df_master_file["recursive_code"] = recursive_code

    # Join related sku id filtering by sku external id
    recursive_codes = list(set(df_master_file.to_dict("list")["recursive_code"]))
    #sku_related_ids = SKURecursiveCode.objects.filter(recursive_code__in=recursive_codes) \
    #    .values("sku_id", "recursive_code")
    sku_related_ids = SKU.objects.filter(external_id__in=recursive_codes).annotate(recursive_code=F('external_id')).values("recursive_code", "external_id", 'pk')
    df_sku_related_ids = pd.DataFrame.from_records(sku_related_ids, columns=['recursive_code', 'external_id'])
    df_sku_related_ids['recursive_code'] = df_sku_related_ids['recursive_code']
    df_master_file['recursive_code'] = df_master_file['recursive_code']
    df_result_master_file = pd.merge(left=df_master_file, right=df_sku_related_ids, on="recursive_code")

    # Normalize data, price and amount, '(' is from accounting format and set float decimal format
    df_result_master_file[amount_column] = df_result_master_file[amount_column] \
        .replace(["\(", "\)"], ['-', ''], regex=True)
    df_result_master_file[price_column] = df_result_master_file[price_column] \
        .replace(["\(", "\)"], ['-', ''], regex=True)
    df_result_master_file[price_column] = df_result_master_file[price_column] \
        .replace('\.', '', regex=True)
    df_result_master_file[price_column] = df_result_master_file[price_column] \
        .replace('\,', '.', regex=True)
    df_result_master_file[amount_column] = df_result_master_file[amount_column].astype(int)
    df_result_master_file[price_column] = df_result_master_file[price_column].astype(float)

    if amount_extra_action and extra_action_commands.get(amount_extra_action):
        df_result_master_file[amount_column] = eval(extra_action_commands.get(amount_extra_action).format(
            str_value_command='df_result_master_file[amount_column]'
        ))
    # Create lines
    result_master_file_dict = df_result_master_file.to_dict("records")
    new_lines = []
    return_lines = []
    for line in result_master_file_dict:
        if line[amount_column] > 0:
            sku = SKU.objects.filter(external_id=line["external_id"]).first()
            if move.source.type.name == "STORE":
                show_room = Location.objects.filter(parent=move.source, type__name="SHOWROOM").first()
                sub_location_ids = inventory_utils.get_location_tree(location_id=show_room.pk)
                inventory = (Item.objects.filter(sku__external_id=line["recursive_code"], current_location_id__in=sub_location_ids)
                             .values('sku_id', 'current_location_id').annotate(total_amount=Count('pk')).order_by('-total_amount'))
                if len(inventory) > 0:
                    sub_location_id = inventory[0]["current_location_id"]
            new_lines.append(
                MoveLine(
                    move=move,
                    status="COMPLETED",
                    sku_id=sku.pk,
                    amount=line[amount_column],
                    timestamp=datetime.timestamp(line[date_column]),
                    sub_location_id=sub_location_id,
                    price=line[price_column],
                    created_by=move.created_by,
                    modified_by=move.modified_by
                )
            )
        else:
            return_lines.append(line)

    # Create lines from RETURN items
    moves = [move]
    return_move = None
    if return_lines:
        return_move = deepcopy(move)
        return_move.pk = None
        return_move.type = "RETURN"
        return_move.save()
        moves.append(return_move)
    for line in return_lines:
        amount = abs(line[amount_column])
        price = abs(line[price_column])
        if amount != 0:
            sku = SKU.objects.filter(external_id=line["external_id"]).first()
            if move.source.type.name == "STORE":
                show_room = Location.objects.filter(parent=move.source, type__name="SHOWROOM").first()
                sub_location_ids = inventory_utils.get_location_tree(location_id=show_room.pk)
                inventory = (Item.objects.filter(sku__external_id=line["recursive_code"], current_location_id__in=sub_location_ids)
                             .values('sku_id', 'current_location_id').annotate(total_amount=Count('pk')).order_by('-total_amount'))
                if len(inventory) > 0:
                    sub_location_id = inventory[0]["current_location_id"]
            new_lines.append(
                MoveLine(
                    move=return_move,
                    status="COMPLETED",
                    sku_id=sku.pk,
                    amount=amount,
                    timestamp=datetime.timestamp(line[date_column]),
                    sub_location_id=sub_location_id,
                    price=price,
                    created_by=move.created_by,
                    modified_by=move.modified_by
                )
            )
    MoveLine.objects.bulk_create(new_lines)

    # Update price on RFID sale moves
    if not df_result_master_file.empty:
        result_max_timestamp = datetime.timestamp(max(df_result_master_file[date_column])) + 86400
        result_min_timestamp = datetime.timestamp(min(df_result_master_file[date_column]))
        df_result_master_file[price_column] = df_result_master_file[price_column].abs()
        df_result_master_file[amount_column] = df_result_master_file[amount_column].abs()
        df_unit_price = df_result_master_file.groupby([product_code_column, date_column], as_index=False).sum()
        df_unit_price["unit_price"] = df_unit_price[price_column] / df_unit_price[amount_column]
        rfid_sales = MoveLine.objects.select_related('sku').exclude(move__reporter_source='WEB_APP').filter(
            timestamp__lte=result_max_timestamp, timestamp__gte=result_min_timestamp,
            move__type__in=["SALE_OUT", "RETURN"]).extra(select={"datetime": "to_timestamp(timestamp)::date"})
        for rfid_sale in rfid_sales:
            df_query = df_unit_price.query("`{}` == '{}' and `{}` == '{}'".format(
                    product_code_column, rfid_sale.sku.external_id, date_column, rfid_sale.datetime))
            if not df_query.empty:
                rfid_sale.price = df_query['unit_price'].values[0] * rfid_sale.amount
                rfid_sale.modified_by = move.modified_by
                rfid_sale.save()
    return moves


def validate_move_master_file(move: Move):
    master_file_columns = move.MASTER_FILE_PARAMS["master_file_columns"]
    required_columns = []
    expected_min_header_length = 0
    for column_params in master_file_columns.values():
        if column_params.get("required") == "true":
            required_columns.append(column_params["external_column_name"])
            expected_min_header_length = expected_min_header_length + len(column_params["external_column_name"])

    expected_min_header_length = expected_min_header_length if expected_min_header_length else 256
    master_file = move.master_file
    dialect = csv.Sniffer().sniff(str(master_file.read(expected_min_header_length)))
    master_file.seek(0)
    df_master_file = pd.read_csv(master_file, sep=dialect.delimiter)
    columns = list(df_master_file.columns)
    for required_column in required_columns:
        if required_column not in columns:
            error_message = "Invalid csv format, required columns are {}".format(required_columns)
            raise AttributeError(error_message)


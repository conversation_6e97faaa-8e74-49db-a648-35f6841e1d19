# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json
from datetime import datetime
import pandas as pd

from django.db.models.aggregates import Count

from customers.models import *
from ..view_models import LocationItemsTotal, InventorySummaryToLocation, InventorySummaryToLocationByState
from ..models import *
import inventory.utils as inventory_utils


def get_amount_inventory(location_id, customer_id):
    level_locations = inventory_utils.get_locations_tree(location_id=location_id)
    summary_list = LocationItemsTotal.objects.filter(current_location_id__in=level_locations,
                                                     customer_id=customer_id).values('total_present',
                                                                                     'total_dispatch',
                                                                                     'total_in_transfer',
                                                                                     'total')
    total = inventory_utils.group_by_state(summary_list, customer_id, location_id)
    return total


def get_summary_inventory(self, current_location_id, customer_id):
    level_locations = inventory_utils.get_locations_tree(location_id=current_location_id)
    customer = Customer.objects.get(pk=customer_id)
    if customer.name == 'MARROQUINERA_COLOMBIA':
        summary_list = list(self.filter_queryset(
            InventorySummaryToLocationByState.objects.filter(current_location_id__in=level_locations,
                                                             customer_id=customer_id,).exclude(total_present=None)).order_by('-sku_id'))
        join_up_fields = ('total_present',)
        total_summary = inventory_utils.join_up_repeated_sku(summary_list=summary_list, reserved_lines=None,
                                                             join_up_fields=join_up_fields, customer=customer)
    else:
        summary_list = list(self.filter_queryset(InventorySummaryToLocation.objects.filter(current_location_id__in=level_locations, customer_id=customer_id))
                                .order_by('-sku_id'))

        reserved = Reserve.objects.filter(status='CREATED', customer_id=customer_id,
                                          location_id__in=level_locations).values_list('id', flat=True)
        reserved_lines = list(
            ReserveLine.objects.filter(reserve_id__in=reserved).values('sku_id', 'amount').order_by('sku_id'))
        join_up_fields = ('total_present', 'total_available', 'total_seconds', 'total')
        total_summary = inventory_utils.join_up_repeated_sku(summary_list=summary_list, reserved_lines=reserved_lines,
                                                             join_up_fields=join_up_fields, customer=customer)
    return total_summary


def get_amount_inventory_type(customer_id, type_id, parent_id):
    summary = LocationItemsTotal.objects.all()
    group_level = []
    location_types = Location.objects.filter(type_id=type_id, customer__pk=customer_id, parent_id=parent_id)
    for location in location_types:
        level_locations = inventory_utils.get_locations_tree(location_id=location.id)
        summary_list = summary.filter(current_location_id__in=level_locations, customer_id=customer_id).values(
            'total_present', 'total_dispatch', 'total_in_transfer', 'total')
        total = inventory_utils.group_by_parent_location(line_objects=summary_list)
        custom = {'customer': location.customer.get(pk=customer_id), 'current_location': location}
        total.update(custom)
        group_level.append(total)
    return group_level


def get_amount_inventory_parent(customer_id, parent_id, page, page_size):
    group_level = []
    offset = (page - 1) * page_size
    location_types = Location.objects.filter(customer__pk=customer_id, parent_id=parent_id)
    for index in range(0, len(location_types)):
        location = location_types[index]
        level_locations = inventory_utils.get_location_tree(location_id=location.id)
        if offset <= index < (offset + page_size):
            summary_list = LocationItemsTotal.objects.filter(
                current_location_id__in=level_locations, customer_id=customer_id)\
                .values('total_present', 'total_dispatch', 'total_in_transfer', 'total')
        else:
            summary_list = [{'total_present': None, 'total_dispatch': None, 'total_in_transfer': None, 'total': None}]
        total = inventory_utils.group_by_parent_location(line_objects=summary_list)
        custom = {'customer': location.customer.get(pk=customer_id), 'current_location': location}
        total.update(custom)
        group_level.append(total)
    return group_level


def get_move_summary_amount(queryset: MoveLine.objects, sub_location_id: int, move_type: str, group_by_date="TRUE"):
    sub_location_ids = inventory_utils.get_locations_tree(location_id=sub_location_id)
    base_summary = queryset.filter(sub_location_id__in=sub_location_ids)
    query_values = ['sku_id', 'move__customer_id', 'sub_location_id']
    if group_by_date == "TRUE":
        base_summary = base_summary.extra(select={"date": "to_timestamp(timestamp)::date"})
        query_values.append('date')

    result_summary, total_amounts = operate_and_group_sale_moves(base_summary=base_summary, query_values=query_values)

    for summary in result_summary:
        percentage = summary.get('total_amount') * 100 / total_amounts
        summary['percentage'] = percentage
        summary['move__type'] = move_type

    return result_summary


def operate_and_group_sale_moves(base_summary: MoveLine.objects, sub_location_ids, query_values: list,
                                 build_default_line=False, default_query_values={},
                                 amount_type: str = None, created_min=None, created_max=None):
    amount_fields = {
        "AMOUNT": "amount",
        "PRICE": "price"
    }
    amount_field = amount_fields.get(amount_type, "amount")
    """
    web_out_dates = SalesSKU.objects.filter(reporter_source__in=["HANDHELD"], type="SALE_OUT")\
        .values_list('created', flat=True).order_by('created').distinct()

    web_return_dates = SalesSKU.objects.filter(reporter_source__in=["HANDHELD"], type="RETURN")\
        .values_list('created', flat=True).order_by('created').distinct()
    
    web_out_summary = SalesSKU.objects.filter(reporter_source__in=["HANDHELD"], type="SALE_OUT") \
        .values(*query_values).annotate(total_amount=Sum('total_amount'), lines_count=Sum('total_lines')).order_by()

    web_return_summary = SalesSKU.objects.filter(reporter_source__in=["HANDHELD"], type="RETURN") \
        .values(*query_values).annotate(total_amount=Sum('total_amount') * (-1), lines_count=Sum('total_lines')).order_by()
    """
    if created_max and created_min:
        device_out_summary = SalesSKU.objects.filter(type="SALE_OUT", sub_location_id__in=sub_location_ids,
                                                     sale_date__gte=created_min, sale_date__lte=created_max).exclude(reporter_source__in=["HANDHELD"])
        device_return_summary = SalesSKU.objects.filter(type="RETURN", sub_location_id__in=sub_location_ids,
                                                        sale_date__gte=created_min, sale_date__lte=created_max)\
            .exclude(reporter_source__in=["HANDHELD"])
    else:
        device_out_summary = SalesSKU.objects.filter(type="SALE_OUT", sub_location_id__in=sub_location_ids,)\
            .exclude(reporter_source__in=["HANDHELD"])
        device_return_summary = SalesSKU.objects.filter(type="RETURN", sub_location_id__in=sub_location_ids) \
            .exclude(reporter_source__in=["HANDHELD"])
    """
    if web_out_dates:
        device_out_summary = device_out_summary.extra(
            where=["created::date NOT IN %s"], params=[tuple(web_out_dates)])
    if web_return_dates:
        device_return_summary = device_return_summary.extra(
            where=["created::date NOT IN %s"], params=[tuple(web_return_dates)])
    """
    device_out_summary = device_out_summary.values(*query_values)\
        .annotate(total_amount=Sum('total_amount'), lines_count=Sum('total_lines')).order_by()
    device_return_summary = device_return_summary.values(*query_values)\
        .annotate(total_amount=Sum('total_amount') * (-1), lines_count=Sum('total_lines')).order_by()
    total_summary = device_out_summary.union(device_out_summary, device_return_summary)
    df_total_summary = pd.DataFrame.from_records(total_summary, columns=query_values + ["total_amount", "lines_count"])
    if not df_total_summary.empty:
        for column in query_values:
            if str(df_total_summary[column].dtypes) == 'object':
                df_total_summary[column] = df_total_summary[column].astype('str')
        df_total_summary = df_total_summary.groupby(query_values, as_index=False).sum()
        df_total_summary.sort_values(by="total_amount", inplace=True, ascending=False)
    elif build_default_line:
        default_line = {"total_amount": 0, "lines_count": 0}
        for column in query_values:
            default_line[column] = default_query_values.get(column)
        df_total_summary = df_total_summary.append(default_line, ignore_index=True)
    total_amounts = df_total_summary["total_amount"].sum()
    result_summary = df_total_summary.to_dict("records")
    return result_summary, total_amounts


def get_move_total_amount(queryset: MoveLine.objects,  move_type: str, customer_id: str,
                          sub_location_id=None, parent_id=None, sub_location_type_id=None,
                          amount_type: str = None, created_min=None, created_max=None):
    total_amounts = 0
    if parent_id or sub_location_type_id or sub_location_id:
        partial_totals = []
        location_filters = {"customer": customer_id, "is_active": True}
        if sub_location_id:
            location_filters['id'] = sub_location_id
        if parent_id:
            location_filters['parent_id'] = parent_id
        if sub_location_type_id:
            location_filters['type_id'] = sub_location_type_id
        related_locations = Location.objects.filter(**location_filters)
        total_amount_sale = SalesSKU.objects.filter(sub_location_id__in=related_locations.values_list('id'),
                                                    type='SALE_OUT').aggregate(total_amounts=Sum('total_amount'))
        if total_amount_sale["total_amounts"]:
            total_amount_sales = total_amount_sale["total_amounts"]
        total_amount_returned = SalesSKU.objects.filter(sub_location_id__in=related_locations.values_list('id'),
                                                        type='RETURN').values('total_amount').aggregate(
            total_amounts=Sum('total_amount'))
        if total_amount_returned["total_amounts"]:
            total_amount_returneds = total_amount_returned["total_amounts"]
        for location in related_locations:
            location_id = location.id
            sub_location_ids = inventory_utils.get_location_tree(location_id=location_id)
            query_values = ['customer_id']
            default_query_values = {"customer_id": customer_id}
            summary_total = queryset.filter(sub_location_id__in=sub_location_ids)
            result_summary, total_amount = operate_and_group_sale_moves(
                base_summary=summary_total, query_values=query_values, build_default_line=True,
                default_query_values=default_query_values, amount_type=amount_type,
                sub_location_ids=sub_location_ids, created_min=created_min, created_max=created_max)
            total_amounts = total_amounts + total_amount
            for total in result_summary:
                total['sub_location_id'] = location_id
                total['sub_location_ids'] = sub_location_ids
                partial_totals.append(total)
        totals = sorted(partial_totals, key=lambda k: k['total_amount'], reverse=True)
    else:
        query_values = ['sub_location_id', 'move__customer_id']
        result_summary, total_amounts = operate_and_group_sale_moves(
            base_summary=queryset, query_values=query_values, amount_type=amount_type)
        totals = result_summary

    for total in totals:
        percentage = 0.0
        if total_amounts != 0:
            percentage = total.get('total_amount') * 100 / total_amounts
        total['percentage'] = percentage
        total['move__type'] = move_type

    return totals


def get_move_location_properties_total_amount(created_min, created_max, queryset: MoveLine.objects, move_totals: [],
                                              amount_type: str = None):
    for move_total in move_totals:
        sub_location_id = move_total['sub_location_id']
        sub_location_ids = move_total.pop('sub_location_ids', None)
        if not sub_location_ids:
            sub_location_ids = [sub_location_id]
        customer_id = move_total.get('move__customer_id')
        query_values = ['sku__properties']
        totals = queryset.filter(sub_location_id__in=sub_location_ids, move__customer_id=customer_id)
        result_response = operate_and_group_sale_moves(
            base_summary=totals, query_values=query_values, amount_type=amount_type, sub_location_ids=sub_location_ids,
            created_min=created_min, created_max=created_max)
        result_summary = result_response[0]

        grouped_properties = {}
        for total in result_summary:
            """{
                "-- property key --": {"-- property value --": ["-- amounts --"]}
            }"""
            sku_properties = json.loads(total["sku__properties"].replace('\"', '\\"').replace("\'", "\""))
            for property_key, property_value in sku_properties.items():
                grouped_properties.setdefault(property_key, {property_value: []}) \
                    .setdefault(property_value, []).append(total["total_amount"])

        properties_dict = {}
        for property_key, grouped_values in grouped_properties.items():
            total_amount = 0
            property_values = []
            for value_amounts in grouped_values.values():
                amount = 0
                for value_amount in value_amounts:
                    amount = amount + value_amount
                total_amount = total_amount + amount
            for property_value, value_amounts in grouped_values.items():
                amount = 0
                percentage = 0.0
                for value_amount in value_amounts:
                    amount = amount + value_amount
                if total_amount != 0:
                    percentage = amount * 100 / total_amount
                property_values.append({
                    "value": property_value,
                    "amount": amount,
                    "percentage": "{0:.2f}".format(percentage)
                })
            order_property_values = sorted(property_values, key=lambda k: k['percentage'], reverse=True)
            properties_dict[property_key] = order_property_values

        move_total['properties'] = properties_dict
    return move_totals

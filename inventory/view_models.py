# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from common.models import ViewModel
from django.db import models
from inventory.models import SKU, Location, LocationType
from customers.models import Customer


class LocationInventorySummary(models.Model, ViewModel):
    sku = models.ForeignKey(SKU, on_delete=models.DO_NOTHING)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING)
    total_present = models.IntegerField()
    total_in_transfer = models.IntegerField()
    total_packed = models.IntegerField()
    total_dispatch = models.IntegerField()
    total_reserved = models.IntegerField()
    total_difference = models.IntegerField()
    total = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'inventory_summary'
        ordering = ['-total_present']


class LocationItemsTotal(models.Model, ViewModel):
    current_location = models.ForeignKey(Location, on_delete=models.DO_NOTHING)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING)
    total_present = models.IntegerField()
    total_dispatch = models.IntegerField()
    total_in_transfer = models.IntegerField()
    total = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'inventory_total_to_location'


class InventorySummaryToLocation(models.Model, ViewModel):
    sku = models.ForeignKey(SKU, on_delete=models.DO_NOTHING)
    current_location = models.ForeignKey(Location, on_delete=models.DO_NOTHING)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING)
    total_present = models.IntegerField(null=True)
    total_available = models.IntegerField(null=True)
    total_seconds = models.IntegerField(null=True)
    total = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'inventory_summary_to_location'
        ordering = ['-current_location_id', '-total_present']


class InventorySummaryToLocationByState(models.Model, ViewModel):
    sku = models.ForeignKey(SKU, on_delete=models.DO_NOTHING)
    current_location = models.ForeignKey(Location, on_delete=models.DO_NOTHING)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING)
    total_present = models.IntegerField(null=True)

    class Meta:
        managed = False
        db_table = 'inventory_summary_to_location_by_state'
        ordering = ['-current_location_id', '-total_present']


class SkuKeys(models.Model, ViewModel):
    key = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'sku_keys'
        ordering = ['-id']


class SkuProperties(models.Model, ViewModel):
    key = models.CharField(max_length=50)
    value = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'sku_key_value'
        ordering = ['-id']


class SKURecursiveCode(models.Model, ViewModel):
    sku = models.ForeignKey(SKU, on_delete=models.DO_NOTHING)
    recursive_code = models.CharField(max_length=500)

    class Meta:
        managed = False
        db_table = 'sku_recursive_code'
        ordering = ['-id']

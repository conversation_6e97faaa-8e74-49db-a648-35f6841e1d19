-- Create Application users and set permissions
CREATE DATABASE kong_core;
CREATE USER kong_core_app;
ALTER USER kong_core_app WITH PASSWORD 'kong_core_app_F=V84?>xc>w-G!ctVn';

GRANT ALL PRIVILEGES ON DATABASE kong_core TO kong_core_app;

-- Enable Postgresql Hstore Extension
CREATE EXTENSION IF NOT EXISTS hstore;


--Insert to kong modules
INSERT INTO public.core_config_kongmodule (code,"name",description) VALUES
('AD_000','INVENTORY_AUDITS','Audits Module'),
('AD_001','INVENTORY_AUDIT_CREATE','create audits '),
('AD_002','INVENTORY_AUDIT_LIST','list audits'),
('AD_003','INVENTORY_AUDIT_ADJUST','adjust audits'),
('ALY_QS_000','ANALYTICS_QS','analytics quicksight module'),
('MV_000','INVENTORY_MOVES','Moves Module'),
('MV_001','INVENTORY_MOVE_LIST','list moves'),
('IM_001','INVENTORY_ITEM_DETAIL','detail items'),
('IM_002','INVENTORY_ITEM_POSITION','position items'),
('PK_001','OPERATIONS_PACKING_DETAIL','detail pakings'),
('PK_002','OPERATIONS_PACKING_DISPATCH','dispatch packings'),
('PK_003','OPERATIONS_PACKING_LIST','list packings'),
('PK_004','OPERATIONS_PACKING_DESTROY','destroy pakings');
INSERT INTO public.core_config_kongmodule (code,"name",description) VALUES
('WO_001','OPERATIONS_WORK_ORDER_CREATE','create work order'),
('WO_002','OPERATIONS_WORK_ORDER_LIST','list work orders'),
('WO_003','OPERATIONS_WORK_ORDER_DETAIL','detail work orders'),
('SO_001','OPERATIONS_STORE_ORDER_CREATE','create store orders'),
('SO_002','OPERATIONS_STORE_ORDER_LIST','list store orders'),
('SO_003','OPERATIONS_STORE_ORDER_DETAIL','detail store orders'),
('DT_001','OPERATIONS_DISTRIBUTION_CREATE','create distributions'),
('DT_002','OPERATIONS_DISTRIBUTION_LIST','list distributions'),
('DT_003','OPERATIONS_DISTRIBUTION_DETAIL','detail distributions'),
('CT_000','OPERATIONS_CONTACTS','contacts module'),
('CT_001','OPERATIONS_CONTACT_CREATE','create contacts');
INSERT INTO public.core_config_kongmodule (code,"name",description) VALUES
('CT_002','OPERATIONS_CONTACT_LIST','list contacts'),
('CT_003','OPERATIONS_CONTACT_DETAIL','detail contacts'),
('IM_000','INVENTORY','Inventory Module'),
('IM_003','INVENTORY_ITEM_RECEIVE','receive items'),
('IM_006','INVENTORY_ITEM_INACTIVE','Desactivar Items'),
('PO_000','PRODUCTION_ORDER','Production Order Module'),
('PR_000','PRODUCTS','Products Module'),
('PR_001','PRODUCT_CREATE','product create'),
('PR_002','PRODUCT_LIST','product list'),
('PR_003','PRODUCT_DETAIL','product detail'),
('IM_004','INVENTORY_RESUME_LIST','inventory resume'),
('PO_001','PRODUCTION_ORDER_CREATE','production order create'),
('PO_002','PRODUCTION_ORDER_LIST','production order list'),
('PO_003','PRODUCTION_ORDER_DETAIL','production order detail');
INSERT INTO public.core_config_kongmodule (code,"name",description) VALUES
('PUO_001','PURCHASE_ORDER_CREATE','purchase order create'),
('PUO_002','PURCHASE_ORDER_LIST','purchase order list'),
('PUO_003','PURCHASE_ORDER_DETAIL','purchase order detail'),
('PRNT_003','PRINT_ORDER_DETAIL','print order detail'),
('PRNT_001','PRINT_ORDER_CREATE','print order create'),
('PRNT_002','PRINT_ORDER_LIST','print order list'),
('HW_002','HARDWARE_LIST','hardware list'),
('TRKG_001','TRACKING_LIST','List Trackings'),
('LG_000','LOGISTICS','Logistics Module'),
('LG_001','LOGISTICS_PICKING_CREATE','create picking'),
('LG_002','LOGISTICS_SHIPPING_RETURN_LIST','List Shipping logistics'),
('PO_004','PRODUCTION_ORDER_COMPLETE', 'Complete issued sku amount in orden'),
('OP_001','OPERATIONS_CANCEL_CASCADE_ORDER','Cancelar Ordenes en cascadas(sto,dis,wko)'),
('TRKG_002','TRACKING_ITEM_VALIDATE','Validate tracking items');
INSERT INTO public.core_config_kongmodule (code,"name",description) VALUES
('PK_005','OPERATIONS_PACKING_VALIDATE','Validate packing'),
('IM_005','INVENTORY_ITEM_RETURN','Return item'),
('IM_007','INVENTORY_ITEM_ENABLE','enable items from UNAVAILABLE status'),
('IM_009','INVENTORY_SKU_MAILING','Crear skus de tipo envio(inventario)'),
('PK_006','OPERATIONS_PACKING_CREATE','Create packing'),
('MV_002','INVENTORY_TRANSFER_CREATE','Create transfer move'),
('MV_003','INVENTORY_TRANSFER_RECEIVE','Receive transfer move'),
('ST_PG_001','STOCK_PACKAGE_LIST','Package_List'),
('ST_PG_002','STOCK_PACKAGE_CREATE','Package_Create'),
('ST_PG_003','STOCK_PACKAGE_ASSOCIATE_TAG','Package_Associate_Tag'),
('ST_PG_004','STOCK_PACKAGE_DESTROY','Stock_Package_Destroy');

--Insert to Location types
INSERT INTO public.inventory_locationtype (created,modified,external_id,"name",created_by_id,modified_by_id,parent_id) VALUES
('2019-04-15 12:15:08.704','2019-04-15 12:15:08.704','A1','COUNTRY',1,1,NULL)
,('2019-04-15 12:15:24.080','2019-04-15 12:15:24.080','B1','DEPARTMENT/STATE',1,1,1)
,('2019-04-15 12:15:35.656','2019-04-15 12:15:35.656','C1','CITY',1,1,2)
,('2019-04-15 12:20:11.356','2019-04-15 12:20:11.356','D1','HOLDING',1,1,3)
,('2019-04-15 12:20:30.117','2019-04-15 12:20:30.117','E1','WAREHOUSE',1,1,4)
,('2019-04-15 12:28:34.113','2019-04-15 12:28:34.114','F1','SALON',1,1,5)
,('2019-04-15 12:28:52.552','2019-04-15 12:28:52.552','G1','ROW',1,1,6)
,('2019-04-15 12:29:06.461','2019-04-15 12:29:06.461','H1','FRONT',1,1,7)
,('2019-04-15 12:29:22.775','2019-04-15 12:29:22.775','H2','BACK',1,1,7)
,('2019-04-15 12:29:47.638','2019-04-15 12:29:47.638','I1','RACK-FRONT',1,1,8);

INSERT INTO public.inventory_locationtype (created,modified,external_id,"name",created_by_id,modified_by_id,parent_id) VALUES
('2019-04-15 12:30:06.890','2019-04-15 12:30:06.890','I2','RACK-BACK',1,1,9)
,('2019-04-15 12:30:29.923','2019-04-15 12:30:29.923','J1','LEVEL-FRONT',1,1,10)
,('2019-04-15 12:31:00.305','2019-04-15 12:31:00.305','J2','LEVEL-BACK',1,1,11)
,('2019-04-15 12:31:15.424','2019-04-15 12:31:15.424','K1','POSITION-FRONT',1,1,12)
,('2019-04-15 12:31:29.945','2019-04-15 12:31:29.945','K2','POSITION-BACK',1,1,13);

-- Insert Services Types
INSERT INTO public.integrations_servicetype (created,modified,external_id,name,description,created_by_id,modified_by_id) VALUES
('2019-07-02 10:25:03.281','2019-07-02 10:25:03.281','1','Next Day Air','Next Day Air',1,1)
,('2019-07-02 10:25:19.581','2019-07-02 10:25:19.581','2','2nd Day Air','2nd Day Air',1,1)
,('2019-07-02 10:25:33.027','2019-07-02 10:25:33.027','3','Ground','Ground',1,1)
,('2019-07-02 10:25:47.230','2019-07-02 10:25:47.230','7','Express','Express',1,1)
,('2019-07-02 10:26:03.058','2019-07-02 10:26:03.058','8','Expedited','Expedited',1,1)
,('2019-07-02 10:26:16.909','2019-07-02 10:26:16.909','11','UPS Standard','UPS Standard',1,1)
,('2019-07-02 10:26:30.163','2019-07-02 10:26:30.163','12','3 Day Select','3 Day Select',1,1)
,('2019-07-02 10:26:45.453','2019-07-02 10:26:45.453','13','Next Day Air Saver','Next Day Air Saver',1,1)
,('2019-07-02 10:28:08.119','2019-07-02 10:28:08.119','14','UPS Next Day Air® Early','UPS Next Day Air® Early',1,1)
,('2019-07-02 10:28:22.035','2019-07-02 10:28:22.035','54','Express Plus','Express Plus',1,1);

INSERT INTO public.integrations_servicetype (created,modified,external_id,name,description,created_by_id,modified_by_id) VALUES
('2019-07-02 10:28:33.207','2019-07-02 10:28:33.208','59','2nd Day Air A.M.','2nd Day Air A.M.',1,1)
,('2019-07-02 10:28:45.419','2019-07-02 10:28:45.419','65','UPS Saver','UPS Saver',1,1)
,('2019-07-02 10:28:56.929','2019-07-02 10:28:56.929','M2','First Class Mail','First Class Mail',1,1)
,('2019-07-02 10:29:12.288','2019-07-02 10:29:12.288','M3','Priority Mail','Priority Mail',1,1)
,('2019-07-02 10:29:29.028','2019-07-02 10:29:29.028','M4','Expedited MaiI Innovations','Expedited MaiI Innovations',1,1)
,('2019-07-02 10:29:48.489','2019-07-02 10:29:48.489','M5','Priority Mail Innovations','Priority Mail Innovations',1,1)
,('2019-07-02 10:30:04.736','2019-07-02 10:30:04.736','M6','Economy Mail Innovations','Economy Mail Innovations',1,1)
,('2019-07-02 10:30:24.342','2019-07-02 10:30:24.342','M7','MaiI Innovations (MI) Returns','MaiI Innovations (MI) Returns',1,1)
,('2019-07-02 10:40:59.682','2019-07-02 10:40:59.682','70','UPS Access Point Economy','UPS Access Point Economy',1,1)
,('2019-07-02 10:41:11.711','2019-07-02 10:41:11.711','71','UPS Worldwide Express Freight Midday','UPS Worldwide Express Freight Midday',1,1);

INSERT INTO public.integrations_servicetype (created,modified,external_id,name,description,created_by_id,modified_by_id) VALUES
('2019-07-02 10:41:22.619','2019-07-02 10:41:22.619','74','UPS Express®12:00','UPS Express®12:00',1,1)
,('2019-07-02 10:41:33.367','2019-07-02 10:41:33.367','82','UPS Today Standard','UPS Today Standard',1,1)
,('2019-07-02 10:41:59.984','2019-07-02 10:41:59.984','83','UPS Today Dedicated Courier','UPS Today Dedicated Courier',1,1)
,('2019-07-02 10:42:12.791','2019-07-02 10:42:12.791','84','UPS Today Intercity','UPS Today Intercity',1,1)
,('2019-07-02 10:42:25.560','2019-07-02 10:42:25.560','85','UPS Today Express','UPS Today Express',1,1)
,('2019-07-02 10:42:36.733','2019-07-02 10:42:36.733','86','UPS Today Express Saver','UPS Today Express Saver',1,1)
,('2019-07-02 10:42:53.993','2019-07-02 10:42:53.993','96','UPS Worldwide Express Freight.','UPS Worldwide Express Freight.',1,1);

-- Insert Package Types

INSERT INTO public.integrations_packagetype (created,modified,external_id,name,description,created_by_id,modified_by_id) VALUES
('2019-07-02 11:37:50.410','2019-07-02 11:37:50.410','1','UPS Letter','UPS Letter',1,1)
,('2019-07-02 11:38:24.637','2019-07-02 11:38:24.637','2','Customer Supplied Package','Customer Supplied Package',1,1)
,('2019-07-02 11:41:11.472','2019-07-02 11:41:11.472','3','Tube','Tube',1,1)
,('2019-07-02 11:42:23.775','2019-07-02 11:42:23.775','4','PAK','PAK',1,1)
,('2019-07-02 11:43:16.428','2019-07-02 11:43:16.428','21','UPS Express Box','UPS Express Box',1,1)
,('2019-07-02 11:43:59.506','2019-07-02 11:43:59.506','24','UPS 25KG Box','UPS 25KG Box',1,1)
,('2019-07-02 11:44:12.235','2019-07-02 11:44:12.235','25','UPS 10KG Box','UPS 10KG Box',1,1)
,('2019-07-02 11:44:27.998','2019-07-02 11:44:27.998','30','Pallet','Pallet',1,1)
,('2019-07-02 11:45:09.112','2019-07-02 11:45:09.112','2b','Medium Express Box','Medium Express Box',1,1)
,('2019-07-02 11:44:41.711','2019-07-02 11:45:23.472','2a','Small Express Box','Small Express Box',1,1);

INSERT INTO public.integrations_packagetype (created,modified,external_id,name,description,created_by_id,modified_by_id) VALUES
('2019-07-02 11:45:45.001','2019-07-02 11:45:45.001','2c','Large Express Box','Large Express Box',1,1)
,('2019-07-02 11:46:01.326','2019-07-02 11:46:01.326','56','Flats','Flats',1,1)
,('2019-07-02 11:48:19.818','2019-07-02 11:48:19.818','57','Parcels','Parcels',1,1)
,('2019-07-02 11:48:32.894','2019-07-02 11:48:32.894','58','BPM','BPM',1,1)
,('2019-07-02 11:48:56.320','2019-07-02 11:48:56.320','59','First Class','First Class',1,1)
,('2019-07-02 11:59:35.969','2019-07-02 11:59:35.969','60','Priority','Priority',1,1)
,('2019-07-02 11:59:47.310','2019-07-02 11:59:47.310','61','Machineables','Machineables',1,1)
,('2019-07-02 12:00:00.122','2019-07-02 12:00:00.122','62','Irregulars','Irregulars',1,1)
,('2019-07-02 12:00:12.886','2019-07-02 12:00:12.887','63','Parcel Post','Parcel Post',1,1)
,('2019-07-02 12:00:23.485','2019-07-02 12:00:23.485','64','BPM Parcel','BPM Parcel',1,1);

INSERT INTO public.integrations_packagetype (created,modified,external_id,name,description,created_by_id,modified_by_id) VALUES
('2019-07-02 12:00:36.121','2019-07-02 12:00:36.121','65','Media Mail','Media Mail',1,1)
,('2019-07-02 12:00:48.816','2019-07-02 12:00:48.816','66','BPM Flat','BPM Flat',1,1)
,('2019-07-02 12:01:07.371','2019-07-02 12:01:07.371','67','Standard Flat','Standard Flat',1,1);

--Insert relations PackageTypes VS ServiceTypes
INSERT INTO public.integrations_packagetype_service (packagetype_id,servicetype_id) VALUES
(1,8),(1,1),(1,2),(1,9),(2,1),(2,2),(2,3),(2,7),(2,8),(2,9);

INSERT INTO public.integrations_packagetype_service (packagetype_id,servicetype_id) VALUES
(3,8),(3,1),(3,2),(3,9),(4,8),(4,1),(4,2),(4,9),(5,8),(5,1);

INSERT INTO public.integrations_packagetype_service (packagetype_id,servicetype_id) VALUES
(5,2),(5,9),(10,8),(10,1),(10,2),(10,9),(9,8),(9,1),(9,2),(9,9);

INSERT INTO public.integrations_packagetype_service (packagetype_id,servicetype_id) VALUES
(11,8),(11,1),(11,2),(11,9);

--Insert Integration Types
INSERT INTO integrations_integrationtype (id,created,modified,external_id,"name",active,created_by_id,modified_by_id,"type") VALUES
(1,'2019-11-13 14:34:46.580','2019-11-13 14:34:46.580','A1','SHOPIFY',true,1,1,'ECOMMERCE')
,(2,'2019-11-19 15:52:14.934','2019-11-19 15:52:14.934','A2','UPS',true,1,1,'CONVEYOR')
,(4,'2020-03-27 15:34:00.520','2020-03-27 15:34:00.520','A3','SIIGO',true,1,1,'ECOMMERCE')
;

--Insert Integration Groups
INSERT INTO integrations_integrationgroup (id,created,modified,external_id,"name",active,created_by_id,modified_by_id) VALUES
(1,'2020-04-01 13:51:38.297','2020-04-02 13:26:02.493','A1','PAYMENT MEAN',true,1,1)
,(2,'2020-04-01 13:52:17.673','2020-04-01 13:52:17.673','A2','IVA',true,1,1)
,(3,'2020-04-01 13:52:33.209','2020-04-01 13:52:33.209','A3','RETEFUENTE',true,1,1)
,(4,'2020-04-01 13:53:01.841','2020-04-02 13:26:13.467','A4','INVOICE',true,1,1)
,(5,'2020-04-01 13:53:30.286','2020-04-02 13:26:20.479','A5','WAREHOUSE',true,1,1)
,(6,'2020-04-02 13:28:01.400','2020-04-02 13:28:01.400','A6','COST CENTER',true,1,1)
;

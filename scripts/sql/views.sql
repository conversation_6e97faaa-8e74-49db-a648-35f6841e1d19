--INVENTORY SUMMARY TO LOCATION ---
CREATE OR REPLACE VIEW public.inventory_summary_to_location as
SELECT row_number() OVER () AS id,
    inventory_item.sku_id,
    inventory_item.current_location_id,
    inventory_item.customer_id,
    sum(
        CASE
            WHEN inventory_item.state = 'PRESENT' THEN 1
            ELSE 0
        END) AS total_present,
    sum(
    	case
    		when (r."name" = 'NORMAL'or r."name" is null) and inventory_item.state = 'PRESENT'
    		THEN 1 ELSE 0
    	END) AS total_available,
    sum(
    	case
    		WHEN r."name" = 'SECONDS' and inventory_item.state = 'PRESENT'
    		THEN 1 ELSE 0
    	END)AS total_seconds,
    count(*) AS total
  FROM inventory_item
  left join inventory_location  as location on current_location_id = location.id
  left join inventory_returntype r on inventory_item.type_return_id = r.id
  GROUP BY inventory_item.sku_id, location.type_id, inventory_item.customer_id, inventory_item.current_location_id;

--INVENTORY TOTAL BY LOCATION ---
CREATE OR REPLACE VIEW public.inventory_total_to_location
AS SELECT row_number() OVER () AS id,
    inventory_item.current_location_id,
    inventory_item.customer_id,
    sum(
        CASE
            WHEN inventory_item.state::text = 'PRESENT'::text THEN 1
            ELSE 0
        END) AS total_present,
    sum(
        CASE
            WHEN inventory_item.state::text = 'DISPATCH'::text THEN 1
            ELSE 0
        END) AS total_dispatch,
        sum(
        CASE
            WHEN inventory_item.state::text = 'IN_TRANSFER'::text THEN 1
            ELSE 0
        END) AS total_in_transfer,
    count(*) AS total
   FROM inventory_item
     LEFT JOIN inventory_location location ON inventory_item.current_location_id = location.id
  GROUP BY inventory_item.current_location_id, inventory_item.customer_id;

-- SUMMARY RESERVES ---
CREATE OR REPLACE VIEW public.summary_reserved as
SELECT row_number() OVER () AS id,
inventory_reserve.location_id,
inventory_reserve.customer_id,
line.sku_id,
sum(line.amount) as total_reserve
FROM inventory_reserve
left join inventory_reserveline  as line on inventory_reserve.id = line.reserve_id
group by line.sku_id,inventory_reserve.location_id, inventory_reserve.customer_id;
# -*- coding: utf-8 -*-
import codecs
from django.db import transaction
from ..models import SKU, SKUGroup, Country, StateCountry, City
from common.models import *


def generate_sql_epcs():
    reader = codecs.open("state.csv", 'r', encoding='utf-8')
    new_skus = []

    for line in reader:
        row = line.split(',')

        id = row[0]
        name = row[1]
        country_id = row[2]
        created_by_id = row[3]
        modified_by_id = row[4].replace('\n', '')

        new_state = StateCountry.objects.create(
            id=id,
            name=name,
            country_id=country_id,
            created_by_id=created_by_id,
            modified_by_id=modified_by_id
        )
        new_state.save()

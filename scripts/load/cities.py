# -*- coding: utf-8 -*-
import codecs
from django.db import transaction
from ..models import SKU, SKUGroup, Country, StateCountry, City
from common.models import *


def generate_sql_epcs(group_id, user_id):
    reader = codecs.open("city.csv", 'r', encoding='utf-8')
    new_skus = []

    for line in reader:
        row = line.split(',')

        id = row[0]
        name = row[1]
        state_id = row[2]
        country_id = row[3]
        created_by_id = row[4]
        modified_by_id = row[5].replace('\n', '')

        new_city = City.objects.create(
            id=id,
            name=name,
            state_id=state_id,
            country_id=country_id,
            created_by_id=created_by_id,
            modified_by_id=modified_by_id
        )
        new_city.save()

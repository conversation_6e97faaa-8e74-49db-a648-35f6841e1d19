# -*- coding: utf-8 -*-
import codecs
from django.db import transaction
from ..models import SKU, SKUGroup, Country, StateCountry, City
from common.models import *


def generate_sql_epcs(group_id, user_id):
    reader = codecs.open("country.csv", 'r', encoding='utf-8')
    new_skus = []

    for line in reader:
        row = line.split(',')

        id = row[0]
        name = row[1]
        iso3 = row[2]
        iso2 = row[3]
        phonecode = row[4]
        capital = row[5]
        currency = row[6]
        created_by_id = row[7]
        modified_by_id = row[8].replace('\n', '')

        new_country = Country.objects.create(
            id=id,
            name=name,
            iso3=iso3,
            iso2=iso2,
            phonecode=phonecode,
            capital=capital,
            currency=currency,
            created_by_id=created_by_id,
            modified_by_id=modified_by_id
        )
        new_country.save()

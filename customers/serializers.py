# -*- coding: utf-8 -*-
from common.serializers import *

from .models import *


# Customer Serializers.
class CustomerNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = ('id', 'name', 'type_customer', 'identification', 'parent')


class CustomerSerializer(BaseSerializer):
    class Meta:
        model = Customer
        fields = '__all__'


class CustomerDetailNestedSerializer(BaseSerializer):
    class Meta:
        model = Customer
        fields = ('id', 'name', 'type_customer')


class CustomerNestedRelatedFieldSerializer(serializers.RelatedField):
    """
    Get Customer nested serializer by int value
    """

    def to_representation(self, value):
        """
        Serialize tagged objects to a simple textual representation.
        """
        customer_object = Customer.objects.get(pk=value)
        serializer = CustomerNestedSerializer(customer_object)
        return serializer.data

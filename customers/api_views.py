# -*- coding: utf-8 -*-
from django_filters.rest_framework import DjangoFilterBackend
from requests import Response
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.filters import SearchFilter, OrderingFilter

from .filters import *
from .serializers import *


class CustomerViewSet(viewsets.ModelViewSet):
    """
    retrieve:
    Return the given contact.

    list:
    Return a list of all the existing contacts.

    create:
    Create a new contact instance.

    update:
    Update an existing contact instance.

    partial_update:
    Update partial an existing contact instance.

    delete:
    Delete  an existing contact instance.
    """
    __basic_fields = ('external_id', 'id', 'name', 'identification')
    serializer_class = CustomerSerializer
    queryset = Customer.objects.all()
    filter_class = CustomerFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'
    search_fields = __basic_fields

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists Customer History
        """
        olpn_object = self.get_object()
        olpns = Customer.objects.get(pk=olpn_object.pk)
        query = olpns.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = CustomerNestedSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = CustomerNestedSerializer(query, many=True)
        return Response(serializer.data)

# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from common.models import *
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db import models
from simple_history.models import HistoricalRecords


# Create your models here.


class Customer(BaseModel):
    CUSTOMER_TYPE = (
        ("CLIENT", "Client"),
    )
    IDENTIFICATION_TYPE = (
        ("NIT", "Nit"),
        ("SHIPPER_NUMBER", "Shipper Number")
    )
    external_id = models.CharField(max_length=15, null=True, blank=True, unique=True)
    name = models.CharField(max_length=50)
    display_name = models.CharField(max_length=50)
    identification = models.CharField(max_length=14, null=True, blank=True)
    type_customer = models.CharField(max_length=20, choices=CUSTOMER_TYPE, default="CLIENT")
    type_identification = models.CharField(max_length=20, choices=IDENTIFICATION_TYPE, default="NIT",
                                           null=True, blank=True)
    properties = JSONField(default=dict)
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return "{}".format(self.name)

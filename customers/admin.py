# -*- coding: utf-8 -*-
from django.apps import apps
from django.contrib import admin

from .models import *

# Register your models here.
# auto-register all models
app = apps.get_app_config('customers')


class CustomerAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name', 'identification', 'type_customer', 'parent')


admin.site.register(Customer, CustomerAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-10-10 14:32
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core_config', '0015_customermetadata'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='historicaljsonmapconfig',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='historicaljsonmapconfig',
            name='json_map',
        ),
        migrations.RemoveField(
            model_name='historicaljsonmapconfig',
            name='model',
        ),
        migrations.AddField(
            model_name='historicaljsonmapconfig',
            name='json_schema',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, help_text='https://json-schema.org/understanding-json-schema/', null=True),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name='historicaljsonmapconfig',
            name='model_id',
            field=models.CharField(choices=[('STORE_ORDER', 'Store Order'), ('STORE_ORDER_LINE', 'Store Order Line'), ('PRODUCTION_ORDER', 'Production order'), ('PURCHASE_ORDER', 'Purchase order')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='jsonmapconfig',
            name='json_schema',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, help_text='https://json-schema.org/understanding-json-schema/', null=True),
        ),
        migrations.AddField(
            model_name='jsonmapconfig',
            name='model_id',
            field=models.CharField(choices=[('STORE_ORDER', 'Store Order'), ('STORE_ORDER_LINE', 'Store Order Line'), ('PRODUCTION_ORDER', 'Production order'), ('PURCHASE_ORDER', 'Purchase order')], max_length=20, null=True),
        ),
        migrations.RemoveField(
            model_name='jsonmapconfig',
            name='customer',
        ),
        migrations.RemoveField(
            model_name='jsonmapconfig',
            name='json_map',
        ),
        migrations.RemoveField(
            model_name='jsonmapconfig',
            name='model',
        ),
        migrations.AlterUniqueTogether(
            name='jsonmapconfig',
            unique_together=set([('model_id', 'model_field')]),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2021-06-15 17:08
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0007_auto_20190712_1606'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core_config', '0009_userconfig_external_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalJsonMapConfig',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('model', models.Char<PERSON><PERSON>(choices=[('PRODUCTION_ORDER', 'Production order'), ('PURCHASE_ORDER', 'Purchase order')], max_length=20, null=True)),
                ('model_field', models.CharField(choices=[('PROPERTIES', 'Properties')], max_length=20, null=True)),
                ('json_map', django.contrib.postgres.fields.jsonb.JSONField(help_text='[{"key_name": "-- name --", "key_display_name": "-- display name --"}]')),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical json map config',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='JsonMapConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('model', models.CharField(choices=[('PRODUCTION_ORDER', 'Production order'), ('PURCHASE_ORDER', 'Purchase order')], max_length=20, null=True)),
                ('model_field', models.CharField(choices=[('PROPERTIES', 'Properties')], max_length=20, null=True)),
                ('json_map', django.contrib.postgres.fields.jsonb.JSONField(help_text='[{"key_name": "-- name --", "key_display_name": "-- display name --"}]')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
    ]

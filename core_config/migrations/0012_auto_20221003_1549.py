# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-10-03 20:49
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core_config', '0011_auto_20211019_1233'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicaljsonmapconfig',
            name='params',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AddField(
            model_name='jsonmapconfig',
            name='params',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={}, null=True),
        ),
        migrations.AlterField(
            model_name='historicaljsonmapconfig',
            name='model',
            field=models.CharField(choices=[('STORE_ORDER', 'Store order'), ('STORE_ORDER_LINE', 'Store order line'), ('PRODUCTION_ORDER', 'Production order'), ('PURCHASE_ORDER', 'Purchase order')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='historicaljsonmapconfig',
            name='model_field',
            field=models.CharField(choices=[('PROPERTIES', 'Properties'), ('PARAMS', 'Params')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='jsonmapconfig',
            name='model',
            field=models.CharField(choices=[('STORE_ORDER', 'Store order'), ('STORE_ORDER_LINE', 'Store order line'), ('PRODUCTION_ORDER', 'Production order'), ('PURCHASE_ORDER', 'Purchase order')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='jsonmapconfig',
            name='model_field',
            field=models.CharField(choices=[('PROPERTIES', 'Properties'), ('PARAMS', 'Params')], max_length=20, null=True),
        ),
    ]

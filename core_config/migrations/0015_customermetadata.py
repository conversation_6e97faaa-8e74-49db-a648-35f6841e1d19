# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-10-10 01:29
from __future__ import unicode_literals

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core_config', '0014_auto_20221003_1552'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerMetadata',
            fields=[
                ('namespace', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('business_name', models.Char<PERSON>ield(max_length=140)),
                ('company_prefixes', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), size=10)),
                ('customer_number', models.CharField(max_length=3)),
                ('epc_hex_read_length', models.IntegerField(default=24)),
                ('epc_hex_write_length', models.Integer<PERSON>ield(default=24)),
                ('default_template_id', models.IntegerField(default=1)),
                ('customer_logo', models.ImageField(blank=True, null=True, upload_to='customer_metadata')),
                ('barcode_standard', models.CharField(choices=[('GS1', 'GS1'), ('APES_STANDARD', 'Apes Standard')], default='APES_STANDARD', max_length=15)),
            ],
            options={
                'ordering': ['namespace'],
            },
        ),
    ]

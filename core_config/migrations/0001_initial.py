# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 17:05
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0002_auto_20190415_1203'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerMetadata',
            fields=[
                ('namespace', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('business_name', models.CharField(max_length=140)),
                ('company_prefixes', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), size=10)),
                ('customer_number', models.Char<PERSON>ield(max_length=3)),
                ('epc_hex_read_length', models.Integer<PERSON>ield(default=24)),
                ('epc_hex_write_length', models.IntegerField(default=24)),
                ('default_template_id', models.IntegerField(default=1)),
                ('customer_logo', models.ImageField(blank=True, null=True, upload_to='customer_metadata')),
            ],
            options={
                'ordering': ['namespace'],
            },
        ),
        migrations.CreateModel(
            name='KongModule',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=15, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.CharField(blank=True, max_length=140, null=True)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='UserConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('active_mobile_modules', models.ManyToManyField(related_name='related_mobile_configs', to='core_config.KongModule')),
                ('active_web_modules', models.ManyToManyField(related_name='related_web_configs', to='core_config.KongModule')),
                ('audit_locations', models.ManyToManyField(to='inventory.Location')),
                ('default_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='users_asociated', to='inventory.Location')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='configs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
    ]

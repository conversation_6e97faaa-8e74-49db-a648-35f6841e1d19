# -*- coding: utf-8 -*-
from common.serializers import *
from inventory.serializers import LocationNestedSerializer
from customers.serializers import CustomerNestedSerializer
from rest_framework import serializers

from .models import *


class KongModuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = KongModule
        fields = '__all__'


class KongModuleNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = KongModule
        fields = ('code', 'name')


class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = '__all__'


class RoleNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ('id', 'external_id', 'name')


class UserConfigSerializer(serializers.ModelSerializer):
    active_mobile_modules = KongModuleSerializer(many=True, read_only=True)
    active_web_modules = KongModuleSerializer(many=True, read_only=True)
    default_location = LocationNestedSerializer(read_only=True)
    audit_locations = LocationNestedSerializer(read_only=True, many=True)
    customer = CustomerNestedSerializer(read_only=True)
    role = RoleNestedSerializer(read_only=True)
    user = UserNestedSerializer(read_only=True)

    role_id = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all(), source='role',
                                                 write_only=True, required=False)
    user_id = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), source='user',
                                                 write_only=True, required=False)
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(), source='customer',
                                                     write_only=True, required=False)
    audit_locations_ids = serializers.ListSerializer(write_only=True, child=serializers.CharField(max_length=150))

    class Meta:
        model = UserConfig
        fields = '__all__'


class UserSerializer(BaseSerializer, serializers.ModelSerializer):
    configs = UserConfigSerializer()

    class Meta:
        ref_name = "CoreConfigUser"
        model = User
        fields = ['id', 'last_login', 'username', 'first_name', 'last_name',
                  'email', 'is_active', 'groups', 'configs']


class CustomerMetadataSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomerMetadata
        fields = '__all__'


class JsonMapConfigPropertiesSerializer(BaseSerializer):
    properties = serializers.JSONField()

    class Meta:
        model = JsonMapConfig
        fields = ('model_id', 'model_field', 'json_schema',
                  'properties')
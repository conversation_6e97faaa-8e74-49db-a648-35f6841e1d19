from core_config.api_views import RoleViewSet, UserViewSet, JsonMapConfigViewSet, KongModuleViewSet,\
    UserConfigViewSet
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register(r'roles', RoleViewSet, 'role')
router.register(r'users', UserViewSet, 'user')
router.register(r'json-map-configs', JsonMapConfigViewSet, 'json-map-config')
router.register(r'kong-modules', KongModuleViewSet, 'kong-module')
router.register(r'user-configs', UserConfigViewSet, 'user-configs')

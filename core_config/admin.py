# -*- coding: utf-8 -*-
from django.apps import apps
from django.contrib import admin
from dynamic_raw_id.admin import DynamicRawIDMixin

from core_config.models import Role, UserConfig, JsonMapConfig

# auto-register all models
app = apps.get_app_config('core_config')


class UserConfigAdmin(DynamicRawIDMixin, admin.ModelAdmin):
    list_display = ("id", "user", "default_location", "customer", "role")
    raw_id_fields = ("default_location",)
    dynamic_raw_id_fields = ("audit_locations",)
    filter_horizontal = ("active_mobile_modules", "active_web_modules")


class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'name')


class JsonMapConfigAdmin(admin.ModelAdmin):
    list_display = ('id', 'model_id', 'model_field')
    search_fields = ('model_id', 'model_field')


admin.site.register(Role, UserRoleAdmin)
admin.site.register(UserConfig, UserConfigAdmin)
admin.site.register(JsonMapConfig, JsonMapConfigAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

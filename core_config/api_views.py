# -*- coding: utf-8 -*-
from django.db import transaction
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from .serializers import *
from .filters import *
from core_config.schemas import JsonMapConfigViewSchema


class RoleViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A simple ViewSet for viewing users roles.
    """
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    filter_class = RoleFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    """
    def generate_temporary_token(user_id):
        User = get_user_model()
        user = User.objects.get(id=user_id)
        refresh = RefreshToken.for_user(user)
        token = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }
        return token
    """


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    filter_class = UserFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    @action(methods=['get'], detail=False)
    def current_user(self, request):
        """
        Returns current user authenticated
        """
        serializer = UserSerializer(request.user, context={'request': request})
        return Response(serializer.data)

    """
    @action(methods=['post'], detail=False)
    def get_auth_token(self, request):
        Returns current user authenticated
        user = User.objects.get(pk=request.user.id)
        temporary_token = generate_temporary_token(user_id=user.pk)

        return Response(status=status.HTTP_202_ACCEPTED)
    """

class JsonMapConfigViewSet(viewsets.GenericViewSet):
    """
    A simple ViewSet for viewing json map configs.
    """
    queryset = JsonMapConfig.objects.all()
    serializer_class = JsonMapConfigPropertiesSerializer
    filter_class = JsonMapConfigFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    @action(methods=['get'], detail=False, schema=JsonMapConfigViewSchema())
    def summary_keys(self, request):
        """
        Get valid keys from selected model
        """
        queryset = self.filter_queryset(self.get_queryset())
        summary_keys = []
        summary_key = {}
        for line in queryset:
            json_properties = line.json_schema.get("properties", {})
            for key_name, key_properties in json_properties.items():
                summary_key["model"] = line.model_id
                summary_key["model_field"] = line.model_field
                summary_key["key_name"] = key_name
                summary_key["choices"] = key_properties.get("choices", [])
                summary_keys.append(summary_key)

        page = self.paginate_queryset(summary_keys)
        return self.get_paginated_response(page)


class KongModuleViewSet(viewsets.ModelViewSet):
    """
    A simple ViewSet for viewing kong modules.
    """
    __basic_fields = ('code', 'name')
    queryset = KongModule.objects.all()
    serializer_class = KongModuleSerializer
    filter_class = KongModuleFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'
    search_fields = __basic_fields


class UserConfigViewSet(viewsets.ModelViewSet):
    """
    A simple ViewSet for viewing kong modules.
    """
    __basic_fields = ()
    queryset = UserConfig.objects.all()
    serializer_class = UserConfigSerializer
    filter_class = UserConfigFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'
    search_fields = __basic_fields

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        audit_locations = serializer.initial_data["audit_locations_ids"]
        try:
            with transaction.atomic():
                user_config = serializer.save()
            if audit_locations:
                for audit_location in audit_locations:
                    audit_location_object = Location.objects.filter(pk=audit_location).first()
                user_config = UserConfig.audit_locations.add(audit_location_object)
        except Exception as error:
            transaction.rollback()
            body = {'result': error.args[0]}
            return Response(data=body, status=status.HTTP_406_NOT_ACCEPTABLE)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
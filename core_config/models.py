# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from simple_history.models import HistoricalRecords

from django.contrib.postgres.fields import ArrayField
from common.models import *
from django.db import models
from inventory.models import Location
from customers.models import Customer


class CustomerMetadata(models.Model):
    BARCODE_STANDARD = (
        ('GS1', 'GS1'),
        ('APES_STANDARD', 'Apes Standard')
    )

    namespace = models.CharField(max_length=50, primary_key=True)
    business_name = models.CharField(max_length=140)
    company_prefixes = ArrayField(models.IntegerField(), size=10)
    customer_number = models.CharField(max_length=3)
    epc_hex_read_length = models.IntegerField(default=24)
    epc_hex_write_length = models.IntegerField(default=24)
    default_template_id = models.IntegerField(default=1)
    customer_logo = models.ImageField(upload_to='customer_metadata', null=True, blank=True)
    barcode_standard = models.CharField(max_length=15, choices=BARCODE_STANDARD, default="APES_STANDARD")

    class Meta:
        ordering = ['namespace']

    def __str__(self):
        return '{}-{}'.format(self.namespace, self.business_name)


class KongModule(models.Model):
    code = models.CharField(max_length=15, unique=True)
    name = models.CharField(max_length=100)
    description = models.CharField(max_length=140, null=True, blank=True)

    class Meta:
        ordering = ['code']

    def __str__(self):
        return '{} {}'.format(self.code, self.name)


class Role(BaseModel):
    external_id = models.CharField(max_length=25, null=True, blank=True, unique=True)
    name = models.CharField(max_length=100)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{} {}'.format(self.external_id, self.name)


class UserConfig(models.Model):
    user = models.OneToOneField(User, related_name="configs", on_delete=models.PROTECT)
    external_id = models.CharField(max_length=15, null=True, blank=True, unique=True)
    default_location = models.ForeignKey(Location, related_name="users_asociated", null=True, blank=True, on_delete=models.PROTECT)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    audit_locations = models.ManyToManyField(Location)
    active_mobile_modules = models.ManyToManyField(KongModule, related_name="related_mobile_configs")
    active_web_modules = models.ManyToManyField(KongModule, related_name="related_web_configs")
    role = models.ForeignKey(Role, null=True, blank=True, on_delete=models.PROTECT)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{} {}'.format(self.user, self.default_location)


class JsonMapConfig(BaseModel):
    MODEL_IDS = (
        ('STORE_ORDER', 'Store Order'),
        ('STORE_ORDER_LINE', 'Store Order Line'),
        ('PRODUCTION_ORDER', 'Production order'),
        ('PURCHASE_ORDER', 'Purchase order')
    )
    MODEL_FIELDS = (
        ('PROPERTIES', 'Properties'),
        ('PARAMS', 'Params')
    )
    model_id = models.CharField(max_length=20, choices=MODEL_IDS, null=True, blank=False)
    model_field = models.CharField(max_length=20, choices=MODEL_FIELDS, null=True, blank=False)
    json_schema = JSONField(help_text='https://json-schema.org/understanding-json-schema/',
                            null=True, blank=True, default=dict)
    params = JSONField(null=True, blank=True, default=dict)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']
        unique_together = ('model_id', 'model_field')

    def __str__(self):
        return '{}-{}'.format(self.model_id, self.model_field)


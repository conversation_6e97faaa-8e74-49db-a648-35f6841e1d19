# -*- coding: utf-8 -*-

from __future__ import unicode_literals

import django_filters
from common.filters import *
from common.models import *
from core_config.models import *


class RoleFilter(django_filters.FilterSet):
    location_id = django_filters.CharFilter(field_name="configs__default_location")

    class Meta:
        model = Role
        fields = ['external_id', 'name']


class UserFilter(django_filters.FilterSet):
    location_id = django_filters.CharFilter(field_name="configs__default_location")
    role_id = django_filters.CharFilter(field_name="configs__role_id")
    role__name = django_filters.CharFilter(field_name="configs__role__name")

    class Meta:
        model = User
        fields = ['location_id', 'role_id', 'role__name']


class JsonMapConfigFilter(django_filters.FilterSet):

    class Meta:
        model = JsonMapConfig
        fields = ['model_id', 'model_field']


class KongModuleFilter(django_filters.FilterSet):

    class Meta:
        model = KongModule
        fields = ['id', 'code', 'name', 'description']


class UserConfigFilter(django_filters.FilterSet):

    class Meta:
        model = UserConfig
        fields = ['id', 'default_location', 'audit_locations', 'role',]
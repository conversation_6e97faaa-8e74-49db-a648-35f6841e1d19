# -*- coding: utf-8 -*-

from common.models import *
from core_config.models import UserConfig


def get_system_user():
    """
    Retrieves designated user for system operations
    :return:
    """
    system_user = User.objects.get(username='SYSTEM_USER')
    return system_user


def get_system_user_id():
    """
    Retrieves designated user ID for system operations
    :return:
    """
    system_user = User.objects.get(username='SYSTEM_USER')
    return system_user.pk


def get_user_config(user_id):
    """
    Retrieves designated user core_config OBJECT for system operations
    :return:
    """

    user_config = UserConfig.objects.get(user_id=user_id)
    return user_config

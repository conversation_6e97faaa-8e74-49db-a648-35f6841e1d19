#!/bin/bash

# Script de configuración inicial para Kong RFID WMS
# Autor: TechnoApes
# Fecha: $(date +%Y-%m-%d)

set -e  # Salir en caso de error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para mostrar mensajes
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Función para leer input con valor por defecto
read_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " value
        if [ -z "$value" ]; then
            value="$default"
        fi
    else
        read -p "$prompt: " value
        while [ -z "$value" ]; do
            log_warning "Este campo es obligatorio"
            read -p "$prompt: " value
        done
    fi
    
    eval "$var_name='$value'"
}

# Función para leer contraseña
read_password() {
    local prompt="$1"
    local var_name="$2"
    
    while true; do
        read -s -p "$prompt: " password
        echo
        read -s -p "Confirmar contraseña: " password_confirm
        echo
        
        if [ "$password" = "$password_confirm" ]; then
            eval "$var_name='$password'"
            break
        else
            log_error "Las contraseñas no coinciden. Intente nuevamente."
        fi
    done
}

# Obtener directorio raíz del proyecto
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$PROJECT_ROOT/config.json"

log_info "=== Configuración inicial de Kong RFID WMS ==="
log_info "Directorio del proyecto: $PROJECT_ROOT"

# 1. Configurar variables de entorno
log_info "\n🔧 Configuración de variables de entorno"
read_with_default "DJANGO_SETTINGS_MODULE" "main.conf.local" "DJANGO_SETTINGS_MODULE"
read_with_default "KONG_SETTINGS_MODULE" "$CONFIG_FILE" "KONG_SETTINGS_MODULE"

# Exportar variables
export DJANGO_SETTINGS_MODULE
export KONG_SETTINGS_MODULE

log_success "Variables de entorno configuradas:"
log_success "  DJANGO_SETTINGS_MODULE: $DJANGO_SETTINGS_MODULE"
log_success "  KONG_SETTINGS_MODULE: $KONG_SETTINGS_MODULE"

# 2. Configurar parámetros de la base de datos y aplicación
log_info "\n🗄️ Configuración de la aplicación y base de datos"
read_with_default "APP_NAMESPACE" "APES-RFID-DEV" "APP_NAMESPACE"
read_with_default "DB_HOST" "" "DB_HOST"
read_with_default "DB_NAME" "" "DB_NAME"
read_with_default "DB_USER" "" "DB_USER"
read_password "DB_PASSWORD" "DB_PASSWORD"

# 3. Actualizar config.json
log_info "\n📝 Actualizando archivo config.json..."

# Crear backup del config.json
cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
log_info "Backup creado: $CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"

# Usar Python para actualizar el JSON de forma segura
python3 << EOF
import json
import sys

try:
    # Leer config actual
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    
    # Actualizar valores
    config['APP_NAMESPACE'] = '$APP_NAMESPACE'
    config['DB_HOST'] = '$DB_HOST'
    config['DB_NAME'] = '$DB_NAME'
    config['DB_USER'] = '$DB_USER'
    config['DB_PASSWORD'] = '$DB_PASSWORD'
    
    # Escribir config actualizado
    with open('$CONFIG_FILE', 'w') as f:
        json.dump(config, f, indent=4)
    
    print("✅ Archivo config.json actualizado correctamente")
    
except Exception as e:
    print(f"❌ Error actualizando config.json: {e}")
    sys.exit(1)
EOF

if [ $? -ne 0 ]; then
    log_error "Error actualizando config.json"
    exit 1
fi

# 4. Verificar conexión a la base de datos
log_info "\n📡 Verificando conexión a la base de datos..."
if python manage.py check --database=default; then
    log_success "Conexión a la base de datos exitosa"
else
    log_error "Error de conexión a la base de datos"
    exit 1
fi

# 5. Crear extensiones de PostgreSQL
log_info "\n🔧 Creando extensiones de PostgreSQL..."
python manage.py dbshell << 'EOF' || log_warning "No se pudieron crear todas las extensiones (puede ser por permisos)"
CREATE EXTENSION IF NOT EXISTS hstore;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
\q
EOF

# 6. Aplicar migraciones principales de Django
log_info "\n🚀 Aplicando migraciones principales de Django..."
python manage.py migrate

# 7. Obtener módulos del config.json y aplicar migraciones específicas
log_info "\n📦 Aplicando migraciones de módulos específicos..."

# Obtener APP_MODULES del config.json
APP_MODULES=$(python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)
    modules = config.get('APP_MODULES', [])
    print(' '.join(modules))
")

if [ -n "$APP_MODULES" ]; then
    for module in $APP_MODULES; do
        log_info "  Aplicando migraciones para: $module"
        if python manage.py migrate "$module"; then
            log_success "    ✅ $module migrado correctamente"
        else
            log_warning "    ⚠️ Error migrando $module (puede que no tenga migraciones)"
        fi
    done
else
    log_warning "No se encontraron APP_MODULES en config.json"
fi

# 8. Recolectar archivos estáticos
log_info "\n📦 Recolectando archivos estáticos..."
python manage.py collectstatic --noinput

# 9. Configurar superusuario
log_info "\n👤 Configuración del superusuario"
read_with_default "Username del superusuario" "apes_admin" "ADMIN_USERNAME"
read_with_default "Email del superusuario" "<EMAIL>" "ADMIN_EMAIL"
read_password "Password del superusuario" "ADMIN_PASSWORD"

# 10. Crear superusuario
log_info "\n� Creando superusuario..."
if python create_superuser.py --username "$ADMIN_USERNAME" --email "$ADMIN_EMAIL" --password "$ADMIN_PASSWORD"; then
    log_success "Superusuario creado exitosamente"
else
    log_error "Error creando superusuario"
    exit 1
fi

# 11. Verificar estado final
log_info "\n🔍 Verificando estado final del sistema..."

# Verificar migraciones
log_info "Estado de migraciones:"
python manage.py showmigrations | head -20

# Verificar superusuario
log_info "Verificando superusuario:"
python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
user = User.objects.filter(username='$ADMIN_USERNAME').first()
if user:
    print(f'✅ Superusuario encontrado: {user.username} ({user.email})')
else:
    print('❌ Superusuario no encontrado')
"

# 12. Limpiar credenciales del config.json
log_info "\n🧹 Limpiando credenciales del archivo config.json por seguridad..."

# Crear backup final con las credenciales
cp "$CONFIG_FILE" "$CONFIG_FILE.with_credentials.$(date +%Y%m%d_%H%M%S)"
log_info "Backup con credenciales guardado en: $CONFIG_FILE.with_credentials.$(date +%Y%m%d_%H%M%S)"

# Limpiar credenciales del config.json
python3 << EOF
import json
import sys

try:
    # Leer config actual
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    
    # Limpiar valores sensibles
    config['APP_NAMESPACE'] = ''
    config['DB_HOST'] = ''
    config['DB_NAME'] = ''
    config['DB_USER'] = ''
    config['DB_PASSWORD'] = ''
    
    # Escribir config limpio
    with open('$CONFIG_FILE', 'w') as f:
        json.dump(config, f, indent=4)
    
    print("✅ Credenciales limpiadas del config.json")
    
except Exception as e:
    print(f"❌ Error limpiando config.json: {e}")
    sys.exit(1)
EOF

if [ $? -eq 0 ]; then
    log_success "Credenciales removidas del config.json por seguridad"
else
    log_error "Error limpiando credenciales del config.json"
fi

# 13. Resumen final
log_success "\n🎉 ¡Configuración completada exitosamente!"
log_success "======================================="
log_success "Configuración del proyecto:"
log_success "  - APP_NAMESPACE: $APP_NAMESPACE"
log_success "  - DB_HOST: $DB_HOST"
log_success "  - DB_NAME: $DB_NAME"
log_success "  - DB_USER: $DB_USER"
log_success "  - Superusuario: $ADMIN_USERNAME ($ADMIN_EMAIL)"
log_success ""
log_warning "🔒 SEGURIDAD: Las credenciales han sido removidas del config.json"
log_warning "   Si necesitas las credenciales, revisa los archivos de backup creados"
log_success ""
log_success "Para iniciar el servidor ejecuta:"
log_success "  export DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS_MODULE"
log_success "  export KONG_SETTINGS_MODULE=$KONG_SETTINGS_MODULE"
log_success "  ./init_api.sh --d-settings=$DJANGO_SETTINGS_MODULE --k-settings=$KONG_SETTINGS_MODULE --kong-mode=web"
log_success ""
log_success "O para desarrollo:"
log_success "  export DJANGO_SETTINGS_MODULE=$DJANGO_SETTINGS_MODULE"
log_success "  export KONG_SETTINGS_MODULE=$KONG_SETTINGS_MODULE"
log_success "  python manage.py runserver"

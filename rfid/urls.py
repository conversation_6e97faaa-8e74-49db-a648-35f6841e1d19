# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.urls import path
from rest_framework.routers import DefaultRouter

from .api_views import *

router = DefaultRouter()
router.register(r'nodes', NodeViewSet, 'node')
router.register(r'readers', ReaderViewSet, 'reader')
router.register(r'print-orders', PrintOrderViewSet, 'print-order')
router.register(r'print-templates', PrintTemplateViewSet, 'print-template')
router.register(r'rfid-production-orders', RFIDProductionOrderViewSet, 'rfid-production-order')
router.register(r'rfid-purchase-orders', RFIDPurchaseOrderViewSet, 'rfid-purchase-order')


urlpatterns = router.urls

urlpatterns += [
    path('rfid-activity/', RFIDActivityView.as_view(), name='rfid-activity'),
    path('rfid-mobile-activity/', RFIDActivityMobileView.as_view(), name='rfid-mobile-activity'),
]

# -*- coding: utf-8 -*-
from rest_framework.exceptions import APIException


class StatusUnavailableForProcess(APIException):
    status_code = 400
    default_detail = 'The status of print order should by ISSUED,item_ids_generated in true' \
                     ' and serial_numbers_generated in true.'
    default_code = 'A000'


class StatusUnavailableForPrint(APIException):
    status_code = 401
    default_detail = 'The status of print order should by IN_PROCESS.'
    default_code = 'A001'

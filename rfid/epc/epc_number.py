# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from abc import abstractmethod

from rfid.epc.utils.field import FieldDictionary


class EPCNumber(object):
    """
    Base Class for all EPC
    """

    def __init__(self):
        """Initializes the data for the EPCNumber Class"""
        self._field_dict = FieldDictionary()
        self._bits = None
        self._encoding_type = ''
        self._serial_number = 0
        self._pack_string_format = None

    @abstractmethod
    def load_fields(self):
        """
        Loads Fields for the Derived EPC Number. This method must be overridden in the derived class
        and should not be called from the base class as it will throw an exception. 
        """
        pass

    def get_field_value(self, field_name):
        """
        Gets the Value of the supplied fieldName
        example: serialNumber = sgtin.getFieldValue("SerialNumber")
        """
        field = self._field_dict[field_name]
        return field.field_value

    def get_field(self, field_name):
        """
        Gets the Value of the supplied fieldName
        example: serialNumber = sgtin.getField("SerialNumber")
        """
        return self._field_dict[field_name]

    @abstractmethod
    def encode(self, *args, **kwargs):
        """
        Encodes an EPC number with the given fields.  
        This method must be overridden in a derived class and should not be called from 
        the base class as it will throw an exception.
        """
        pass

    def encoding_type(self):
        """Returns the Type of EPC Encoding represented by the derivative. eg. SGTIN96, SSCC96 etc """
        return self._encoding_type

    @property
    def pack_string_format(self):
        """ The current pack string format for the encoding """
        return self._pack_string_format

    @pack_string_format.setter
    def pack_string_format(self, value):
        """ Set the  current pack string format  for the encoding """
        self._pack_string_format = value

    @property
    def bits(self):
        """ The current bits for the encoding """
        return self._bits

    @bits.setter
    def bits(self, value):
        """ Set the  current bits for the encoding """
        self._bits = value

    @property
    def serial_number(self):
        """ The current serial number for the encoding  """
        return self._serial_number

    @serial_number.setter
    def serial_number(self, value):
        """ Set the  current serial number for the encoding  """
        self._serial_number = value

    @abstractmethod
    def from_tag_uri(self, tag_uri):
        """Override in derived class. Parses the EPC from a TagURI"""
        pass

    @abstractmethod
    def to_tag_uri(self):
        """Override in derived class"""
        pass

    @abstractmethod
    def from_uri(self, uri):
        """Parses the EPC from a Tag URI"""
        pass

    @abstractmethod
    def to_uri(self):
        """Override in derived class"""
        pass

    def from_hex(self, hex_value):
        """Parses the EPC from a Hex Value"""
        binary_epc = bin(int(hex_value, 16))
        return self.from_binary(binary_epc)

    def to_hex(self):
        """Returns a hex representation of the EPCNumber"""
        h = hex(int(self._bits, 2))
        return h[2:].upper()

    @abstractmethod
    def from_gs1(self, gs1_value):
        """Override in derived class"""
        pass

    @abstractmethod
    def to_gs1(self):
        """Override in derived class"""
        pass

    @abstractmethod
    def from_binary(self, bin_value):
        """Override in derived class"""
        pass

    def to_binary(self):
        """Returns a binary representation of the EPCNumber"""
        return self._bits

    @abstractmethod
    def from_xml(self, xml_value):
        """Meant to be overriden in the derived class"""
        pass

    @abstractmethod
    def to_xml(self):
        """Meant to be overriden in the derived class"""
        pass

    @abstractmethod
    def from_dictionary(self, dictionary):
        """Meant to be overriden in the derived class"""
        pass

    @abstractmethod
    def to_dictionary(self):
        """Meant to be overriden in the derived class"""
        pass

    def format(self, format="HEX"):
        """
        Returns a string representation of the EPC Number in the provided format
        """
        if format.lower() == "hex":
            return self.to_hex()
        elif format.lower() == "xml":
            return self.to_xml()
        elif format.lower() == "dict":
            return self.to_dictionary()
        elif format.lower() == "dictionary":
            return self.to_dictionary()
        elif format.lower() == "binary":
            return self.to_binary()
        elif format.lower() == "bin":
            return self.to_binary()
        elif format.lower() == "tag":
            return self.to_tag_uri()
        elif format.lower() == "epctag":
            return self.to_tag_uri()
        elif format.lower() == "epctaguri":
            return self.to_tag_uri()
        elif format.lower() == "epcuri":
            return self.to_uri()
        elif format.lower() == "uri":
            return self.to_uri()

    def increment(self, count=1):
        cur = self.serial_number + count
        self.serial_number = cur
        return cur

    def decrement(self, count=1):
        cur = self.serial_number - count
        if cur < 0:
            raise ValueError("Serial number field may not be below 0.")
        self.serial_number = cur
        return cur

    @abstractmethod
    def update_bits(self):
        """ Meant to be overridden in the derived class """
        pass


def get_encoding_from_hex(hex_value):
    """ Returns the proper encoding name and  size from hex epc e.g. SGTIN, SSCC etc."""
    header = hex_value[:2]
    if header == '2C':
        return "GDTI", 96
    elif header == '2D':
        return "GSRN", 96
    elif header == '2E':
        return "RESERVED", 0
    elif header == '2F':
        return "USDOD", 96
    elif header == '30':
        return "SGTIN", 96
    elif header == '31':
        return "SSCC", 96
    elif header == '32':
        return "SGLN", 96
    elif header == '33':
        return "GRAI", 96
    elif header == '34':
        return "GIAI", 96
    elif header == '35':
        return "GID", 96
    elif header == '36':
        return "SGTIN", 198
    elif header == '37':
        return "GRAI", 170
    elif header == '38':
        return "GIAI", 202
    elif header == '39':
        return "SGLN", 195
    elif header == '3A':
        return "GDTI", 113
    elif header == '3B':
        return "ADI", -1
    else:
        return None
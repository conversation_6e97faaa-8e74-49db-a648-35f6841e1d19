# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import collections

import numpy as np
from bitstring import BitArray


class FieldDictionary(collections.OrderedDict):
    pass


class Field(object):
    """
    Field Class represents a single field within an EPC Encoding
    """

    def __init__(self, field_name=None, offset=None, bit_length=None, ordinal=None, field_value=None, digit_length=None,
                 is_padded=False):

        self._field_name = field_name
        self._field_value = field_value
        self._offset = offset
        self._bits = None
        self._bit_length = bit_length
        self._digit_length = digit_length
        self._ordinal = ordinal
        self._is_padded = is_padded

    @property
    def field_name(self):
        """ The current name for the field  """
        return self._field_name

    @field_name.setter
    def field_name(self, value):
        """ Set the current name for the field  """
        self._field_name = value

    @property
    def field_value(self):
        """ The current value for the field  """
        if self.is_padded:
            return str(self._field_value).zfill(self.digit_length)
        else:
            return str(self._field_value)

    @field_value.setter
    def field_value(self, value):
        """ Set the current value for the field  """
        self._field_value= value
        # refresh fieldBits value
        # self.getBits()

    @property
    def offset(self):
        """ The current offset for the field  """
        return self._offset

    @offset.setter
    def offset(self, value):
        """ Set the current offset for the field  """
        self._offset = value

    @property
    def bits(self):
        """ The current bits for the field  """

        if self.field_name and self.bit_length and self.bit_length > 0:
            ui = BitArray(uint=np.uint32(self.field_value), length=self.bit_length)
            return ui.bin[2:]
        else:
            return "0".zfill(self.bit_length)
        return self._bits

    @bits.setter
    def bits(self, value):
        """ Set the current bits for the field  """
        self._bits = value

    @property
    def bit_length(self):
        """ The current bit_length for the field  """
        return self._bit_length

    @bit_length.setter
    def bit_length(self, value):
        """ Set the current bit_length for the field  """
        self._bit_length = value

    @property
    def digit_length(self):
        """ The current digit_length for the field  """
        return self._digit_length

    @digit_length.setter
    def digit_length(self, value):
        """ Set the current digit_length for the field  """
        self._digit_length = value

    @property
    def ordinal(self):
        """ The current ordinal for the field  """
        return self._ordinal

    @ordinal.setter
    def ordinal(self, value):
        """ Set the current ordinal for the field  """
        self._ordinal = value

    @property
    def is_padded(self):
        """ is_padded for the field  """
        return self._is_padded

    @is_padded.setter
    def is_padded(self, value):
        """ Set is_padded for the field  """
        self._is_padded = value

    def validate_size(self, value):
        if value > pow(2, self._bitLength):
            raise ValueError("%s value is too large" % self.field_name)

    def to_xml(self):
        # hex='%s' binary='%s' length='%d' ordinal='%d' offset='%d' self.hex
        return "<Field name='%s' value='%s'/>" % (self.field_name, str(self.field_value))

    def __lt__(self, object):
        return int(self.ordinal) < int(object.ordinal)

    def __getitem__(self, idx):
        return self._ordinal

    def __repr__(self):
        return "{0}:{1}:{2}:{3}:{4}:{5}".format(self.ordinal, self.field_name, self.field_value, self.bit_length,
                                                self.offset, self.digit_length)

    def __str__(self):
        return self.name
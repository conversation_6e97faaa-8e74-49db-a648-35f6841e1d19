# Partitions are described like this.
# cp = GS1 Company Prefix
# i = Indicator/Pad Digit and Item Reference
# [partition_value, cp_prefix_bits, cp_digits,i_bits, i_digits ]

# Positions in partitions
partition_idx = 0
cp_bits_idx = 1
cp_digits_idx = 2
item_bits_idx = 3
item_digits_idx = 4

SGTIN = [[0, 40, 12, 4, 1], [1, 37, 11, 7, 2], [2, 34, 10, 10, 3], [3, 30, 9, 14, 4], [4, 27, 8, 17, 5],
              [5, 24, 7, 20, 6], [6, 20, 6, 24, 7]]
SSCC = [[0, 40, 12, 18, 5], [1, 37, 11, 20, 6], [2, 34, 10, 24, 7], [3, 30, 9, 27, 8], [4, 27, 8, 30, 9],
             [5, 24, 7, 34, 10], [6, 20, 6, 38, 11]]
GRAI = [[0, 40, 12, 4, 0], [1, 37, 11, 7, 1], [2, 34, 10, 10, 2], [3, 30, 9, 14, 3], [4, 27, 8, 17, 4],
             [5, 24, 7, 20, 5], [6, 20, 6, 24, 6]]
GIAI = [[0, 40, 12, 42, 12], [1, 37, 11, 45, 13], [2, 34, 10, 48, 14], [3, 30, 9, 52, 15], [4, 27, 8, 55, 16],
             [5, 24, 7, 58, 17], [6, 20, 6, 62, 18]]
SGLN = [[0, 40, 12, 1, 0], [1, 37, 11, 4, 1], [2, 34, 10, 7, 2], [3, 30, 9, 11, 3], [4, 27, 8, 14, 4],
             [5, 24, 7, 17, 5], [6, 20, 6, 21, 6]]
GIAI202 = [[0, 40, 12, 148, 18], [1, 37, 11, 151, 19], [2, 34, 10, 154, 20], [3, 30, 9, 158, 21],
                [4, 27, 8, 161, 22], [5, 24, 7, 164, 23], [6, 20, 6, 168, 24]]
GSRN = [[0, 40, 12, 18, 5], [1, 37, 11, 21, 6], [2, 34, 10, 24, 7], [3, 30, 9, 28, 8], [4, 27, 8, 31, 9],
             [5, 24, 7, 34, 10], [6, 20, 6, 38, 11]]
GDTI = [[0, 40, 12, 1, 0], [1, 37, 11, 4, 1], [2, 34, 10, 7, 2], [3, 30, 9, 11, 3], [4, 27, 8, 14, 4],
             [5, 24, 7, 17, 5], [6, 20, 6, 21, 6]]
GID = []  # No Partition Table for GID-96


def get_numbers( partition_type):
    """ Returns the proper partition table array based on the the partition type e.g. SGTIN, SSCC etc."""
    if partition_type == "SGTIN-96":
        return SGTIN
    if partition_type == "SGTIN":
        return SGTIN
    elif partition_type == "SSCC":
        return SSCC
    elif partition_type == "GRAI":
        return GRAI
    elif partition_type == "GIAI":
        return GIAI
    elif partition_type == "GIAI202":
        return GIAI202
    elif partition_type == "SGLN":
        return SGLN
    elif partition_type == "GSRN":
        return GSRN
    elif partition_type == "GDTI":
        return GDTI
    elif partition_type == "GID":
        return GID


def get_partition_value(prefix_length, partition_type):
    """Returns the proper Partition Value based on the Company Prefix Length and Partition Type
    Example : print GetPartitionValue(6,"SGTIN")"""

    numbers = get_numbers(partition_type)
    # verify  why  the  two  following lines works
    if prefix_length == 5:
        prefix_length = 6

    for partition in numbers:
        if partition[cp_digits_idx] == prefix_length:
            return partition[partition_idx]


def get_company_prefix_digit_length(partition_value, partition_type):
    """Returns the number of digits allowed in a Company Prefix for a given tag epc"""
    numbers = get_numbers(partition_type)

    for partition in numbers:
        if partition[partition_idx] == partition_value:
            return partition[cp_digits_idx]


def get_company_prefix_bit_length(partition_value, partition_type):
    """Returns the number of bits allowed in a Company Prefix for a given tag epc"""
    numbers = get_numbers(partition_type)
    for partition in numbers:
        if partition[partition_idx] == partition_value:
            return partition[cp_bits_idx]


def get_item_bit_length(partition_value, partition_type):
        """Returns the number of bits allowed in an Item Ref for a given tag epc"""
        numbers = get_numbers(partition_type)
        for partition in numbers:
                if partition[partition_idx] == partition_value:
                        return partition[item_bits_idx]


def get_item_digit_length(partition_value, partition_type):
        """Returns the number of digits allowed in an Item Ref for a given tag epc"""
        numbers = get_numbers(partition_type)
        for partition in numbers:
                if partition[partition_idx] == partition_value:
                        return partition[item_digits_idx]
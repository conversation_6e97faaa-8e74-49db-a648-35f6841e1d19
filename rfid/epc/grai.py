# -*- coding: utf-8 -*-
import bitstring
import numpy as np
from rfid.epc import epc_number
from rfid.epc.utils import partitions
from rfid.epc.utils.field import Field
from rfid.epc.utils.field import FieldDictionary


class GRAI96(epc_number.EPCNumber):
    """
           Represents an GRAI-96 EPC Encoding
    """
    uri_template = "urn:epc:id:grai:{company_prefix}.{asset_group}.{serial_number}"
    tag_uri_template = "urn:epc:tag:grai-96:{filter}.{company_prefix}.{asset_group}.{serial_number}"

    def __init__(self):
        """
        Constructor for GRAI96
        :param epc_number:
        """
        self._field_dict = FieldDictionary()
        self._encoding_type = "GRAI96"
        self._load_fields()
        self._bits = None
        self._pack_string_format = None

    def _load_fields(self):
        """
        Loads Fields for the GRAI-96
        """

        header = Field(field_name="header", offset=0, bit_length=8, ordinal=1, field_value=51, digit_length=2)
        self._field_dict["header"] = header

        epc_filter = Field(field_name="filter", offset=8, bit_length=3, ordinal=2, field_value=0, digit_length=1)
        self._field_dict["filter"] = epc_filter

        partition = Field(field_name="partition", offset=11, bit_length=3, ordinal=3, field_value=0, digit_length=1)
        self._field_dict["partition"] = partition

        # These next two fields will have their offset,bitLength,digitLength, and value determined at runtime
        company_prefix = Field(field_name="company_prefix", offset=14, bit_length=20, ordinal=4, field_value=0, digit_length=6, is_padded=True)
        self._field_dict["company_prefix"] = company_prefix

        asset_group = Field(field_name="asset_group", offset=38, bit_length=24, ordinal=5, field_value=0, digit_length=6)
        self._field_dict["asset_group"] = asset_group

        serial_number = Field(field_name="serial_number", offset=58, bit_length=38, ordinal=6, field_value=0, digit_length=12)
        self._field_dict["serial_number"] = serial_number

    def encode(self, *args, **kwargs):
        """
        Encodes an GRAI-96 with the values supplied in **kwargs.
        Args:
            *args:
                Empty and ignored
            **kwargs:
                company_prefix
                indicator_digit - default is 0
                item_group
                filter - default is 2
                serial_number - default is 0
        Returns:
           epc_number : An encode instance of an GRAI-96
        """
        company_prefix = str(kwargs.get("company_prefix"))
        indicator_digit = kwargs.get("indicator_digit", 0)
        item_group = str(kwargs.get("item_group"))
        filter_value = kwargs.get("filter", 0 )
        serial_number = kwargs.get("serial_number", 0)

        # Filter
        self.set_field_value("filter", filter_value)

        # Partition
        partition_value = partitions.get_partition_value(len(company_prefix), "GRAI")

        self.set_field_value("partition", np.uint32(partition_value))

        # Company Prefix
        company_prefix_field = self.get_field("company_prefix")
        company_prefix_field.bit_length = partitions.get_company_prefix_bit_length(partition_value, "GRAI")
        company_prefix_field.digit_length = partitions.get_company_prefix_digit_length(partition_value, "GRAI")
        self.set_field_value("company_prefix", company_prefix)

        # Item asset_group
        asset_group_field = self.get_field("asset_group")
        asset_group_field.bit_length = partitions.get_item_bit_length(partition_value, "GRAI")
        asset_group_field.digit_length = partitions.get_item_digit_length(partition_value, "GRAI")
        asset_group_field.offset = int(company_prefix_field.bit_length) + int(company_prefix_field.offset)

        # EPC GRAI asset_group is composed by indicator digit and asset_group.
        indicator_and_asset_group = str(indicator_digit) + str(item_group)
        self.set_field_value("asset_group", indicator_and_asset_group)

        # Serial Number
        serial_number_field = self.get_field("serial_number")
        serial_number_field.digit_length = len(str(serial_number))
        self.set_field_value("serial_number", serial_number)

        self.update_bits()

        return self

    def set_field_value(self, field_name, value):
        """
        Overridden from the base class because if the SerialNumber changes the indicator digit must be preserved
        otherwise its a straight exchange
        """
        field = self._field_dict[field_name]
        field.field_value = value

    def load_fields(self):
        return self._load_fields()

    def update_bits(self):
        # Set the PackString Format
        self._pack_string_format = 'uint:8, uint:3, uint:3, uint:{}, uint:{}, uint:38' \
            .format(self.get_field("company_prefix").bit_length, self.get_field("asset_group").bit_length)

        # Pack the bitstring
        bsp = bitstring.pack(self.pack_string_format,
                             self.get_field_value("header"),
                             self.get_field_value("filter"),
                             self.get_field_value("partition"),
                             self.get_field_value("company_prefix"),
                             self.get_field_value("asset_group"),
                             int(self.get_field_value("serial_number")))

        # Set the _bits for the GRAI-96
        self._bits = bsp.unpack("bin")[0]

    def from_binary(self, binary):
        """
        Decodes an GRAI from BINARY string

        :arg:
          binary (str) - A 96 bit binary string representing an GRAI-96
        :returns
          (GRAI96) - An instance of the GRAI96 Class.

        :raises:
            ValueError - If the binary string passed in is not 96 bits, this exception is thrown.
        """
        self._load_fields()
        if len(binary) != 96:
            raise ValueError("Binary string is not 96 bits. GRAI-96 Requires 96 bits to decode properly")

        # Filter
        filter_value = binary[8:11]
        self.set_field_value("filter", int(filter_value, 2))
        partition_value = binary[11:14]
        partition_value = int(partition_value, 2)
        self.set_field_value("partition", partition_value)

        # Partition
        company_prefix_bit_length = partitions.get_company_prefix_bit_length(partition_value, "GRAI")
        company_prefix_digit_length = partitions.get_company_prefix_digit_length(partition_value, "GRAI")

        self.get_field("company_prefix").offset = 14
        self.get_field("company_prefix").bit_length = company_prefix_bit_length
        self.get_field("company_prefix").digit_length = company_prefix_digit_length

        item_group_bit_length = partitions.get_item_bit_length(partition_value, "GRAI")
        item_group_digit_length = partitions.get_item_digit_length(partition_value, "GRAI")

        self.get_field("asset_group").offset = 14 + company_prefix_bit_length
        self.get_field("asset_group").bit_length = item_group_bit_length
        self.get_field("asset_group").digit_length = item_group_digit_length

        company_prefix_bits = binary[14:14 + self.get_field("company_prefix").bit_length]
        company_prefix = str(int(company_prefix_bits, 2)).zfill(company_prefix_digit_length)
        self.set_field_value("company_prefix", company_prefix)

        asset_group_bits = binary[self.get_field("asset_group").offset:self.get_field(
            "asset_group").offset + self.get_field("asset_group").bit_length]
        # make sure we are on a 1 byte boundary

        asset_group = str(int(asset_group_bits, 2)).zfill(item_group_digit_length)
        self.set_field_value("asset_group", asset_group)
        serial_number_bits = binary[self.get_field("serial_number").offset:self.get_field(
            "serial_number").offset + self.get_field("serial_number").bit_length]

        # self.serial_number = int(serial_number_bits, 2)
        self.set_field_value("serial_number", int(serial_number_bits, 2))
        self.update_bits()

        return self

    def to_uri(self):
        cp = self.get_field_value("company_prefix")
        ref = self.get_field_value("asset_group")
        serial = self.get_field_value("serial_number")
        return self.uri_template.format(**{"company_prefix": cp, "asset_group": ref, "serial_number": serial})

    def to_tag_uri(self):
        filter_value = self.get_field_value("filter")
        cp = self.get_field_value("company_prefix")
        ref = self.get_field_value("asset_group")
        serial = self.get_field_value("serial_number")
        return self.tag_uri_template.format(**{"filter": filter_value,
                                               "company_prefix": cp,
                                               "asset_group": ref,
                                               "serial_number": serial})
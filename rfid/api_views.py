# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json

from django.core.mail.backends import console
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import Search<PERSON>ilter, OrderingFilter

from django.db import transaction

import inventory
from operations.business_logic import production_orders, purchase_orders
from django.db import transaction, IntegrityError

from integrations.api_views import send_error
from operations.business_logic import production_orders
from rest_framework import viewsets, status, mixins
from rest_framework.generics import CreateAPIView, ListAPIView
from rest_framework.decorators import action
from rest_framework.permissions import *
from rest_framework.response import Response

from .business_logic import print_orders
from .filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReaderFilter, NodeFilter
from .schemas import RFIDActivityViewSchema
from .serializers import *
from .tasks import init_print_order, issue_print_order_items, create_rfid_activity
from core_config import utils
from core_config.models import UserConfig

import integrations.tasks as integration_tasks
import integrations.utils as integration_utils
import rfid.tasks as rfid_tasks
import rfid.utils as rfid_utils


class NodeViewSet(viewsets.ModelViewSet):
    """
    A simple ViewSet for viewing and editing Node.
    """
    queryset = Node.objects.all()
    serializer_class = NodeSerializer
    filter_class = NodeFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    @action(methods=['get'], detail=True)
    def rfid_activities(self, request, pk=None):
        """
        Lists Node RFID_Activity
        """
        node = self.get_object()
        query = RFIDActivity.objects.filter(node=node)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = RFIDActivityNodeSerializer(page, context=
            {'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = RFIDActivityNodeSerializer(query, many=True)
        return Response(serializer.data)


class ReaderViewSet(viewsets.ModelViewSet):
    """
    A simple ViewSet for viewing and editing a RFID Reader.
    """
    queryset = Reader.objects.all()
    serializer_class = ReaderSerializer
    filter_class = ReaderFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'


class PrintOrderViewSet(viewsets.ModelViewSet):
    """
    retrieve:
    Return the given print order.
    list:
    Return a list of all the existing print orders.
    create:
    Create a new print order instance.

    update:
    Update an existing print order instance.

    partial_update:
    Update partial an existing print order instance.

    delete:
    Delete  an existing print order instance.
    """
    queryset = PrintOrder.objects.all()
    serializer_class = PrintOrderSerializer
    filter_class = PrintOrderFilter
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    def get_serializer_context(self):
        return {'request': self.request}

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        print_order_lines = serializer.validated_data.pop('lines')

        with transaction.atomic():
            # TODO: refactor this lines in a common  method
            print_order = serializer.save()
            if not print_order.batch_number:
                if print_order.purchase_order:
                    print_order.batch_number = 'PUO-{}'.format(print_order.pk)
                else:
                    print_order.batch_number = 'PO-{}'.format(print_order.pk)
                print_order.save()
            if print_order.customer:
                print_order.save()
            else:
                user_config = utils.get_user_config(request.user.pk)
                print_order.customer = user_config.customer
                print_order.save()
            gtin_lines = print_order_lines.get('gtin')
            print_orders.create_gtin_lines_from_array(order=print_order, lines=gtin_lines)
            init_print_order(print_order.pk)
            if print_order.batch_type == 'PURCHASE_ORDER':
                module_config = integration_utils.verified_integration_module(
                    customer_id=2, kong_module__name='PRINT_ORDER_LINE_COMPLETE',
                    active_integration=True)
                if module_config:
                    request_body = {
                        "detail-type": "PRINT_ORDER_LINE_COMPLETE",
                        "print_order_id": print_order.id
                    }
                    integration_tasks.integration_module_request(
                        integration_module_id=module_config.id, body=request_body)

        headers = self.get_success_headers(serializer.data)
        serializer = PrintOrderSerializer(print_order)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists Print order History
        """
        print_object = self.get_object()
        prints = PrintOrder.objects.get(pk=print_object.pk)
        query = prints.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = PrintOrderSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = PrintOrderSerializer(query, many=True)
        return Response(serializer.data)

    @action(methods=['get'], detail=True)
    def gtin_lines(self, request, pk=None):
        """
            Lists print order GTIN Lines
        """
        print_order = self.get_object()
        query = GTINPrintOrderLine.objects.filter(
            print_order=print_order)
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = GTINPrintOrderLineSerializer(
                page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = GTINPrintOrderLineSerializer(
            query, many=True)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=EmptySerializer,
            url_path='actions/init_print', url_name='init-print')
    def init_print(self, request, pk=None):
        print_order = self.get_object()
        serializer = EmptySerializer()
        print_order.init_print(request.user)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=PrintOrderProgressSerializer,
            url_path='actions/progress_print', url_name='progress-print')
    def progress_print(self, request, pk=None):
        print_order = self.get_object()
        serializer = PrintOrderProgressSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data['lines']
        issue_items = request.query_params.get('issue_items')

        validation = print_orders.validate_items_gtin_line(lines=lines, order=print_order)
        if validation:
            print_orders.execute_progress_print_order(lines, print_order)
            if issue_items == "true":
                issue_print_order_items(print_order.pk)
            return Response(data=serializer.data, status=status.HTTP_202_ACCEPTED)
        else:
            data = {'message': 'Items not allowed for print order.'}
            return Response(data=data, status=status.HTTP_406_NOT_ACCEPTABLE)

    @action(methods=['post'], detail=True, serializer_class=PrintOrderTotalSerializer,
            url_path='actions/finish_print', url_name='finish-print')
    def finish_print(self, request, pk=None):
        print_order = self.get_object()
        serializer = PrintOrderTotalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        total_tags_used = serializer.validated_data['total_tags_used']
        total_tags_void = serializer.validated_data['total_tags_void']
        total_tags_programed = serializer.validated_data['total_tags_programed']
        print_order.finish_print(total_tags_used, total_tags_void, total_tags_programed)
        result = issue_print_order_items(print_order.pk)
        if result == False:
            body = {'message': 'The items do not exist in the system'}
            return Response(data=body, status=status.HTTP_400_BAD_REQUEST)
        gtin_lines = GTINPrintOrderLine.objects.filter(print_order=print_order).values_list('issued_items', flat=True)
        items_list = ','.join(','.join(item["epcs"]) for item in list(gtin_lines))
        module_config = integration_utils.verified_integration_module(
            customer_id=print_order.customer_id, kong_module__name='PRINT_ORDER_SEND_ITEMS',
            active_integration=True)
        order_id = None
        type_order_external_id = None
        if print_order.production_order:
            order_id = print_order.production_order.pk
            type_order_external_id = "PO"
        elif print_order.purchase_order:
            type_order_external_id = 'PUO'
            order_id = print_order.purchase_order.pk
        if module_config:
            request_body = {
                "detail-type": "CREATE_PRINT_ORDER",
                "order_id": order_id,
                "type_order_external_id": type_order_external_id,
                "print_order_id": print_order.pk,
                "items": [items_list]

            }
            integration_tasks.integration_module_request(
                integration_module_id=module_config.id, body=request_body)
        return Response(serializer.data)

    @action(methods=['post'], detail=True, serializer_class=EmptySerializer,
            url_path='actions/aborted_print', url_name='aborted-print')
    def finish_aborted(self, request, pk=None):
        print_order = self.get_object()
        print_order.aborted_print()
        return Response(status=status.HTTP_202_ACCEPTED)

    @action(methods=['post'], detail=True, serializer_class=RFIDExecuteActionPrintOrderSerializer,
            url_path='actions/execute-actions-print-order', url_name='actions/execute-actions-print-order')
    def execute_action(self, request, pk=None):
        print_order = self.get_object()
        serializer = RFIDExecuteActionPrintOrderSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        gtin_lines = GTINPrintOrderLine.objects.filter(print_order_id=serializer.initial_data["print_order_id"])
        if serializer.validated_data["print_order"].status == "PRINTED" and gtin_lines:
            if serializer.initial_data["action"] == "TO LIVE":
                print_order_items = GTINPrintOrderLine.objects.filter(print_order=serializer.initial_data["print_order_id"]). \
                    values_list('created_items', flat=True)
                items_list = ','.join(','.join(item["epcs"]) for item in list(print_order_items))
                new_items = list(filter(None, list(items_list.split(","))))
                new_epc = None
                items = []
                for epc in new_items:
                    sku_epc = None
                    gtin_line = GTINPrintOrderLine.objects.filter(created_items__epcs__contains=epc,
                                                                  print_order_id=print_order.pk).first()
                    if gtin_line:
                        sku_epc = gtin_line.sku.pk
                    if serializer.initial_data["coding_type"] == 'GRAI_96':
                        new_epc = grai.GRAI96()
                    elif serializer.initial_data["coding_type"] == 'SGTIN_96':
                        new_epc = sgtin.SGTIN96()
                    new_epc.from_hex(epc)
                    items.append(
                        Item(
                            pk=epc,
                            sku_id=sku_epc,
                            serial=int(new_epc.get_field_value("serial_number")),
                            state="PRESENT",
                            batch_number=print_order.batch_number,
                            batch_type=print_order.batch_type,
                            current_location=print_order.location,
                            created_by=print_order.created_by,
                            modified_by=print_order.modified_by,
                            customer=print_order.customer,
                        )
                    )
                items = Item.objects.bulk_create(items)
        else:
            body = {"message": "Review the colum created_items or the status about print order {}"
                .format(serializer.initial_data["print_order_id"])}
            return Response(status=status.HTTP_400_BAD_REQUEST)
        return Response(status=status.HTTP_202_ACCEPTED)

    @action(methods=['post'], detail=True, serializer_class=ListPrintOrderClonningSerializer,
            url_path='actions/clone_print', url_name='clone-print')
    def clone_print(self, request, pk=None):
        print_order = self.get_object()
        serializer = ListPrintOrderClonningSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines', None)
        print_orders.clone_print(order=print_order, reporter=request.user, lines=lines)
        return Response(status=status.HTTP_202_ACCEPTED)


class PrintTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """
    retrieve:
    Return the given item group.
    list:
    Return a list of all the existing items.
    """
    queryset = PrintTemplate.objects.all()
    serializer_class = PrintTemplateSerializer
    filter_fields = ('tag_reference',)
    filter_backends = (DjangoFilterBackend, SearchFilter, OrderingFilter,)
    ordering_fields = '__all__'

    @action(methods=['get'], detail=True, url_name='history')
    def history(self, request, pk=None):
        """
        Lists Print order History
        """
        print_line_object = self.get_object()
        print_lines = GTINPrintOrderLine.objects.get(pk=print_line_object.pk)
        query = print_lines.history.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = GTINPrintOrderLineSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = GTINPrintOrderLineSerializer(query, many=True)
        return Response(serializer.data)


class RFIDActivityView(CreateAPIView, ListAPIView):
    """
        Process RFID-ACTIVITY
    """
    queryset = RFIDActivity.objects.all()
    serializer_class = RFIDActivitySerializer
    permission_classes = (IsAuthenticated,)
    schema = RFIDActivityViewSchema()

    def post(self, request, **kwargs):
        serializer = RFIDActivitySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        readings = serializer.validated_data['readings']
        node_id = serializer.validated_data['node_id']
        timestamp = serializer.validated_data['timestamp']
        action = serializer.validated_data['action']
        user_id = request.user.pk
        run_shopify = request.query_params.get('run_shopify', 'false')
        node_object = Node.objects.get(mac=node_id)
        location_name = node_object.location.name

        if 'customer_id' in serializer.validated_data:
            customer_id = serializer.validated_data['customer_id']
        else:
            customer_id = UserConfig.objects.get(user_id=user_id).customer_id

        verification_items = rfid_utils.get_allowed_items(readings=readings, customer_id=customer_id, action=action)
        if verification_items:
            try:
                with transaction.atomic():
                    if run_shopify == "true":
                        verification_module = integration_utils.verified_integration_module(customer_id=customer_id,
                                                                                            permission_name='INVENTORY_ITEM_POS')
                        if verification_module:
                            node_object = Node.objects.get(mac=node_id)
                            if node_object.location == verification_module.current_location:
                                if verification_module.active_integration:
                                    rfid_tasks.inventory_update_rfid.delay(action=action, readings=readings,
                                                                           customer_id=customer_id,
                                                                           current_location_id=verification_module.current_location.id)
                    create_rfid_activity(readings=readings, node_id=node_id, timestamp=timestamp, action=action,
                                         reporter_id=user_id, customer_id=customer_id, request=request)
                    return Response(status=status.HTTP_201_CREATED)

            except IntegrityError as error:
                transaction.rollback()
                request.data['response'] = error.args[0]
                send_error(request, request.data)
                raise IntegrityError
        else:
            body = {"message": "Some item was already {}".format(action)}
            return Response(body, status=status.HTTP_406_NOT_ACCEPTABLE)

    def get(self, request, *args, **kwargs):
        query = RFIDActivity.objects.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = RFIDActivityNodeSerializer(page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = RFIDActivityNodeSerializer(query, many=True)
        return Response(serializer.data)


class RFIDActivityMobileView(CreateAPIView, ListAPIView):
    """
        Process RFID-ACTIVITY-MOBILE

    """
    queryset = RFIDActivityMobile.objects.all()
    serializer_class = RFIDActivityAssetSerializer
    permission_classes = (IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        serializer = RFIDActivityAssetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        items = serializer.validated_data["items"]
        location = serializer.validated_data["location"]
        action = serializer.validated_data["action"]
        user_id = request.user.pk
        rfid_tasks.create_rfid_activity_mobile(items=items, action=action, location_id=location.id,
                                               reporter_id=user_id)

        return Response(status=status.HTTP_201_CREATED)

    def get(self, request, *args, **kwargs):
        query = RFIDActivityMobile.objects.all()
        page = self.paginate_queryset(query)
        if page is not None:
            serializer = RFIDActivityMobileSerializer(
                page, context={'request': request}, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = RFIDActivityMobileSerializer(query, many=True)
        return Response(serializer.data)


class RFIDPurchaseOrderViewSet(mixins.CreateModelMixin, mixins.DestroyModelMixin, viewsets.GenericViewSet):
    """
    create:
    Create a new purchase order instance, (also generates Print Order).
    delete:
    Delete  an existing purchase order instance.
    """

    queryset = PurchaseOrder.objects.all()
    serializer_class = RFIDPurchaseOrderSerializer
    permission_classes = (IsAuthenticated,)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        customer_properties = UserConfig.objects.get(user_id=request.user.pk).customer.properties.get(
            "integration_users")
        if customer_properties:
            user_ids = customer_properties.get("user_ids")
            if str(request.user.pk) in user_ids:
                current_location = inventory.business_logic.locations.validate_locations(
                    source_id=serializer.initial_data["entry_location_id"],
                    destination_id=None, user=request.user)
                if current_location:
                    if current_location.status_code != 200:
                        return current_location
                    if serializer.initial_data.get("source_id"):
                        serializer.initial_data["source_id"] = json.loads(current_location.content).get(
                            "current_location_source")
                        serializer.initial_data["destination_id"] = json.loads(current_location.content).get(
                            "current_location_destination")
                    else:
                        serializer.initial_data["entry_location_id"] = json.loads(current_location.content).get(
                            "current_location_source")
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines')
        used_external_id = serializer.validated_data.pop('used_external_id')

        with transaction.atomic():
            purchase_order = serializer.save()

            if purchase_order.customer:
                purchase_order.save()
            else:
                user_config = utils.get_user_config(request.user.pk)
                purchase_order.customer = user_config.customer
                purchase_order.save()

            if used_external_id is True:
                lines = purchase_orders.transform_lines_external_to_id(lines)
            purchase_lines = purchase_orders.create_lines_from_array(purchase_order, lines)
            print_order = print_orders.create_from_purchase_order(order=purchase_order, lines=purchase_lines)
            init_print_order(print_order.pk)

        module_config = integration_utils.verified_integration_module(
            customer_id=purchase_order.customer_id, kong_module__name='PURCHASE_ORDER_CREATE',
            active_integration=True)
        if module_config:
            request_body = {
                "detail-type": "PURCHASE_ORDER_CREATE",
                "purchase_order_id": purchase_order.id
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_config.id, body=request_body)

        headers = self.get_success_headers(serializer.data)
        serializer = RFIDPurchaseOrderSerializer(purchase_order)
        final_data = serializer.data  # I don't really like this solution  to  add print_order_id
        final_data['print_order_id'] = print_order.pk  # to the response, find a better way to do so.

        return Response(final_data, status=status.HTTP_201_CREATED, headers=headers)


class RFIDProductionOrderViewSet(mixins.CreateModelMixin, mixins.DestroyModelMixin, viewsets.GenericViewSet):
    """
    create:
    Create a new production order instance, (also generates Print Order).
    delete:
    Delete  an existing production order instance.
    """

    queryset = ProductionOrder.objects.all()
    serializer_class = RFIDProductionOrderSerializer
    permission_classes = (IsAuthenticated,)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        print("serializer", serializer)
        logging.info(request.data)
        customer_properties = UserConfig.objects.get(user_id=request.user.pk).customer.properties.get("integration_users")
        if customer_properties:
            user_ids = customer_properties.get("user_ids")
            if str(request.user.pk) in user_ids:
                current_location = inventory.business_logic.locations.validate_locations(
                    source_id=serializer.initial_data["entry_location_id"],
                    destination_id=None, user=request.user)
                if current_location:
                    if current_location.status_code != 200:
                        return current_location
                    if serializer.initial_data.get("source_id"):
                        serializer.initial_data["source_id"] = json.loads(current_location.content).get(
                            "current_location_source")
                        serializer.initial_data["destination_id"] = json.loads(current_location.content).get(
                            "current_location_destination")
                    else:
                        serializer.initial_data["entry_location_id"] = json.loads(current_location.content).get(
                            "current_location_source")
        serializer.is_valid(raise_exception=True)
        lines = serializer.validated_data.pop('lines')
        used_external_id = serializer.validated_data.pop('used_external_id')

        with transaction.atomic():
            production_order = serializer.save()

            if production_order.customer:
                production_order.save()
            else:
                user_config = utils.get_user_config(request.user.pk)
                production_order.customer = user_config.customer
                production_order.save()

            if used_external_id is True:
                lines = production_orders.transform_lines_external_to_id(lines)
            production_orders.create_lines_from_array(production_order, lines)
            print_order = print_orders.create_from_order(order=production_order, lines=lines)
            init_print_order(print_order.pk)

        module_config = integration_utils.verified_integration_module(
            customer_id=production_order.customer_id, kong_module__name='PRODUCTION_ORDER_CREATE',
            active_integration=True)
        if module_config:
            request_body = {
                "detail-type": "PRODUCTION_ORDER_CREATE",
                "production_order_id": production_order.id
            }
            integration_tasks.integration_module_request(
                integration_module_id=module_config.id, body=request_body)
        headers = self.get_success_headers(serializer.data)
        serializer = RFIDProductionOrderSerializer(production_order)
        final_data = serializer.data  # I don't really like this solution  to  add print_order_id
        final_data['print_order_id'] = print_order.pk  # to the response, find a better way to do so.

        return Response(final_data, status=status.HTTP_201_CREATED, headers=headers)

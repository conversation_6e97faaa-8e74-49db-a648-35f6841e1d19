# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from common.serializers import *
from inventory.serializers import SKUAmountSerializer, SKUNestedSerializer, LocationNestedSerializer, \
    ItemNestedSerializer, ItemTypeSerializer
from operations.serializers import ProductionOrderSerializer, \
    ContactNestedSerializer, ProductionOrderNestedSerializer, PurchaseOrderSerializer, PurchaseOrderNestedSerializer

from .models import *
from customers.serializers import CustomerNestedSerializer
from operations.models import *


# Readers And Hardware Related Serializers
class ReaderBrandSerializer(BaseSerializer, serializers.ModelSerializer):
    class Meta:
        model = ReaderBrand
        fields = '__all__'


class NetworkSettingsSerializer(serializers.Serializer):
    dhcp_enabled = serializers.BooleanField()
    private_ip_address = serializers.IPAddressField()
    net_mask = serializers.IPAddressField()
    gateway = serializers.IPAddressField()
    dns = serializers.ListField(child=serializers.IPAddressField())


class ReaderSerializer(BaseSerializer, serializers.ModelSerializer):
    brand = ReaderBrandSerializer()
    dhcp_enabled = serializers.BooleanField(write_only=True)
    private_ip_address = serializers.IPAddressField(write_only=True)
    net_mask = serializers.IPAddressField(write_only=True)
    gateway = serializers.IPAddressField(write_only=True)
    dns = serializers.ListField(child=serializers.IPAddressField(), write_only=True)
    network_settings = serializers.SerializerMethodField()

    class Meta:
        model = Reader
        fields = '__all__'

    def get_network_settings(self, obj):
        try:
            serializer = NetworkSettingsSerializer(obj.get_network_settings())
            return serializer.data
        except Exception as e:
            print(str(e))
            return None


class NodeSerializer(BaseSerializer, serializers.ModelSerializer):
    readers = ReaderSerializer(many=True)
    location = LocationNestedSerializer(read_only=True)
    location_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='location',
                                                     write_only=True)

    class Meta:
        model = Node
        fields = '__all__'


class NodeNestedSerializer(BaseSerializer):
    location = LocationNestedSerializer(read_only=True)

    class Meta:
        model = Node
        fields = ('mac', 'location',)


# Tags Serializers
class BrandTagNestedSerializer(BaseSerializer, serializers.ModelSerializer):
    class Meta:
        model = BrandTag
        fields = '__all__'


class TagChipBrandNestedSerializer(BaseSerializer, serializers.ModelSerializer):
    class Meta:
        model = TagChipBrand
        fields = '__all__'


class TagReferenceSerializer(BaseSerializer, serializers.ModelSerializer):
    brand_id = serializers.PrimaryKeyRelatedField(queryset=BrandTag.objects.all(), source='brand',
                                                  write_only=True)
    brand = BrandTagNestedSerializer(read_only=True)

    chip_brand_id = serializers.PrimaryKeyRelatedField(queryset=TagChipBrand.objects.all(), source='chip_brand',
                                                       write_only=True)
    chip_brand = TagChipBrandNestedSerializer(read_only=True)

    class Meta:
        model = TagReference
        fields = '__all__'


# Print Order Related Serializers
class PrintTemplateSerializer(BaseSerializer, serializers.ModelSerializer):
    tag_reference_id = serializers.PrimaryKeyRelatedField(queryset=TagReference.objects.all(), source='tag_reference',
                                                          write_only=True)
    tag_reference = TagReferenceSerializer(read_only=True)

    class Meta:
        model = PrintTemplate
        fields = '__all__'


class PrintOrderNestedSerializer(BaseSerializer, serializers.ModelSerializer):
    location = LocationNestedSerializer(read_only=True)
    location_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='location',
                                                     write_only=True)
    provider = ContactNestedSerializer(read_only=True)
    purchase_order = PurchaseOrderNestedSerializer(read_only=True)
    purchase_order_id = serializers.PrimaryKeyRelatedField(queryset=PurchaseOrder.objects.all(), source='purchase_order'
                                                           , write_only=True, required=False, allow_null=True)

    production_order = ProductionOrderNestedSerializer(read_only=True)
    production_order_id = serializers.PrimaryKeyRelatedField(queryset=ProductionOrder.objects.all(),
                                                             source='production_order', write_only=True,
                                                             required=False, allow_null=True)
    customer = CustomerNestedSerializer(read_only=True)
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(),
                                                     source='customer', write_only=True,
                                                     required=False, allow_null=True)

    class Meta:
        model = PrintOrder
        fields = '__all__'
        read_only_fields = ('serial_numbers_generated',)


class GTINPrintOrderLineSerializer(BaseSerializer, serializers.ModelSerializer):
    sku = SKUNestedSerializer(read_only=True)
    print_order = PrintOrderNestedSerializer(read_only=True)

    class Meta:
        model = GTINPrintOrderLine
        fields = '__all__'


class PrintOrderLinesSerializer(serializers.Serializer):
    gtin = SKUAmountSerializer(many=True, write_only=True)


class GTINPrintOrderLineProgressSerializer(serializers.Serializer):
    sku_id = serializers.CharField(max_length=140, write_only=True)
    items = serializers.ListField(child=serializers.CharField(max_length=150), required=False, write_only=True)


class PrintOrderProgressSerializer(serializers.Serializer):
    lines = GTINPrintOrderLineProgressSerializer(many=True, write_only=True)

class PrintOrderClonningSerializer(serializers.Serializer):
    sku_id = serializers.CharField(max_length=140, write_only=True)
    items = serializers.ListField(child=serializers.CharField(max_length=150), write_only=True)
    cloned_amount = serializers.IntegerField(write_only=True)

class ListPrintOrderClonningSerializer(serializers.Serializer):
    lines = PrintOrderClonningSerializer(many=True, write_only=True)

class PrintOrderSerializer(BaseSerializer, serializers.ModelSerializer):
    printed_by = UserNestedSerializer(read_only=True)

    template = PrintTemplateSerializer(read_only=True)
    template_id = serializers.PrimaryKeyRelatedField(queryset=PrintTemplate.objects.all(), source='template',
                                                     write_only=True, allow_null=True, required=False)

    location = LocationNestedSerializer(read_only=True)
    location_id = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all(), source='location',
                                                     write_only=True)

    created_by = UserNestedSerializer(read_only=True)
    modified_by = UserNestedSerializer(read_only=True)

    lines = PrintOrderLinesSerializer(write_only=True)
    status = serializers.CharField(max_length=25, read_only=True)
    prefix = serializers.CharField(max_length=3, read_only=True)

    provider_id = serializers.PrimaryKeyRelatedField(queryset=Contact.objects.all(), source='provider',
                                                     write_only=True, required=False, allow_null=True)
    provider = ContactNestedSerializer(read_only=True)

    purchase_order = PurchaseOrderNestedSerializer(read_only=True)
    purchase_order_id = serializers.PrimaryKeyRelatedField(queryset=PurchaseOrder.objects.all(), source='purchase_order'
                                                           , write_only=True, required=False, allow_null=True)

    production_order = ProductionOrderNestedSerializer(read_only=True)
    production_order_id = serializers.PrimaryKeyRelatedField(queryset=ProductionOrder.objects.all(),
                                                             source='production_order', write_only=True,
                                                             required=False, allow_null=True)
    customer = CustomerNestedSerializer(read_only=True)
    customer_id = serializers.PrimaryKeyRelatedField(queryset=Customer.objects.all(),
                                                     source='customer', write_only=True,
                                                     required=False, allow_null=True)

    class Meta:
        model = PrintOrder
        fields = '__all__'
        read_only_fields = ('serial_numbers_generated',)


class PrintOrderTotalSerializer(serializers.Serializer):
    total_tags_used = serializers.IntegerField()
    total_tags_void = serializers.IntegerField()
    total_tags_programed = serializers.IntegerField()


# Reading Serializer
class ReadingSerializer(serializers.Serializer):
    epc = serializers.CharField(max_length=128)
    user_memory = serializers.CharField(max_length=32, allow_blank=True)
    tid = serializers.CharField(max_length=16, allow_blank=True)
    antenna = serializers.IntegerField()
    description = serializers.CharField(max_length=140, allow_blank=True, allow_null=True, required=False)


# Node Activity Record serializer
class RFIDActivitySerializer(serializers.Serializer):
    action = serializers.CharField(max_length=15)
    node_id = serializers.CharField(max_length=17)
    timestamp = serializers.FloatField()
    readings = ReadingSerializer(many=True)
    customer_id = serializers.IntegerField(required=False)


class RFIDActivityNodeSerializer(BaseSerializer):
    item = serializers.SerializerMethodField()
    node = NodeNestedSerializer(read_only=True)

    def get_item(self, obj):
        try:
            item = Item.objects.get(pk=obj.epc)
            return ItemNestedSerializer(item).data
        except Item.DoesNotExist:
            return None

    class Meta:
        model = RFIDActivity
        fields = ('epc', 'antenna', 'action', 'created', 'item', 'node', 'customer_id')


class RFIDActivityAssetSerializer(EmptySerializer):
    RFID_ACTION_MOBILE = (
        ("IN", "In"),
        ("OUT", "Out")
    )
    items = serializers.ListField(child=serializers.CharField(max_length=150))
    location = LocationNestedSerializer(read_only=True)
    location_id = serializers.PrimaryKeyRelatedField(
        queryset=Location.objects.all(), source='location', write_only=True)
    action = serializers.ChoiceField(choices=RFID_ACTION_MOBILE)


class RFIDActivityMobileSerializer(BaseSerializer):
    item = serializers.SerializerMethodField(read_only=True)
    location = LocationNestedSerializer(read_only=True)

    @staticmethod
    def get_item_object(obj):
        try:
            item = Item.objects.get(pk=obj.pk)
            return ItemNestedSerializer(item).data
        except Exception as error:
            logging.error("class RFIDActivityMobileSerializer")
            logging.error("def get_item_object")
            logging.error(error.args)
            return None


# RFID Production Order Serializer

class RFIDProductionOrderSerializer(ProductionOrderSerializer):
    used_external_id = serializers.BooleanField(required=False, default=False, write_only=True)
    item_type = ItemTypeSerializer(read_only=True)
    item_type_id = serializers.PrimaryKeyRelatedField(
        queryset=ItemType.objects.all(), source='item_type', required=False, write_only=True)
    item_type_external_id = serializers.SlugRelatedField(queryset=ItemType.objects.all(), slug_field='external_id',
                                                              source='item_type', write_only=True, required=False,
                                                              allow_null=True)
    type_external_id = serializers.SlugRelatedField(queryset=SKUType.objects.all(), slug_field='external_id',
                                                    source='type', write_only=True, required=False)
    type_id = serializers.PrimaryKeyRelatedField(queryset=SKUType.objects.all(), source='type',
                                                 write_only=True, required=False)
    class Meta:
        model = ProductionOrder
        fields = '__all__'


# RFID Purchase Order Serializer
class RFIDPurchaseOrderSerializer(PurchaseOrderSerializer):
    used_external_id = serializers.BooleanField(required=False, default=False, write_only=True)
    item_type = ItemTypeSerializer(read_only=True)
    item_type_id = serializers.PrimaryKeyRelatedField(
        queryset=ItemType.objects.all(), source='item_type', required=False, write_only=True)
    item_type_external_id = serializers.SlugRelatedField(queryset=ItemType.objects.all(), slug_field='external_id',
                                                         source='item_type', write_only=True, required=False,
                                                         allow_null=True)
    type_external_id = serializers.SlugRelatedField(queryset=SKUType.objects.all(), slug_field='external_id',
                                                    source='type', write_only=True, required=False)
    type_id = serializers.PrimaryKeyRelatedField(queryset=SKUType.objects.all(), source='type',
                                                 write_only=True, required=False)

    class Meta:
        model = PurchaseOrder
        fields = '__all__'


class RFIDExecuteActionPrintOrderSerializer(serializers.Serializer):
    ACTION = [
        ("TO LIVE", "To Live")
    ]
    action = serializers.ChoiceField(choices=ACTION)
    print_order = PrintOrderSerializer(read_only=True)
    print_order_id = serializers.PrimaryKeyRelatedField(queryset=PrintOrder.objects.all(),
                                                        source='print_order', write_only=True,
                                                        required=False, allow_null=True)


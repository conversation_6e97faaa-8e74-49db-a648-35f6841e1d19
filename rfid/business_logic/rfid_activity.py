# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from datetime import time

import rfid.utils as rfid_utils
from common.models import User
from inventory import utils
from rfid.models import RFIDActivity
from inventory.business_logic.moves import move_items, update_items_state, arrange_items_by_sku
from inventory.models import Trace, Item, Move, MoveLine
from ..models import Node
from customers.models import Customer


def ingress_item(item, destination_location, reporter, customer, description):
    updated_items_ids = move_items(items=[item], destination=destination_location,
                                   reporter=reporter)
    for item in updated_items_ids:
        Trace.objects.create(
            created_by=reporter,
            modified_by=reporter,
            item_id=item,
            location=destination_location,
            reporter_source="FIXED_PORTAL",
            action="SIMPLE_ENTRY",
            description=description,
            reporter=reporter,
            customer=customer
        )


def take_out_item(item, source_location, reporter, customer, description):
    updated_items_ids = update_items_state(items=[item], state="NOT_PRESENT", reporter=reporter)
    for item in updated_items_ids:
        Trace.objects.create(
            created_by=reporter,
            modified_by=reporter,
            item_id=item,
            location=source_location,
            reporter_source="FIXED_PORTAL",
            action="TAKEOUT",
            description=description,
            reporter=reporter,
            customer=customer
        )


def sale_item(item: list, source_location: object, reporter: object, customer: object, description: str):
    updated_items_ids = update_items_state(items=[item], state="SOLD", reporter=reporter)
    for item in updated_items_ids:
        Trace.objects.create(
            created_by=reporter,
            modified_by=reporter,
            item_id=item,
            location=source_location,
            reporter_source="POS_DEVICE",
            action="SALE",
            description=description,
            reporter=reporter,
            customer=customer
        )


def return_item(item: list, destination_location: object, reporter: object, customer: object, description: str):
    updated_items_ids = move_items(items=[item], destination=destination_location,
                                   reporter=reporter)
    for item in updated_items_ids:
        Trace.objects.create(
            created_by=reporter,
            modified_by=reporter,
            item_id=item,
            location=destination_location,
            reporter_source="FIXED_PORTAL",
            action="RETURN",
            description=description,
            reporter=reporter,
            customer=customer
        )


def register_reading_trace(item, location, reporter, customer, description: str):
    Trace.objects.create(
        created_by=reporter,
        modified_by=reporter,
        item_id=item,
        location=location,
        reporter_source="FIXED_PORTAL",
        action="READING",
        description=description,
        reporter=reporter,
        customer=customer
    )


def create_rfid_move_lines_from_items(move: Move, items: list,  timestamp_ms=float):
    if timestamp_ms:
        timestamp = float(timestamp_ms) / 1000
    else:
        timestamp = time.time()
    new_lines = []
    skus_dict = arrange_items_by_sku(items)
    skus_dict = utils.group_items_by_sku_and_location(items_ids=items)

    for sku_id, location_items_dict in skus_dict.items():
        for location_id, item_list in location_items_dict.items():
            items_dict = {"items": [item.pk for item in item_list]}
            new_lines.append(
                MoveLine(
                    move=move,
                    status=move.status,
                    sku_id=sku_id,
                    amount=len(item_list),
                    items=items_dict,
                    received_items=items_dict,
                    timestamp=timestamp,
                    sub_location_id=location_id,
                    created_by=move.created_by,
                    modified_by=move.modified_by
                )
            )
    MoveLine.objects.bulk_create(new_lines)


def create_rfid_move(node: Node, reporter: object, customer: object, items: list, move_type: str,
                     source_reporter='FIXED_PORTAL', timestamp_ms=float):
    move = Move.objects.create(
        source_id=node.location_id,
        destination_id=node.location_id,
        type=move_type,
        reporter_source=source_reporter,
        status="COMPLETED",
        created_by=reporter,
        modified_by=reporter,
        customer=customer
    )
    create_rfid_move_lines_from_items(move=move, items=items, timestamp_ms=timestamp_ms)
    return move


def process_rfid_activity(readings: list, node: Node, timestamp: float, action: str, reporter: User,
                          customer: Customer):
    items = []

    for reading in readings:
        items.append(reading['epc'])

    new_items = rfid_utils.get_items_available(readings=readings, customer_id=customer.id)
    new_readings = [i for i in readings if (i['epc'] in new_items)]

    for reading in new_readings:
        epc = reading['epc']
        user_memory = reading['user_memory']
        tid = reading['tid']
        antenna = reading['antenna']
        description = reading.get("description")

        RFIDActivity.objects.create(
            epc=epc,
            user_memory=user_memory,
            tid=tid,
            antenna=antenna,
            node=node,
            timestamp=timestamp,
            customer=customer,
            action=action
        )

        if action == "IN":
            ingress_item(item=epc, destination_location=node.location, reporter=reporter, customer=customer,
                         description=description)
        elif action == "OUT":
            take_out_item(item=epc, source_location=node.location, reporter=reporter, customer=customer,
                          description=description)
        elif action == "SALE":
            sale_item(item=epc, source_location=node.location, reporter=reporter, customer=customer,
                      description=description)
        elif action == "RETURN":
            return_item(item=epc, destination_location=node.location, reporter=reporter, customer=customer,
                        description=description)
        else:
            register_reading_trace(item=epc, location=node.location, reporter=reporter, customer=customer,
                                   description=description)

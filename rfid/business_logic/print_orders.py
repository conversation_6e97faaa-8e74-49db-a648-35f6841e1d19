# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import functools
import logging

from django.db import transaction
from rest_framework import status
from rest_framework.response import Response

import inventory
from inventory import business_logic
from inventory.models import Item, Trace, SKUType
from operations.models import Pur<PERSON>Order, ProductionOrder
from rfid.epc import sgtin, grai

from ..models import PrintOrder, GTINPrintOrderLine


def create_gtin_lines_from_array(order, lines):
    new_lines = []
    for line in lines:
        new_lines.append(
            GTINPrintOrderLine(
                print_order=order,
                sku=line['sku'],
                amount=line['amount'],
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    GTINPrintOrderLine.objects.bulk_create(new_lines)
    return new_lines


def create_gtin_lines_from_array_purchase(order, lines):
    new_lines = []
    for line in lines:
        new_lines.append(
            GTINPrintOrderLine(
                print_order=order,
                sku=line.sku,
                amount=line.amount,
                created_by=order.created_by,
                modified_by=order.modified_by
            )
        )
    GTINPrintOrderLine.objects.bulk_create(new_lines)

    return new_lines

def generate_gtin_items(order: PrintOrder, coding: str):
    """
    Generate order's items GTIN lines
    """
    for line in order.gtin_lines.all():
        object_type_sku = SKUType.objects.get(sku__id=line.sku.pk).coding_type
        line.generate_item_ids(coding=object_type_sku)


def generate_item_ids(order: PrintOrder, coding: str):
    # Add more item generation methods when necessary
    # Ex: GIAI, GRAI, etc..
    """
    Generate item ids (epcs) for given order
    """
    generate_gtin_items(order, coding)
    order.serial_numbers_generated = True
    order.item_ids_generated = True
    order.save()


def issue_items(order: PrintOrder, coding: str):
    """
    Issue Print Order items
    """
    if order.item_ids_generated is False:
        generate_item_ids(order)
    type = None
    if order.production_order:
        type = order.production_order.item_type
    elif order.purchase_order:
        type = order.purchase_order.item_type
    # Create Items
    new_items = []
    for line in order.gtin_lines.all():

        epcs = line.items_to_create_list
        if epcs:
            for epc in epcs:
                new_epc = None
                if coding == 'GRAI_96':
                    new_epc = grai.GRAI96()
                elif coding == 'SGTIN_96':
                    new_epc = sgtin.SGTIN96()
                new_epc.from_hex(epc)

                new_items.append(
                    Item(
                        pk=epc,
                        sku=line.sku,
                        type=type,
                        serial=int(new_epc.get_field_value("serial_number")),
                        state="PRESENT",
                        batch_number=order.batch_number,
                        batch_type=order.batch_type,
                        current_location=order.location,
                        created_by=order.created_by,
                        modified_by=order.modified_by,
                        customer=line.sku.customer,
                    )
                )
            result_items = list(set(line.created_items["epcs"] + line.items_to_create["epcs"]))
            line.created_items["epcs"] = result_items
            line.items_to_create["epcs"] = []
            line.save()
        else:
            continue

    # Create Issue Traces for Items.
    new_traces = []
    for item in new_items:
        new_traces.append(
            Trace(
                item=item,
                location=order.location,
                reporter=order.created_by,
                reporter_source="PRINTER",
                action="PRINT",
                created_by=order.created_by,
                modified_by=order.modified_by,
                customer=item.sku.customer,
            )
        )

    # Save Changes.
    with transaction.atomic():
        items = Item.objects.bulk_create(new_items)
        if items:
            Trace.objects.bulk_create(new_traces)
            order.items_issued = True
            order.save()
            logging.info("{total_items} Issued successfully from  print order {print_order_id}".format(
                total_items=len(new_items),
                print_order_id=order.pk
            ))
        else:
            for line in order.gtin_lines.all():
                order.status = 'IN_PROCESS'
                line.items_to_create["epcs"] = line.created_items["epcs"]
                line.created_items["epcs"] = []
                line.save()
                order.save()

            return False


def create_from_order(order, lines):
    """
     Creates Print Order from Any type of Order
    """
    if isinstance(order, PurchaseOrder):
        return create_from_purchase_order(order, lines)
    elif isinstance(order, ProductionOrder):
        return create_from_production_order(order, lines)
    else:
        raise TypeError("Order Class not identified")


def create_from_purchase_order(order, lines):
    """
    Creates Print Order from Purchase Order
    """
    new_order = PrintOrder.objects.create(
        location=order.entry_location,
        batch_number=order.external_id,
        batch_type="PURCHASE_ORDER",
        provider=order.provider,
        purchase_order=order,
        created_by=order.created_by,
        modified_by=order.modified_by,
        customer=order.customer
    )
    create_gtin_lines_from_array_purchase(order=new_order, lines=lines)
    return new_order


def create_from_production_order(order, lines):
    """
    Creates Print Order from Production Order
    """
    new_order = PrintOrder.objects.create(
        location=order.entry_location,
        batch_number=order.pk,
        batch_type="PRODUCTION_ORDER",
        provider=order.provider,
        production_order=order,
        created_by=order.created_by,
        modified_by=order.modified_by,
        customer=order.customer
    )
    create_gtin_lines_from_array(order=new_order, lines=lines)
    return new_order


def validate_items_gtin_line(lines, order):
    list_received_items = sum([line['items'] for line in lines], [])
    list_issued_items = order.issued_items_list()

    content_equal = 0
    for item in list_received_items:
        if item not in list_issued_items:
            content_equal += 1

    if content_equal > 0:
        return False
    else:
        return True


def execute_progress_print_order(lines, order):
    for line in lines:
        sku_id = line['sku_id']
        items = line['items']
        gtin_object = order.gtin_lines.get(sku_id=sku_id)
        if items:
            result_items = list(set(gtin_object.items_to_create["epcs"] + items))
            gtin_object.items_to_create["epcs"] = result_items
        else:
            result_items = list(set(gtin_object.items_to_create["epcs"] + gtin_object.issued_items["epcs"]))
            gtin_object.items_to_create["epcs"] = result_items
        gtin_object.save()
    return order


def clone_print(order, reporter, lines):
    item_cache = {}
    trace_data = []

    for line in lines:
        cloned_amount = line["cloned_amount"]
        items = line['items']
        for item_id in items:
            if item_id in item_cache:
                item = item_cache[item_id]
            else:
                item = Item.objects.get(pk=item_id)
                item_cache[item_id] = item

            item.total_amount_cloned = (getattr(item, 'total_amount_cloned', 0) or 0) + cloned_amount
            item.save()

            trace_data.append(Trace(
                created_by=reporter,
                modified_by=reporter,
                location=order.location,
                customer_id=order.customer.pk,
                reporter_source="PRINTER",
                action="CLONED",
                reporter=reporter,
                description=None,
                additional_data={
                    "model_pk": order.pk,
                    "model_external_id": order.external_id,
                    "model_name": type(order).__name__
                },
                item_id=item
            ))

    Trace.objects.bulk_create(trace_data)

from rfid.business_logic.rfid_activity import ingress_item, take_out_item, register_reading_trace
from rfid.models import RFIDActivityMobile


def process_rfid_activity(items, location, action, reporter, customer):
    for item in items:

        RFIDActivityMobile.objects.create(
            epc=item,
            action=action,
            location=location
        )

        if action == "IN":
            ingress_item(item=item, destination_location=location, reporter=reporter, customer=customer, description=None)
        elif action == "OUT":
            take_out_item(item=item, source_location=location, reporter=reporter, customer=customer, description=None)
        else:
            register_reading_trace(item=item, location=location, customer=customer, reporter=reporter, description=None)
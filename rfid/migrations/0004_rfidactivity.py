# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-03-11 20:29
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0007_auto_20190712_1606'),
        ('rfid', '0003_historicalgtinprintorderline_historicalprintorder'),
    ]

    operations = [
        migrations.CreateModel(
            name='RFIDActivity',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('antenna', models.IntegerField()),
                ('user_memory', models.CharField(blank=True, max_length=32, null=True)),
                ('epc', models.CharField(max_length=150)),
                ('tid', models.CharField(blank=True, max_length=16, null=True)),
                ('action', models.CharField(choices=[('IN', 'In'), ('OUT', 'Out'), ('READING', 'Reading'), ('SALE', 'Sale'), ('RETURN', 'Return')], default='READING', max_length=15)),
                ('timestamp', models.FloatField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customers.Customer')),
                ('node', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rfid.Node')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
    ]

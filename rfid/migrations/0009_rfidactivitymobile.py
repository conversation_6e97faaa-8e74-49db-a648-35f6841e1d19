# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2022-10-05 15:36
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0048_auto_20221004_2001'),
        ('rfid', '0008_node_customer_properties'),
    ]

    operations = [
        migrations.CreateModel(
            name='RFIDActivityMobile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('epc', models.CharField(max_length=150)),
                ('tid', models.CharField(blank=True, max_length=16, null=True)),
                ('action', models.CharField(choices=[('IN', 'In'), ('OUT', 'Out'), ('READING', 'Reading')], default='READING', max_length=15)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2020-05-04 21:10
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('operations', '0012_auto_20200507_2035'),
        ('rfid', '0005_auto_20200507_2035'),
    ]

    operations = [
        migrations.AddField(
            model_name='gtinprintorderline',
            name='created_items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'epcs': []}, null=True),
        ),
        migrations.AddField(
            model_name='gtinprintorderline',
            name='missing_items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'epcs': []}, null=True),
        ),
        migrations.AddField(
            model_name='historicalgtinprintorderline',
            name='created_items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'epcs': []}, null=True),
        ),
        migrations.AddField(
            model_name='historicalgtinprintorderline',
            name='missing_items',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'epcs': []}, null=True),
        )
    ]

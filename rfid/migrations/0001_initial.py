# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-04-15 17:05
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields
import django.contrib.postgres.fields.jsonb
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0002_auto_20190415_1203'),
        ('operations', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BrandTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.Char<PERSON>ield(max_length=140)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='GTINPrintOrderLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('initial_serial', models.BigIntegerField(blank=True, default=-1, null=True)),
                ('amount', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'epcs': []}, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Node',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('mac', models.CharField(max_length=17, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=140)),
                ('description', models.CharField(blank=True, max_length=140, null=True)),
                ('action', models.CharField(choices=[('IN', 'In'), ('OUT', 'Out'), ('READING', 'Reading'), ('CUSTOM', 'Custom'), ('IN_ONCE', 'In once'), ('OUT_ONCE', 'Out once'), ('SALE', 'Sale')], default='IN', max_length=20)),
                ('profile', models.CharField(choices=[('FIXED', 'Fixed'), ('POS', 'Pos'), ('ENCODER', 'Encoder'), ('ITEM_VALIDATOR', 'Item validator'), ('ITEM_HISTORY', 'Item history'), ('NOT_IMPLEMENTED', 'Not implemented'), ('TICKET', 'Ticket'), ('DOCK_DOOR', 'Dock  door'), ('SHELF', 'Shelf'), ('PARKING', 'Parking')], default='FIXED', max_length=20)),
                ('batch_max_size', models.IntegerField(default=200)),
                ('time_remove_old_tags', models.IntegerField(default=10000)),
                ('waiting_window', models.IntegerField(default=2000)),
                ('time_zone_hours', models.IntegerField(default=-5)),
                ('backup_send_hour', models.IntegerField(default=23, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(23)])),
                ('save_readings_local', models.BooleanField(default=False)),
                ('hardware_properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('gui_properties', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('epc_hex_read_length', models.IntegerField(default=24)),
                ('epc_hex_write_length', models.IntegerField(default=24)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['mac'],
            },
        ),
        migrations.CreateModel(
            name='PrintOrder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('external_id', models.CharField(blank=True, max_length=30, null=True, unique=True)),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('IN_PROCESS', 'In Process'), ('PRINTED', 'Printed'), ('ABORTED', 'Aborted')], default='ISSUED', max_length=20)),
                ('prefix', models.CharField(default='PO', max_length=2)),
                ('url_data', models.TextField(blank=True, null=True)),
                ('serial_numbers_generated', models.BooleanField(default=False)),
                ('item_ids_generated', models.BooleanField(default=False)),
                ('items_issued', models.BooleanField(default=False)),
                ('batch_number', models.CharField(blank=True, max_length=100, null=True)),
                ('batch_type', models.CharField(choices=[('PRODUCTION_ORDER', 'Production Order'), ('PURCHASE_ORDER', 'Purchase Order'), ('INITIAL_INVENTORY', 'Initial Inventory')], default='PRODUCTION_ORDER', max_length=20)),
                ('user_memory', models.CharField(blank=True, max_length=140, null=True)),
                ('total_tags_used', models.IntegerField(blank=True, default=0, null=True)),
                ('total_tags_void', models.IntegerField(blank=True, default=0, null=True)),
                ('total_tags_programed', models.IntegerField(blank=True, default=0, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='print_orders', to='inventory.Location')),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('printed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('production_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='operations.ProductionOrder')),
                ('provider', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='operations.Contact')),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='PrintTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=140)),
                ('description', models.TextField(blank=True, null=True)),
                ('url_template', models.TextField()),
                ('print_offset', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), default=[0, 0], size=2)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Reader',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('mac', models.CharField(blank=True, max_length=17, null=True)),
                ('name', models.CharField(max_length=140)),
                ('antenna_sequence', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), size=32)),
                ('persist_time', models.IntegerField(default=30000)),
                ('user_name', models.CharField(max_length=140)),
                ('password', models.CharField(max_length=140)),
                ('rf_power', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), size=32)),
                ('dhcp_enabled', models.BooleanField(default=True)),
                ('private_ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('net_mask', models.GenericIPAddressField(blank=True, null=True)),
                ('gateway', models.GenericIPAddressField(blank=True, null=True)),
                ('dns', django.contrib.postgres.fields.ArrayField(base_field=models.GenericIPAddressField(), blank=True, null=True, size=4)),
                ('serial_number', models.CharField(blank=True, max_length=140, null=True, unique=True)),
                ('serial_reader_port', models.CharField(blank=True, max_length=140, null=True)),
                ('parameters', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='ReaderBrand',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=140)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='TagChipBrand',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=140)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='TagReference',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=140)),
                ('dimensions', django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), size=2)),
                ('parameters', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('photo_url', models.URLField(blank=True, null=True)),
                ('margins', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('brand', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='rfid.BrandTag')),
                ('chip_brand', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='rfid.TagChipBrand')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
        migrations.AddField(
            model_name='reader',
            name='brand',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='rfid.ReaderBrand'),
        ),
        migrations.AddField(
            model_name='reader',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='reader',
            name='modified_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='reader',
            name='node',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='readers', to='rfid.Node'),
        ),
        migrations.AddField(
            model_name='printtemplate',
            name='tag_reference',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='rfid.TagReference'),
        ),
        migrations.AddField(
            model_name='printorder',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='rfid.PrintTemplate'),
        ),
        migrations.AddField(
            model_name='gtinprintorderline',
            name='print_order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gtin_lines', to='rfid.PrintOrder'),
        ),
        migrations.AddField(
            model_name='gtinprintorderline',
            name='sku',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.SKU'),
        ),
    ]

# -*- coding: utf-8 -*-
# Generated by Django 1.11.15 on 2019-05-16 21:33
from __future__ import unicode_literals

from django.conf import settings
import django.contrib.postgres.fields.jsonb
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0003_historicalcustomer'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('operations', '0007_historicalpackingline_historicalproductionorder_historicalproductionorderline'),
        ('inventory', '0013_auto_20190509_1806'),
        ('rfid', '0002_printorder_customer'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalGTINPrintOrderLine',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('initial_serial', models.BigIntegerField(blank=True, default=-1, null=True)),
                ('amount', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('items', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default={'epcs': []}, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('print_order', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='rfid.PrintOrder')),
                ('sku', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.SKU')),
            ],
            options={
                'verbose_name': 'historical gtin print order line',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalPrintOrder',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', models.DateTimeField(blank=True, editable=False)),
                ('modified', models.DateTimeField(blank=True, editable=False, null=True)),
                ('external_id', models.CharField(blank=True, db_index=True, max_length=30, null=True)),
                ('status', models.CharField(choices=[('ISSUED', 'Issued'), ('IN_PROCESS', 'In Process'), ('PRINTED', 'Printed'), ('ABORTED', 'Aborted')], default='ISSUED', max_length=20)),
                ('prefix', models.CharField(default='PO', max_length=2)),
                ('url_data', models.TextField(blank=True, null=True)),
                ('serial_numbers_generated', models.BooleanField(default=False)),
                ('item_ids_generated', models.BooleanField(default=False)),
                ('items_issued', models.BooleanField(default=False)),
                ('batch_number', models.CharField(blank=True, max_length=100, null=True)),
                ('batch_type', models.CharField(choices=[('PRODUCTION_ORDER', 'Production Order'), ('PURCHASE_ORDER', 'Purchase Order'), ('INITIAL_INVENTORY', 'Initial Inventory')], default='PRODUCTION_ORDER', max_length=20)),
                ('user_memory', models.CharField(blank=True, max_length=140, null=True)),
                ('total_tags_used', models.IntegerField(blank=True, default=0, null=True)),
                ('total_tags_void', models.IntegerField(blank=True, default=0, null=True)),
                ('total_tags_programed', models.IntegerField(blank=True, default=0, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='customers.Customer')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='inventory.Location')),
                ('modified_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('printed_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('production_order', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='operations.ProductionOrder')),
                ('provider', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='operations.Contact')),
                ('template', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='rfid.PrintTemplate')),
            ],
            options={
                'verbose_name': 'historical print order',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]

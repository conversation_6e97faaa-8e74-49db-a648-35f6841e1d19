# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from common.models import User
# Create your tests here.
from django.contrib.auth.models import Permission
from django.test import Client
from django.urls import reverse
from inventory.models import SKU, SKUGroup, Location, Warehouse, Item, SubLocation
from operations.models import Packing, PackingLine, WorkOrder, WorkOrderLine, Contact
from rest_framework.test import APITestCase
from rest_framework.utils import json
from rfid.models import Node, PrintOrder, GTINPrintOrderLine


class OperationPackingAPITestCase(APITestCase):
    def setUp(self):
        self.client = Client()
        self.normal_user = User.objects.create_user(
            username="monkey", password="monkey1234", email="<EMAIL>", is_staff=True
        )
        self.normal_user.save()

        self.super_user = User.objects.create_superuser(
            username="super_monkey", password="super_monkey1234", email="<EMAIL>"
        )
        self.super_user.save()
        self.contact = Contact.objects.create(
            name='camilo',
            type_contact='PROVIDER',
            type_identification='NIT',
            identification='1239487566H'
        )
        self.contact.save()
        self.warehouse_mex = Warehouse.objects.create(
            name="MAIN WAREHOUSE MEXICO",
            display_name="Main - Warehouse -MEXICO",
        )
        self.warehouse_mex.save()

        # create source location.
        self.source = Location.objects.create(
            id=1,
            name="MAIN WAREHOUSE",
            display_name="Main - Warehouse",
            type='SIMPLE',
            warehouse=self.warehouse_mex,
        )
        self.source.save()
        self.destination = Location.objects.create(
            id=2,
            name="MAIN STORE",
            display_name="Main - Store",
            type='SIMPLE'
        )
        self.destination.save()
        self.sub_location = SubLocation.objects.create(
            code='A1',
            location=self.source
        )
        self.sub_location.save()
        # create sku_group.

        self.sku_group = SKUGroup.objects.create(
            name='ZAPATOS',
        )
        self.sku_group.save()
        self.sku = SKU.objects.create(
            id=1,
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004068",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku.save()
        self.sku_two = SKU.objects.create(
            id=2,
            name="OLD_PARR_NEW_IBC_12YR_12X1000ML_40%",
            display_name="OLD PARR NEW IBC 12YR 12X1000ML 40%",
            filter=1,
            ean="5000281004069",
            reference="0107836",
            group=self.sku_group,
            company_prefix="591001",
            gtin_code="05000281004068",
            gtin_data_structure="GTIN_13",
            properties={
                "descripcion": "OLD PARR NEW IBC 12YR 12X1000ML 40%",
                "moneda": "$",
                "precio": "41.00",
                "codproveedor": "82451962",
                "codigosku": "01-07836"
            },
        )
        self.sku_two.save()
        self.item = Item.objects.create(
            id="9202226913468bc",
            serial=1,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item.save()
        self.item_two = Item.objects.create(
            id="9202226913468bd",
            serial=2,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,

        )
        self.item_two.save()

        self.item_three = Item.objects.create(
            id="9202226913468be",
            serial=3,
            current_location=self.source,
            state="PRESENT",
            batch_type="PRODUCTION_ORDER",
            batch_number="102030",
            properties=None,
            locked=False,
            quality_check=False,
            completed_check=False,
            sku=self.sku,
            current_sub_location=self.sub_location,
        )
        self.item_three.save()

        self.work_order = WorkOrder.objects.create(
            finished_date="2018-08-20",
            finished=True,
            assigned_to_id=self.normal_user.id,
            destination_id=self.destination.id,
            source_id=self.source.id,
            external_id="AA1",
            id=1
        )
        self.work_order.save()

        self.work_order_line = WorkOrderLine.objects.create(
            work_order_id=1,
            amount=2,
            sku_id=self.sku.id
        )
        self.work_order_line.save()

        self.packing = Packing.objects.create(
            id=1,
            external_id='A1',
            status='VERIFIED'
        )
        self.packing.save()

        self.packing_line = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line.save()
        self.packing_two = Packing.objects.create(
            id=2,
            external_id='A2',
            status='VERIFIED'
        )
        self.packing_two.save()

        self.packing_line_two = PackingLine.objects.create(
            packing=self.packing,
            sku_id=self.sku.id,
            amount=1,
            items={"items": [self.item.id]}
        )
        self.packing_line_two.save()

        self.node = Node.objects.create(
            mac="1C-39-47-AB-9E-3E",
            name="Apes Yeison Lab",
            description="Apes Yeison Lab",
            location=self.destination,
            action="IN",
            profile="ITEM_VALIDATOR",
            user=self.normal_user

        )
        self.node.save()

        self.print_order = PrintOrder.objects.create(
            external_id="ABC1",
            location=self.source,
            printed_by=self.normal_user
        )
        self.print_order.save()
        self.print_order_two = PrintOrder.objects.create(
            external_id="ABC2",
            location=self.source,
            printed_by=self.normal_user,
            status='IN_PROCESS'
        )
        self.print_order_two.save()

        self.gtin_lines_two = GTINPrintOrderLine.objects.create(
            print_order=self.print_order_two,
            sku=self.sku_two,
            amount=10
        )
        self.gtin_lines_two.save()

        self.gtin_lines = GTINPrintOrderLine.objects.create(
            print_order=self.print_order,
            sku=self.sku,
            amount=10
        )
        self.gtin_lines.save()

        permission = Permission.objects.get(name='Can add print order')
        permission_two = Permission.objects.get(name='Can add node')

        permission_three = Permission.objects.get(name='Can add reader')
        u = User.objects.get(pk=self.normal_user.pk)
        u.user_permissions.add(permission)
        u.user_permissions.add(permission_three)
        u.user_permissions.add(permission_two)

    def test_GETing_rfid_activities(self):
        url= reverse("rfid-activity")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_rfid_activities(self):
        data = {
            "action": "IN",
            "timestamp": 1.53263643744e+12,
            "node_id": self.node.pk,
            "readings": [
                "30103ACA4B84370000000056"
            ]
        }
        url = reverse("rfid-activity")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 201)


    def test_POSTing_a_new_print_order(self):
        data = {
            "external_id": "ABC3",
            "lines": {
                "gtin": [{
                        "sku_id": self.sku.pk,
                        "amount": 2
                    }
                ]
            },
            "location_id": self.source.pk
        }
        url= reverse("print-order-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_GETing_a_gtin_lines(self):
        url = reverse("print-order-gtin-lines", args=[self.print_order.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_a_init_print(self):
        url = reverse("print-order-init-print", args=[self.print_order.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)

    def test_POSTing_a_finish_print(self):
        data = {
            'total_tags_used': 2

        }
        url = reverse("print-order-finish-print", args=[self.print_order_two.pk])
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 200)

    def test_POSTing_a_purchase_order_print(self):
        data = {
            "external_id": "ABC1",
            "provider_id": self.contact.pk,
            "lines": [
                {"sku_id": self.sku.id, "amount": 2}
            ],
            "entry_location_id": self.destination.pk
        }
        url = reverse("rfid-purchase-order-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)

    def test_POSTing_a_production_order_print(self):
        data = {
            "external_id": "ABC1",
            "provider_id": self.contact.pk,
            "lines": [
                {"sku_id": self.sku.id, "amount": 2}
            ],
            "entry_location_id": self.destination.pk
        }
        url = reverse("rfid-production-order-list")
        login = self.client.login(username="monkey", password="monkey1234")
        self.assertEqual(login, True)
        response = self.client.post(url, data=json.dumps(data), content_type='application/json')
        self.assertEqual(response.status_code, 201)
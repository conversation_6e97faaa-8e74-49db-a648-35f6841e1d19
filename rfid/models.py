# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import uuid

from common.models import BaseModel, User
from django.contrib.postgres.fields import <PERSON><PERSON>yField
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models
from inventory.models import Location, Item, SKU, SKUGroup
from customers.models import Customer
from operations.models import Contact, PurchaseOrder, ProductionOrder, PurchaseOrderLine, ProductionOrderLine
from rfid.epc import sgtin, grai
from rfid.exceptions import StatusUnavailableForProcess, StatusUnavailableForPrint

from simple_history.models import HistoricalRecords


class Node(BaseModel):
    NODE_ACTION = (
        ('IN', 'In'),
        ('OUT', 'Out'),
        ('READING', 'Reading'),
        ('CUSTOM', 'Custom'),
        ('IN_ONCE', 'In once'),
        ('OUT_ONCE', 'Out once'),
        ('SALE', 'Sale')
    )
    NODE_PROFILE = (
        ('FIXED', 'Fixed'),
        ('POS', 'Pos'),
        ('ENCODER', 'Encoder'),
        ('ITEM_VALIDATOR', 'Item validator'),
        ('ITEM_HISTORY', 'Item history'),
        ('NOT_IMPLEMENTED', 'Not implemented'),
        ('TICKET', 'Ticket'),
        ('DOCK_DOOR', 'Dock  door'),
        ('SHELF', 'Shelf'),
        ('PARKING', 'Parking')

    )

    mac = models.CharField(primary_key=True, max_length=17)
    name = models.CharField(max_length=140)
    description = models.CharField(max_length=140, null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.PROTECT)
    action = models.CharField(max_length=20, choices=NODE_ACTION, default='IN')
    profile = models.CharField(max_length=20, choices=NODE_PROFILE, default='FIXED')
    user = models.ForeignKey(User, null=True, blank=True, on_delete=models.PROTECT)
    batch_max_size = models.IntegerField(default=200)
    time_remove_old_tags = models.IntegerField(default=10000)
    waiting_window = models.IntegerField(default=2000)
    time_zone_hours = models.IntegerField(default=-5)
    backup_send_hour = models.IntegerField(default=23, validators=[MinValueValidator(0), MaxValueValidator(23)])
    save_readings_local = models.BooleanField(default=False)
    hardware_properties = JSONField(null=True, blank=True, default=dict)
    gui_properties = JSONField(null=True, blank=True, default=dict)
    customer_properties = JSONField(null=True, blank=True, default=dict)
    epc_hex_read_length = models.IntegerField(default=24)
    epc_hex_write_length = models.IntegerField(default=24)

    class Meta:
        ordering = ['mac']

    def __str__(self):
        return '{}-{}'.format(self.pk, self.location)


class ReaderBrand(BaseModel):
    name = models.CharField(max_length=140)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}'.format(self.name)


class Reader(BaseModel):
    class NetworkSettings(object):
        def __init__(self, dhcp_enabled, private_ip_address, net_mask, gateway, dns):
            self.dhcp_enabled = dhcp_enabled or False
            self.private_ip_address = private_ip_address or None
            self.net_mask = net_mask or None
            self.gateway = gateway or None
            self.dns = dns or []

    # Fields
    mac = models.CharField(max_length=17, null=True, blank=True)
    name = models.CharField(max_length=140)
    brand = models.ForeignKey(ReaderBrand, on_delete=models.PROTECT)
    node = models.ForeignKey(Node, related_name='readers', null=True, blank=True, on_delete=models.PROTECT)
    antenna_sequence = ArrayField(models.IntegerField(), size=32)
    persist_time = models.IntegerField(default=30000)
    user_name = models.CharField(max_length=140)
    password = models.CharField(max_length=140)
    rf_power = ArrayField(models.IntegerField(), size=32)
    dhcp_enabled = models.BooleanField(default=True)
    private_ip_address = models.GenericIPAddressField(null=True, blank=True)
    net_mask = models.GenericIPAddressField(null=True, blank=True)
    gateway = models.GenericIPAddressField(null=True, blank=True)
    dns = ArrayField(models.GenericIPAddressField(), size=4, null=True, blank=True)
    serial_number = models.CharField(max_length=140, unique=True, null=True, blank=True)
    serial_reader_port = models.CharField(max_length=140, null=True, blank=True)
    parameters = JSONField(null=True, blank=True, default=dict)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-[{}]'.format(self.brand, self.serial_number)

    def get_network_settings(self):
        """
        Returns Networks settings object which contains dhcp_enabled,
        private_ip_address,net_mask, gateway and dns
        :return:
        """
        return self.NetworkSettings(self.dhcp_enabled,
                                    self.private_ip_address,
                                    self.net_mask,
                                    self.gateway,
                                    self.dns)


class BrandTag(BaseModel):
    name = models.CharField(max_length=140)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}'.format(self.name)


class TagChipBrand(BaseModel):
    name = models.CharField(max_length=140)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}'.format(self.name)


class TagReference(BaseModel):
    brand = models.ForeignKey(BrandTag, null=True, blank=True, on_delete=models.PROTECT)
    name = models.CharField(max_length=140)
    dimensions = ArrayField(models.IntegerField(), size=2)
    chip_brand = models.ForeignKey(TagChipBrand, null=True, blank=True, on_delete=models.PROTECT)
    parameters = JSONField(null=True, blank=True, default=dict)
    photo_url = models.URLField(null=True, blank=True)
    margins = JSONField(null=True, blank=True, default=dict)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.brand, self.name)


class PrintTemplate(BaseModel):
    name = models.CharField(max_length=140)
    description = models.TextField(null=True, blank=True)
    url_template = models.TextField()
    print_offset = ArrayField(models.IntegerField(), size=2, default=[0, 0])
    tag_reference = models.ForeignKey(TagReference, null=True, blank=True, on_delete=models.PROTECT)

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}'.format(self.name)


class PrintOrder(BaseModel):
    PRINT_STATUS = (
        ("ISSUED", "Issued"),
        ("IN_PROCESS", "In Process"),
        ("PRINTED", "Printed"),
        ("ABORTED", "Aborted")
    )
    external_id = models.CharField(max_length=30, null=True, blank=True, unique=True)
    status = models.CharField(max_length=20, choices=PRINT_STATUS, default="ISSUED")
    prefix = models.CharField(max_length=2, default='PO')
    location = models.ForeignKey(Location, related_name='print_orders', on_delete=models.PROTECT)
    template = models.ForeignKey(PrintTemplate, null=True, blank=True, on_delete=models.PROTECT)
    url_data = models.TextField(null=True, blank=True)
    serial_numbers_generated = models.BooleanField(default=False)
    item_ids_generated = models.BooleanField(default=False)
    items_issued = models.BooleanField(default=False)
    batch_number = models.CharField(max_length=100, null=True, blank=True)
    batch_type = models.CharField(max_length=20, choices=Item.BATCH_TYPE, default="PRODUCTION_ORDER")
    user_memory = models.CharField(max_length=140, null=True, blank=True)
    total_tags_used = models.IntegerField(default=0, null=True, blank=True)
    total_tags_void = models.IntegerField(default=0, null=True, blank=True)
    total_tags_programed = models.IntegerField(default=0, null=True, blank=True)
    printed_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.PROTECT)
    provider = models.ForeignKey(Contact, null=True, blank=True, on_delete=models.PROTECT)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    purchase_order = models.ForeignKey(PurchaseOrder, null=True, blank=True, on_delete=models.PROTECT)
    production_order = models.ForeignKey(ProductionOrder, null=True, blank=True, on_delete=models.PROTECT)
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}'.format(self.prefix, self.pk)

    def init_print(self, user):
        if self.status != "ISSUED" and self.item_ids_generated == False and \
                self.serial_numbers_generated == False:
            raise StatusUnavailableForProcess
        else:
            self.printed_by = user
            self.status = "IN_PROCESS"
            self.save()

    def finish_print(self, total_tags_used, total_tags_void, total_tags_programed):
        if self.status == "IN_PROCESS":
            self.status = "PRINTED"
            self.total_tags_used = total_tags_used
            self.total_tags_void = total_tags_void
            self.total_tags_programed = total_tags_programed
            self.save()
        else:
            raise StatusUnavailableForPrint

    def aborted_print(self):
        self.status = "ABORTED"
        purchase_order = self.purchase_order
        production_order = self.production_order
        if purchase_order:
            purchase_order.status = "CANCELLED"
            purchase_order.save()
            purchase_order_lines = PurchaseOrderLine.objects.filter(purchase_order_id=self.purchase_order.id)
            for order_line in purchase_order_lines:
                order_line.status = "CANCELLED"
                order_line.save()

        if production_order:
            production_order.status = "CANCELLED"
            production_order.save()
            production_order_lines = ProductionOrderLine.objects.filter(production_order_id=self.production_order.id)
            for order_line in production_order_lines:
                order_line.status = "CANCELLED"
                order_line.save()
        self.save()

    def issued_items_list(self):
        print_order_items = GTINPrintOrderLine.objects.filter(print_order=self). \
            values_list('issued_items', flat=True)
        items_list = ','.join(','.join(item["epcs"]) for item in list(print_order_items))
        return list(filter(None, list(items_list.split(","))))


class GTINPrintOrderLine(BaseModel):
    print_order = models.ForeignKey(PrintOrder, related_name='gtin_lines', on_delete=models.PROTECT)
    sku = models.ForeignKey(SKU, on_delete=models.PROTECT)
    initial_serial = models.BigIntegerField(null=True, blank=True, default=-1)
    amount = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    purchase_line = models.ForeignKey(PurchaseOrderLine, null=True, on_delete=models.PROTECT)
    issued_items = JSONField(null=True, blank=True, default=dict([("epcs", [])]))  # {"epcs":["456786", "56723"]}
    items_to_create = JSONField(null=True, blank=True, default=dict([("epcs", [])]))  # {"epcs":["456786", "56723"]}
    created_items = JSONField(null=True, blank=True, default=dict([("epcs", [])]))  # {"epcs":["456786", "56723"]}
    history = HistoricalRecords()

    class Meta:
        ordering = ['-id']

    def __str__(self):
        return '{}-{}({})'.format(self.print_order, self.sku, self.amount)

    @property
    def items_list(self):
        if self.issued_items:
            return self.issued_items["epcs"]
        else:
            return []

    @property
    def items_to_create_list(self):
        if self.items_to_create:
            return self.items_to_create["epcs"]
        else:
            return []

    @property
    def total_items(self):
        return len(self.items_list)

    def add_items(self, issued_items=[], replace=False, commit=True):

        if replace:
            issued_items = dict({"epcs": issued_items})
            self.issued_items = issued_items
        else:
            new_item_list = list(
                set(self.items_list + issued_items)
            )
            issued_items = dict({"epcs": new_item_list})
            self.issued_items = issued_items

        if commit:
            self.save()

    def _generate_initial_serial(self, coding: str):
        """
        Generate and assign initial serial
        :return: 
        """
        current_serial = None
        if coding == 'GRAI_96':
            current_serial = SKUGroup.get_current_serial_and_increment(sku_group=self.sku.group_id,
                                                                       quantity=self.amount)
        elif coding == 'SGTIN_96':
            current_serial = SKU.get_current_serial_and_increment(sku=self.sku_id, quantity=self.amount)
        self.initial_serial = current_serial
        self.save()

    def generate_item_ids(self, coding: str):
        """
        Generate item ids (epcs)
        """

        if self.initial_serial == -1:
            self._generate_initial_serial(coding=coding)
        item_ids = []

        if coding == 'GRAI_96':
            for serial in range(self.initial_serial, self.initial_serial + self.amount):
                new_epc = grai.GRAI96()
                new_epc.encode(
                    company_prefix=self.sku.company_prefix,
                    item_group=str(self.sku.group.external_id).zfill(7),
                    filter_value=self.sku.group.filter,
                    serial_number=serial
                )
                # epc  hex representation not adds an L at the end of the string, so  we not remove it
                epc_hex = new_epc.to_hex()
                item_ids.append(epc_hex)
        elif coding == 'SGTIN_96':
            for serial in range(self.initial_serial, self.initial_serial + self.amount):
                new_epc = sgtin.SGTIN96()
                new_epc.encode(
                    company_prefix=self.sku.company_prefix,
                    item_reference=self.sku.reference,
                    filter_value=self.sku.filter,
                    serial_number=serial
                )
                # epc  hex representation not adds an L at the end of the string, so  we not remove it
                epc_hex = new_epc.to_hex()
                item_ids.append(epc_hex)
        self.add_items(item_ids)


class RFIDActivity(models.Model):
    RFID_ACTION = (
        ('IN', 'In'),
        ('OUT', 'Out'),
        ('READING', 'Reading'),
        ('SALE', 'Sale'),
        ('RETURN', 'Return')
    )
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    node = models.ForeignKey(Node, on_delete=models.CASCADE)
    antenna = models.IntegerField()
    user_memory = models.CharField(blank=True, max_length=32, null=True)
    epc = models.CharField(max_length=150)
    tid = models.CharField(blank=True, max_length=16, null=True)
    action = models.CharField(max_length=15, choices=RFID_ACTION, default="READING")
    timestamp = models.FloatField()
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.PROTECT)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-id']


class RFIDActivityMobile(models.Model):
    RFID_ACTION_MOBILE = (
        ("IN", "In"),
        ("OUT", "Out"),
        ("READING", "Reading")
    )
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    location = models.ForeignKey(Location, on_delete=models.PROTECT)
    epc = models.CharField(max_length=150)
    tid = models.CharField(blank=True, max_length=16, null=True)
    action = models.CharField(max_length=15, choices=RFID_ACTION_MOBILE, default="READING")
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-id']

# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from .models import Item, Location
from customers.models import Customer


def get_items_available(readings, customer_id):
    items = []
    for reading in readings:
        items.append(reading['epc'])

    new_items = Item.objects.filter(pk__in=items, sku__customer_id=customer_id) \
        .exclude(state='INACTIVE').values_list('id', flat=True)
    return new_items


def get_allowed_items(readings, customer_id, action):
    items = []
    state = None

    if action == 'RETURN':
        state = 'PRESENT'
    elif action == 'SALE':
        state = 'SOLD'

    for reading in readings:
        items.append(reading['epc'])

    new_items = []
    if state:
        new_items = Item.objects.filter(pk__in=items, sku__customer_id=customer_id,
                                        state=state).values_list('id', flat=True)
    if len(new_items) > 0:
        return False
    else:
        return True

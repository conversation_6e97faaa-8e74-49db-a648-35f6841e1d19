import coreapi
import coreschema
from rest_framework import schemas


class RFIDActivityViewSchema(schemas.AutoSchema):

    def get_manual_fields(self, path, method):
        extra_fields = []
        if method == 'POST':
            extra_fields = [
                coreapi.Field(
                    name="run_shopify",
                    description="Define audit finished (true) or audit in progress (false).",
                    location="query",
                    schema=coreschema.String(),
                    required=False
                )
            ]

        manual_fields = super().get_manual_fields(path, method)
        return manual_fields + extra_fields

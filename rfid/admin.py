# -*- coding: utf-8 -*-
from django.apps import apps
from django.contrib import admin

from .models import *

# auto-register all models
app = apps.get_app_config('rfid')


class NodeAdmin(admin.ModelAdmin):
    list_display = ('mac', 'name', 'location', 'action', 'user')
    raw_id_fields = ('location',)


class ReaderBrandAdmin(admin.ModelAdmin):
    list_display = ('id', 'name',)


class ReaderAdmin(admin.ModelAdmin):
    list_display = ('id', 'mac', 'name', 'brand', 'node', 'antenna_sequence')


class BrandTagAdmin(admin.ModelAdmin):
    list_display = ('id', 'name',)


class TagChipBrandAdmin(admin.ModelAdmin):
    list_display = ('id', 'name',)


class TagReferenceAdmin(admin.ModelAdmin):
    list_display = ('id', 'brand', 'name', 'dimensions', 'margins')


class PrintTemplateAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'url_template', 'print_offset', 'tag_reference')


class PrintOrderLineInLine(admin.TabularInline):
    fields = ('sku', 'initial_serial', 'amount', 'issued_items', 'items_to_create', 'created_items')
    model = GTINPrintOrderLine
    extra = 0
    show_change_link = True
    raw_id_fields = ('sku',)


class PrintOrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'external_id', 'status', 'location', 'customer', 'template', 'total_tags_used',
                    'serial_numbers_generated', 'batch_type', 'batch_number', 'total_tags_void', 'total_tags_programed')
    raw_id_fields = ('location', 'purchase_order', 'production_order', 'provider')
    inlines = [
        PrintOrderLineInLine
    ]
    readonly_fields = ('created', 'modified', 'prefix')
    fieldsets = (
        ('Main Details', {
            'fields': (('created', 'modified'),
                       ('prefix', 'external_id', 'location', 'template'),
                       ('url_data', 'serial_numbers_generated', 'item_ids_generated', 'items_issued'),
                       ('batch_number', 'batch_type', 'user_memory'),
                       ('printed_by', 'provider', 'purchase_order', 'production_order'),
                       ('total_tags_used', 'total_tags_void', 'total_tags_programed', 'customer')
                       )
        }),
        ('Audit Details', {
            'classes': ('collapse',),
            'fields': (('created_by', 'modified_by'),)
        }),
    )


class GTINPrintOrderLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'print_order', 'sku', 'initial_serial', 'created', 'modified')
    raw_id_fields = ('print_order', 'sku')


class RFIDActivityAdmin(admin.ModelAdmin):
    list_display = ('id', 'node', 'antenna', 'user_memory', 'epc', 'tid', 'action', 'timestamp', 'created')


admin.site.register(Node, NodeAdmin)
admin.site.register(ReaderBrand, ReaderBrandAdmin)
admin.site.register(Reader, ReaderAdmin)
admin.site.register(BrandTag, BrandTagAdmin)
admin.site.register(TagChipBrand, TagChipBrandAdmin)
admin.site.register(TagReference, TagReferenceAdmin)
admin.site.register(PrintTemplate, PrintTemplateAdmin)
admin.site.register(PrintOrder, PrintOrderAdmin)
admin.site.register(GTINPrintOrderLine, GTINPrintOrderLineAdmin)
admin.site.register(RFIDActivity, RFIDActivityAdmin)

for model_name, model in app.models.items():
    try:
        admin.site.register(model)
    except admin.sites.AlreadyRegistered:
        pass

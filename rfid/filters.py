# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from django.db.models import Q
import django_filters

from common.filters import BaseFilter
from django_filters import rest_framework as filters
from rfid.models import *


class PrintOrderFilter(BaseFilter, filters.FilterSet):
    item_type_name = django_filters.CharFilter(method='do_nothing')
    item_type__name__exclude = filters.CharFilter(method='do_nothing')
    location_source__names = filters.BaseInFilter(field_name="location__name")
    class Meta:
        model = PrintOrder
        fields = ['external_id', 'status',
                  'production_order_id', 'purchase_order_id',
                  'production_order__external_id', 'purchase_order__external_id', 'location_id', 'customer_id',
                  'item_type_name', 'item_type__name__exclude'
                  ]

    @staticmethod
    def do_nothing(queryset, value, *args, **kwargs):
        item_type_name = (args[0])
        if value == "item_type_name":
            results = queryset.filter(Q(purchase_order__item_type__name=item_type_name) |
                                      Q(production_order__item_type__name=item_type_name))

        elif value == "item_type__name__exclude":
            results = queryset.exclude(Q(purchase_order__item_type__name=item_type_name) |
                                      Q(production_order__item_type__name=item_type_name))
        return results


class ReaderFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Reader
        fields = ['mac']


class NodeFilter(BaseFilter, filters.FilterSet):
    class Meta:
        model = Node
        fields = ['mac', 'profile']

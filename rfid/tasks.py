# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import logging

from celery import shared_task

from common.models import User
from core_config.models import UserConfig
from integrations.models import IntegrationConfig
from inventory.models import Location
from rfid.business_logic import print_orders, rfid_activity, rfid_activity_mobile
from .models import PrintOrder, Node
from customers.models import Customer
from rfid_config.models import CustomerMetadata
from django.db import transaction, IntegrityError
from integrations.api_views import send_error
from integrations.business_logic import ups, shopify, siigo
import rfid.utils as rfid_utils
import integrations.utils as integration_utils
import integrations.tasks as integration_tasks


@shared_task(bind=True)
def init_print_order(self, order_id):
    logging.info("Initializing RFID Print Order: {order}".format(order=order_id))
    order_object = PrintOrder.objects.get(pk=order_id)
    customer_metadata = CustomerMetadata.objects.get(customer_id=order_object.customer_id)
    print_orders.generate_item_ids(order_object, customer_metadata.coding_type)


@shared_task(bind=True)
def issue_print_order_items(self, order_id):
    logging.info("Issuig RFID Print Order: {order} items".format(order=order_id))
    order_object = PrintOrder.objects.get(pk=order_id)
    customer_metadata = CustomerMetadata.objects.get(customer_id=order_object.customer_id)
    print_orders.issue_items(order_object, customer_metadata.coding_type)


@shared_task(bind=True)
def create_rfid_activity(self, request, readings: list, node_id: int, timestamp: float, action: str, reporter_id: int,
                         customer_id: int):
    logging.info("Creating RFID activity: {readings}".format(readings=readings))
    reporter_object = User.objects.get(pk=reporter_id)
    customer_object = Customer.objects.get(pk=customer_id)
    node_object = Node.objects.get(mac=node_id)
    item_list = rfid_utils.get_items_available(readings=readings, customer_id=customer_id)
    rfid_activity.process_rfid_activity(readings=readings, node=node_object, timestamp=timestamp, action=action,
                                        reporter=reporter_object, customer=customer_object)
    if action == "SALE":
        move = rfid_activity.create_rfid_move(
            node=node_object, reporter=reporter_object, customer=customer_object, items=item_list,
            timestamp_ms=timestamp, move_type="SALE_OUT", source_reporter='POS_DEVICE')
        module_config = integration_utils.verified_integration_module(
            customer_id=customer_object.id, kong_module__name='SALE_RFID_MOVE_CREATE',
            active_integration=True)
        if module_config:
            request_body = {
                "detail-type": "SALE_RFID_MOVE_CREATE",
                "move_id": move.id
            }
            integration_tasks.integration_module_request(integration_module_id=module_config.id,
                                                         body=request_body)
    elif action == "RETURN":
        rfid_activity.create_rfid_move(node=node_object, reporter=reporter_object, customer=customer_object,
                                       items=item_list, timestamp_ms=timestamp,
                                       move_type="RETURN", source_reporter='FIXED_PORTAL')


@shared_task(bind=True)
def inventory_update_rfid(self, action: str, readings: list, customer_id: int, current_location_id: int):
    customer_object = Customer.objects.get(id=customer_id)
    location_object = Location.objects.get(pk=current_location_id)
    integration_config_object = IntegrationConfig.objects.get(customer_id=customer_id, type__name='SHOPIFY')
    item_list = rfid_utils.get_items_available(readings=readings, customer_id=customer_id)

    not_valid = False
    result_process = []

    if item_list:
        if action == "SALE":
            result_process = shopify.process_inventory(items=item_list, value=False, customer=customer_object,
                                                       integration_config=integration_config_object,
                                                       location=location_object, set_process=False)
        elif action == "RETURN":
            result_process = shopify.process_inventory(items=item_list, value=True, customer=customer_object,
                                                       integration_config=integration_config_object,
                                                       location=location_object, set_process=False)
    for result in result_process:
        if not result["amount"]:
            not_valid = True
    if not_valid:
        logging.error(result_process)
        raise IntegrityError(result_process)


@shared_task(bind=True)
def create_rfid_activity_mobile(self, items, action, location_id, reporter_id):
    logging.info("Creating RFID activity with mobile: {items}".format(items=items))
    location_object = Location.objects.get(pk=location_id)
    reporter_object = User.objects.get(pk=reporter_id)
    customer = UserConfig.objects.get(user_id=reporter_object.pk).customer
    rfid_activity_mobile.process_rfid_activity(items=items, location=location_object, action=action,
                                               reporter=reporter_object, customer=customer)
